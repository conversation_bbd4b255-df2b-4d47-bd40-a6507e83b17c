# WannaCry Ransomware - Kompletní dokumentace a zdroje

⚠️ **VAROVÁNÍ: Tento dokument je určen výhradně pro vzdělávací a výzkumné účely!**

## 📚 Kompletní seznam dokumentace pro WannaCry analýzu

Tento dokument obsahuje více než 15 kvalitních zdrojů pro analýzu původního WannaCry kódu a dumpu.

---

## 🎯 1. TECHNICKÉ ANALÝZY A REVERSE ENGINEERING

### 1.1 Ghidra Reverse Engineering Tutorial ⭐⭐⭐⭐⭐
- **URL:** https://medium.com/@yogeshojha/reverse-engineering-wannacry-ransomware-using-ghidra-finding-the-killswitch-a212807e9354
- **Autor:** Yogesh Ojha
- **Datum:** April 7, 2019
- **Obsah:** Detailní návod na reverse engineering WannaCry pomocí NSA Ghidra
- **Kl<PERSON>čové body:**
  - Kill switch analýza (`www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com`)
  - Disassembly pomocí Ghidra
  - Decompilation process
  - Function call graph analysis
  - InternetOpenA() a InternetOpenUrlA() funkce
- **Technické detaily:**
  - Entry point analysis
  - Function renaming techniques
  - String analysis
  - Control flow understanding

### 1.2 Secureworks Technical Analysis ⭐⭐⭐⭐⭐
- **URL:** https://www.secureworks.com/research/wcry-ransomware-analysis
- **Autor:** Counter Threat Unit Research Team
- **Datum:** May 18, 2017
- **Obsah:** Kompletní technická analýza od Counter Threat Unit
- **Klíčové body:**
  - Delivery mechanismus přes SMB worm
  - EternalBlue exploit (MS17-010)
  - DoublePulsar backdoor detection
  - Infection process details
  - Encryption implementation (RSA-2048 + AES-128)
- **Technické detaily:**
  - SMBv1 vulnerability exploitation
  - Network propagation threads
  - File encryption process
  - C2 communication via Tor
  - Bitcoin payment system

### 1.3 IDA Pro Static Analysis ⭐⭐⭐⭐
- **URL:** https://icact.org/upload/2018/0369/20180369_finalpaper.pdf
- **Název:** "The Static Analysis of WannaCry Ransomware"
- **Obsah:** Analýza pomocí IDA Pro disassembler
- **Klíčové body:**
  - Binary structure analysis
  - Function identification
  - String extraction
  - Control flow graphs
- **Technické detaily:**
  - PE header analysis
  - Import table examination
  - Code section analysis
  - Data section investigation

### 1.4 Cloud AI Malware Analysis ⭐⭐⭐⭐
- **URL:** https://cloud.google.com/blog/topics/threat-intelligence/gemini-for-malware-analysis
- **Název:** "From Assistant to Analyst: The Power of Gemini 1.5 Pro for Malware Analysis"
- **Datum:** April 29, 2024
- **Obsah:** AI-powered malware analysis techniques
- **Klíčové body:**
  - Automated disassembly analysis
  - Large-scale code understanding
  - Pattern recognition in malware

---

## 🎯 2. AKADEMICKÉ VÝZKUMY A STUDIE

### 2.1 ResearchGate - Comprehensive Analysis ⭐⭐⭐⭐⭐
- **URL:** https://www.researchgate.net/publication/332088162_WannaCry_Ransomware_Analysis_of_Infection_Persistence_Recovery_Prevention_and_Propagation_Mechanisms
- **Název:** "WannaCry Ransomware: Analysis of Infection, Persistence, Recovery Prevention and Propagation Mechanisms"
- **Datum:** April 2019
- **Obsah:** Peer-reviewed akademická studie
- **Klíčové body:**
  - Infection mechanisms
  - Persistence techniques
  - Recovery prevention methods
  - Network propagation analysis
- **Technické detaily:**
  - Registry modifications
  - Service installation
  - File system changes
  - Network scanning algorithms

### 2.2 IEEE Paper - Behavioral Analysis ⭐⭐⭐⭐
- **URL:** https://arxiv.org/pdf/1709.08753
- **Název:** "Automated Behavioral Analysis of Malware: A Case Study of WannaCry Ransomware"
- **Datum:** September 25, 2017
- **Obsah:** Dynamic analysis using Cuckoo Sandbox
- **Klíčové body:**
  - Behavioral pattern analysis
  - Cuckoo Sandbox results
  - API call monitoring
  - Network traffic analysis
- **Technické detaily:**
  - System call tracing
  - File system monitoring
  - Network activity logging
  - Process creation tracking

### 2.3 GMU Technical Report ⭐⭐⭐⭐
- **URL:** https://people-ece.vse.gmu.edu/coursewebpages/ECE/ECE646/F20/project/F18_presentations/Session_III/Session_III_Report_3.pdf
- **Název:** "A Comprehensive Analysis of WannaCry: Technical Analysis"
- **Obsah:** Univerzitní technická zpráva
- **Klíčové body:**
  - Encryption algorithm analysis
  - Network propagation mechanisms
  - Vulnerability exploitation
- **Technické detaily:**
  - Cryptographic implementation
  - SMB protocol exploitation
  - Worm propagation logic

---

## 🎯 3. VLÁDNÍ A CERT DOKUMENTY

### 3.1 US-CERT/CISA Alert ⭐⭐⭐⭐⭐
- **URL:** https://www.cisa.gov/news-events/alerts/2017/05/12/indicators-associated-wannacry-ransomware
- **Název:** "Indicators Associated With WannaCry Ransomware"
- **Datum:** June 7, 2018 (updated)
- **Obsah:** Oficiální US-CERT analýza a IOCs
- **Klíčové body:**
  - Technical indicators of compromise
  - Mitigation strategies
  - Detection signatures
  - Attribution information
- **IOCs poskytnuté:**
  - File hashes (MD5, SHA1, SHA256)
  - Network indicators
  - Registry keys
  - Mutex names

### 3.2 FBI FLASH Report ⭐⭐⭐⭐
- **URL:** https://cdpsdocs.state.co.us/safeschools/Resources/cybersecurity/FBI_FLASH_WannaCry_FINAL_05132017.pdf
- **Název:** FBI Flash Alert MC-000081-MW
- **Datum:** May 13, 2017
- **Obsah:** FBI technická analýza a varování
- **Klíčové body:**
  - Technical analysis summary
  - Attribution assessment
  - Threat actor profiling
- **Technické detaily:**
  - Malware family classification
  - Attack vector analysis
  - Impact assessment

### 3.3 CCN-CERT Malware Report ⭐⭐⭐⭐
- **URL:** https://www.ccn-cert.cni.es/es/informes/informes-ccn-cert-publicos/2172-ccn-cert-id-17-17-ransom-wannacrypt0r-eng/file.html
- **Název:** "Ransom.WannaCry" Malware Report
- **Datum:** May 15, 2017
- **Obsah:** Španělský CERT analýza
- **Klíčové body:**
  - European perspective on attack
  - Technical analysis details
  - Mitigation recommendations

---

## 🎯 4. GITHUB REPOSITORIES A SOURCE CODE

### 4.1 WannaCry Analysis Repository ⭐⭐⭐⭐
- **URL:** https://github.com/ManojPatil99/WannaCry-Ransomware-Malware-Analysis
- **Autor:** ManojPatil99
- **Obsah:** Complete malware analysis project
- **Klíčové body:**
  - Static analysis techniques
  - Dynamic analysis methods
  - Reverse engineering process
  - Documentation and reports
- **Obsahuje:**
  - Analysis scripts
  - Documentation files
  - Sample analysis results
  - Methodology explanations

### 4.2 PMAT Final Report ⭐⭐⭐⭐
- **URL:** https://github.com/rishank-shah/PMAT-Final-Report
- **Autor:** rishank-shah
- **Obsah:** Practical Malware Analysis and Triage report
- **Klíčové body:**
  - Comprehensive analysis methodology
  - Step-by-step analysis process
  - Tools and techniques used
- **Obsahuje:**
  - Detailed analysis report
  - Screenshots and evidence
  - Methodology documentation

---

## 🎯 5. ZDRAVOTNICTVÍ A DOPAD

### 5.1 NHS Lessons Learned Review ⭐⭐⭐⭐
- **URL:** https://www.england.nhs.uk/wp-content/uploads/2018/02/lessons-learned-review-wannacry-ransomware-cyber-attack-cio-review.pdf
- **Název:** "Lessons learned review of the WannaCry Ransomware Cyber Attack"
- **Datum:** February 1, 2018
- **Obsah:** Oficiální NHS review útoku
- **Klíčové body:**
  - Impact assessment na zdravotnictví
  - Organizational response analysis
  - Lessons learned and recommendations
- **Statistiky:**
  - Affected NHS trusts
  - Financial impact
  - Service disruption details

### 5.2 National Audit Office Investigation ⭐⭐⭐⭐
- **URL:** https://www.nao.org.uk/wp-content/uploads/2017/10/Investigation-WannaCry-cyber-attack-and-the-NHS.pdf
- **Název:** "Investigation: WannaCry cyber attack and the NHS"
- **Datum:** April 25, 2018
- **Obsah:** Nezávislé vyšetřování dopadu
- **Klíčové body:**
  - Financial impact analysis
  - System vulnerability assessment
  - Response effectiveness evaluation
- **Zjištění:**
  - Cost of attack
  - Recovery timeline
  - Preventive measures needed

---

## 🎯 6. KOMERČNÍ ANALÝZY

### 6.1 Antiy CERT Deep Analysis ⭐⭐⭐⭐
- **URL:** https://www.antiy.net/p/in-depth-analysis-report-on-wannacry-ransomware/
- **Název:** "IN-DEPTH ANALYSIS REPORT ON WANNACRY RANSOMWARE"
- **Datum:** July 3, 2017
- **Obsah:** Čínská bezpečnostní firma analýza
- **Klíčové body:**
  - Asian perspective on attack
  - Technical deep-dive analysis
  - Attribution assessment
- **Technické detaily:**
  - Code analysis
  - Network behavior
  - Encryption mechanisms

### 6.2 Malware Analysis Blog ⭐⭐⭐
- **URL:** https://www.purpl3f0xsecur1ty.tech/2021/02/17/wannacry.html
- **Název:** "Malware Analysis - Wannacry"
- **Datum:** February 17, 2021
- **Obsah:** Step-by-step analysis process
- **Klíčové body:**
  - Practical analysis approach
  - Tool usage examples
  - Beginner-friendly explanations

---

## 🎯 7. SPECIALIZOVANÉ NÁSTROJE A TECHNIKY

### 7.1 MalwareTech Programming Guide ⭐⭐⭐
- **URL:** https://malwaretech.com/2018/03/best-programming-languages-to-learn-for-malware-analysis.html
- **Název:** "Best Languages to Learn for Malware Analysis"
- **Datum:** March 19, 2018
- **Obsah:** Programming skills pro reverse engineering
- **Klíčové body:**
  - Essential programming languages
  - Tool recommendations
  - Learning resources

### 7.2 Additional Technical Resources ⭐⭐⭐
- **Ghidra Documentation:** https://github.com/NationalSecurityAgency/ghidra
- **IDA Pro Resources:** Various technical blogs and tutorials
- **Cuckoo Sandbox:** Dynamic analysis platform documentation

---

## 📊 SHRNUTÍ DOKUMENTACE

| Kategorie | Počet zdrojů | Průměrná kvalita | Obsah |
|-----------|--------------|------------------|-------|
| **Technické analýzy** | 4 | ⭐⭐⭐⭐⭐ | Reverse engineering, disassembly |
| **Akademické studie** | 3 | ⭐⭐⭐⭐⭐ | Peer-reviewed research |
| **Vládní dokumenty** | 3 | ⭐⭐⭐⭐⭐ | Official CERT reports |
| **GitHub repositories** | 2 | ⭐⭐⭐⭐ | Source code analysis |
| **Impact studies** | 2 | ⭐⭐⭐⭐ | Real-world consequences |
| **Komerční analýzy** | 2 | ⭐⭐⭐⭐ | Industry perspectives |
| **Nástroje a techniky** | 2 | ⭐⭐⭐ | Analysis methodologies |

**Celkem: 18+ kvalitních zdrojů**

---

## 🎯 DOPORUČENÉ POŘADÍ STUDIA

1. **Začněte s:** Secureworks Technical Analysis (kompletní přehled)
2. **Pokračujte:** Ghidra Reverse Engineering Tutorial (praktické skills)
3. **Prohloubte:** ResearchGate Academic Paper (teoretické pozadí)
4. **Rozšiřte:** US-CERT/CISA Alert (oficiální IOCs)
5. **Dokončete:** GitHub repositories (hands-on practice)

---

## 💡 KLÍČOVÉ POZNATKY Z DOKUMENTACE

### Kill Switch Mechanismus
- **Domain:** `www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com`
- **Funkce:** Pokud je domain dostupný, malware se ukončí
- **Objevitel:** Marcus Hutchins (MalwareTech)

### EternalBlue Exploit
- **Vulnerability:** MS17-010 (SMBv1)
- **Origin:** NSA tools leaked by Shadow Brokers
- **Target:** Windows SMB service
- **Companion:** DoublePulsar backdoor

### Encryption Scheme
- **Asymmetric:** RSA-2048 (file keys)
- **Symmetric:** AES-128 (file content)
- **Key Management:** Unique per-file AES keys
- **Master Key:** RSA public key embedded in malware

### Propagation Method
- **Primary:** SMB worm component
- **Secondary:** Email spreading (limited)
- **Network Scanning:** Local subnet + random IPs
- **Exploitation:** EternalBlue + DoublePulsar

### Bitcoin Addresses
```
**********************************
**********************************
********************************** (hard-coded)
```

### File Extensions (176+ targeted)
```
.doc, .docx, .xls, .xlsx, .ppt, .pptx, .pdf, .txt,
.jpg, .jpeg, .png, .gif, .bmp, .mp3, .mp4, .avi,
.zip, .rar, .7z, .sql, .db, .psd, .dwg, ...
```

### C2 Infrastructure
```
gx7ekbenv2riucmf.onion
57g7spgrzlojinas.onion
xxlvbrloxvriy2c5.onion
76jdd2ir2embyv47.onion
cwwnhwhlz52maqm7.onion
```

---

**Tato dokumentace poskytuje kompletní základ pro pochopení a analýzu původního WannaCry ransomwaru!** 📚🔍
