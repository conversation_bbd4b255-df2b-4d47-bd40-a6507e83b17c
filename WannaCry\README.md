# WannaCry3.0
Improvement of 'WannaCry' Virus project

## WannaCry Infections

* NHS (uk) turning away patients, unable to perform x-rays.
* Nissan (uk)
* Telefonica (spain)
* Power firm Iberdrola and Gas Natural (spain)
* FedEx (us)
* University of Waterloo (ontario canada)
* Russia interior ministry & Megafon (russia)
* VTB (russian bank)
* Russian Railroads (RZD)
* Portugal Telecom
* Сбербанк - Sberbank Russia (russia)
* Shaheen Airlines (pakistan)
* Train station in frankfurt (germany)
* Neustadt station (germany)
* The entire network of German Rail seems to be affected
* In China secondary schools and universities had been affected (china)
* A Library in Oman (UAE)
* China Yanshui County Public Security Bureau (china)
* Renault (france)
* Schools/Education (france)
* University of Milano-Bicocca (italy) 
* A mall in singapore (singapore)
* ATMs in china (china)
* Norwegian soccer team ticket sales (norway)
* STC telecom (saudia arabia)
* All ATMs in india closed (india)
* Hitachi, The conglomerate reported problems in email and other systems (japan)
* Academic institutions and offices (taiwan)
* Speed cameras reportedly, traffic and speed cameras (australia)
* Movie theaters systems used for display and ticketing were affected (korea)
* Universities in several higher education institutions (italy)
* Social Security systems of the social security administration (brazil)
* Government and private sectors (mexico)
* Widespread Concern (indonesia)
* Harapan Kita & Dharmais Hospital, no service access to medical records. (indonesia)
* Government of Communication and Information Technology (indonesia)
* and more------------------------------⬇️

<details>
<summary><h3> 🌐 Global Infection Map </h3></summary>

![wannacry1](https://github.com/Sulaimannabdul/WannaCry3.0/assets/*********/cf3ee53c-57f7-4d94-8650-240726bbeef3)<br>
![wannacry2](https://github.com/Sulaimannabdul/WannaCry3.0/assets/*********/014c7c2e-cd6a-41ca-b338-c5f3e9fa7ac4)<br>
![wannacry3](https://github.com/Sulaimannabdul/WannaCry3.0/assets/*********/0beb39b9-dba5-4021-a725-2dd6fa232d46)<br>

</details>

## Language C

## File types

There are a number of files and folders wannacrypt will avoid. Some because it's entirely pointless and others because it might destabilize the system. During scans, it will search the path for the following strings and skip over if present:

*   "Content.IE5"
*   "Temporary Internet Files"
*   " This folder protects against ransomware. Modifying it will reduce protection"
*   "\Local Settings\Temp"
*   "\AppData\Local\Temp"
*   "\Program Files (x86)"
*   "\Program Files (x64)"
*   "\Program Files"
*   "\WINDOWS"
*   "\ProgramData"
*   "\Intel"
*   "$\"

The filetypes it looks for to encrypt are:

.doc, .docx, .xls, .xlsx, .ppt, .pptx, .pst, .ost, .msg, .eml, .vsd, .vsdx, .txt, .csv, .rtf, .123, .wks, .wk1, .pdf, .dwg, .onetoc2, .snt, .jpeg, .jpg, .docb, .docm, .dot, .dotm, .dotx, .xlsm, .xlsb, .xlw, .xlt, .xlm, .xlc, .xltx, .xltm, .pptm, .pot, .pps, .ppsm, .ppsx, .ppam, .potx, .potm, .edb, .hwp, .602, .sxi, .sti, .sldx, .sldm, .sldm, .vdi, .vmdk, .vmx, .gpg, .aes, .ARC, .PAQ, .bz2, .tbk, .bak, .tar, .tgz, .gz, .7z, .rar, .zip, .backup, .iso, .vcd, .bmp, .png, .gif, .raw, .cgm, .tif, .tiff, .nef, .psd, .ai, .svg, .djvu, .m4u, .m3u, .mid, .wma, .flv, .3g2, .mkv, .3gp, .mp4, .mov, .avi, .asf, .mpeg, .vob, .mpg, .wmv, .fla, .swf, .wav, .mp3, .sh, .class, .jar, .java, .rb, .asp, .php, .jsp, .brd, .sch, .dch, .dip, .pl, .vb, .vbs, .ps1, .bat, .cmd, .js, .asm, .h, .pas, .cpp, .c, .cs, .suo, .sln, .ldf, .mdf, .ibd, .myi, .myd, .frm, .odb, .dbf, .db, .mdb, .accdb, .sql, .sqlitedb, .sqlite3, .asc, .lay6, .lay, .mml, .sxm, .otg, .odg, .uop, .std, .sxd, .otp, .odp, .wb2, .slk, .dif, .stc, .sxc, .ots, .ods, .3dm, .max, .3ds, .uot, .stw, .sxw, .ott, .odt, .pem, .p12, .csr, .crt, .key, .pfx, .der

### credit herulume and others, thanks for extracting and else from the binary. 

#### ***Note:*** I will not take any responsibility to any attack or actions that use this ***malware***⛔.
