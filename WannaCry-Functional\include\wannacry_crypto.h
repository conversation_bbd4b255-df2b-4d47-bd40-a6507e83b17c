/*
 * WannaCry Cryptographic Functions
 * 
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * 
 * This header contains all cryptographic functions and utilities
 * extracted from the original WannaCry dump.
 */

#ifndef WANNACRY_CRYPTO_H
#define WANNACRY_CRYPTO_H

#include "wannacry_types.h"
#include <string>
#include <vector>

// ======================== CRYPTOGRAPHIC FUNCTIONS ========================

class WannaCryCrypto {
public:
    // Constructor/Destructor
    WannaCryCrypto();
    ~WannaCryCrypto();

    // Initialization
    bool Initialize();
    void Cleanup();

    // Key generation
    bool GenerateEncryptionKey(std::string& key, size_t keySize = 32);
    bool GenerateRandomBytes(BYTE* buffer, DWORD size);

    // File encryption/decryption
    bool EncryptFile(const std::string& filePath, const std::string& key);
    bool DecryptFile(const std::string& filePath, const std::string& key);
    
    // Data encryption/decryption
    bool EncryptData(const BYTE* input, DWORD inputSize, BYTE* output, DWORD& outputSize, const std::string& key);
    bool DecryptData(const BYTE* input, DWORD inputSize, BYTE* output, DWORD& outputSize, const std::string& key);

    // XOR encryption (simplified for educational purposes)
    void XOREncrypt(BYTE* data, DWORD size, const BYTE* key, DWORD keySize);
    void XORDecrypt(BYTE* data, DWORD size, const BYTE* key, DWORD keySize);

    // Advanced encryption (AES simulation)
    bool AESEncrypt(const BYTE* input, DWORD inputSize, BYTE* output, DWORD& outputSize, const BYTE* key, DWORD keySize);
    bool AESDecrypt(const BYTE* input, DWORD inputSize, BYTE* output, DWORD& outputSize, const BYTE* key, DWORD keySize);

    // RSA operations (simulation)
    bool GenerateRSAKeyPair(std::string& publicKey, std::string& privateKey);
    bool RSAEncrypt(const BYTE* input, DWORD inputSize, BYTE* output, DWORD& outputSize, const std::string& publicKey);
    bool RSADecrypt(const BYTE* input, DWORD inputSize, BYTE* output, DWORD& outputSize, const std::string& privateKey);

private:
    HCRYPTPROV m_hCryptProv;
    bool m_bInitialized;
};

// ======================== CRC32 FUNCTIONS ========================

// CRC32 calculation from original dump
DWORD CalculateCRC32(const BYTE* data, DWORD length);
DWORD CalculateFileCRC32(HANDLE hFile);
DWORD CalculateStringCRC32(const char* str);

// CRC32 with custom polynomial
DWORD CalculateCRC32Custom(const BYTE* data, DWORD length, DWORD polynomial);

// ======================== HASH FUNCTIONS ========================

// MD5 hash calculation
bool CalculateMD5(const BYTE* data, DWORD length, BYTE hash[16]);
bool CalculateFileMD5(const std::string& filePath, BYTE hash[16]);

// SHA1 hash calculation
bool CalculateSHA1(const BYTE* data, DWORD length, BYTE hash[20]);
bool CalculateFileSHA1(const std::string& filePath, BYTE hash[20]);

// SHA256 hash calculation
bool CalculateSHA256(const BYTE* data, DWORD length, BYTE hash[32]);
bool CalculateFileSHA256(const std::string& filePath, BYTE hash[32]);

// ======================== STRING OBFUSCATION ========================

// ROT13 encoding/decoding from original dump
void ROT13(char* str);
void ROT13(std::string& str);
std::string ROT13String(const std::string& input);

// XOR string obfuscation
void XORString(char* str, BYTE key);
void XORString(std::string& str, BYTE key);
std::string XORStringCopy(const std::string& input, BYTE key);

// Base64 encoding/decoding
std::string Base64Encode(const BYTE* data, DWORD length);
std::vector<BYTE> Base64Decode(const std::string& encoded);

// Custom encoding schemes
std::string CustomEncode(const std::string& input, int scheme = 1);
std::string CustomDecode(const std::string& encoded, int scheme = 1);

// ======================== RANDOM NUMBER GENERATION ========================

// Secure random number generation
bool GenerateSecureRandom(BYTE* buffer, DWORD size);
DWORD GenerateSecureRandomDWORD();
WORD GenerateSecureRandomWORD();
BYTE GenerateSecureRandomBYTE();

// Pseudo-random with seed
void InitializePseudoRandom(DWORD seed);
DWORD GeneratePseudoRandomDWORD();

// ======================== KEY DERIVATION ========================

// PBKDF2 key derivation
bool DeriveKeyPBKDF2(const char* password, const BYTE* salt, DWORD saltSize, 
                     DWORD iterations, BYTE* derivedKey, DWORD keySize);

// Simple key derivation
void DeriveSimpleKey(const char* password, BYTE* key, DWORD keySize);

// ======================== UTILITY FUNCTIONS ========================

// Convert binary to hex string
std::string BinaryToHex(const BYTE* data, DWORD length);
std::vector<BYTE> HexToBinary(const std::string& hex);

// Secure memory operations
void SecureZeroMemory(void* ptr, size_t size);
bool SecureCompareMemory(const void* ptr1, const void* ptr2, size_t size);

// File integrity
bool VerifyFileIntegrity(const std::string& filePath, const BYTE* expectedHash, DWORD hashSize);
bool CreateFileChecksum(const std::string& filePath, std::string& checksum);

// ======================== ENCRYPTION ALGORITHMS ========================

// Simple substitution cipher
void SubstitutionCipher(BYTE* data, DWORD size, const BYTE* substitutionTable);

// Vigenère cipher
void VigenereCipher(char* text, const char* key, bool encrypt = true);

// Stream cipher simulation
void StreamCipher(BYTE* data, DWORD size, const BYTE* key, DWORD keySize);

// Block cipher simulation
void BlockCipher(BYTE* data, DWORD size, const BYTE* key, DWORD keySize, bool encrypt = true);

// ======================== CONSTANTS ========================

// Encryption constants
#define ENCRYPTION_KEY_SIZE 32
#define ENCRYPTION_IV_SIZE 16
#define ENCRYPTION_BLOCK_SIZE 16

// Hash sizes
#define MD5_HASH_SIZE 16
#define SHA1_HASH_SIZE 20
#define SHA256_HASH_SIZE 32

// CRC32 polynomial
#define CRC32_POLYNOMIAL 0xEDB88320L

#endif // WANNACRY_CRYPTO_H
