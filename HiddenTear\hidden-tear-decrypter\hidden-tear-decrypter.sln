﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.31101.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "hidden-tear-decrypter", "hidden-tear-decrypter\hidden-tear-decrypter.csproj", "{82C19CBA-E318-4BB3-A408-5005EA083EC5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{82C19CBA-E318-4BB3-A408-5005EA083EC5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82C19CBA-E318-4BB3-A408-5005EA083EC5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82C19CBA-E318-4BB3-A408-5005EA083EC5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82C19CBA-E318-4BB3-A408-5005EA083EC5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
