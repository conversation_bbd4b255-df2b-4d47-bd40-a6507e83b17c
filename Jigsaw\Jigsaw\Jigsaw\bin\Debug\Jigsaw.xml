﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
Jigsaw
</name>
</assembly>
<members>
<member name="P:Jigsaw.My.Resources.Resources.ResourceManager">
	<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member><member name="P:Jigsaw.My.Resources.Resources.Culture">
	<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member><member name="P:Jigsaw.My.Resources.Resources.Jigsaw">
	<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member><member name="T:Jigsaw.My.Resources.Resources">
	<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
</members>
</doc>