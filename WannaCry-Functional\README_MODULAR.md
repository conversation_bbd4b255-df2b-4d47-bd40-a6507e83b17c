# WannaCry Ransomware - Complete Modular Implementation

⚠️ **WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!**  
**DO NOT use this code for malicious purposes or on production systems!**

## 📋 Overview

This is a comprehensive, modular implementation of the WannaCry ransomware based on the complete original dump (588KB). The code has been properly structured into separate C++ modules for better understanding, analysis, and educational purposes.

## 🏗️ Modular Structure

### 📁 Directory Structure
```
WannaCry-Functional/
├── include/                    # Header files
│   ├── wannacry_types.h       # All structures and type definitions
│   ├── wannacry_globals.h     # Global variables and constants
│   ├── wannacry_crypto.h      # Cryptographic functions and CRC32
│   ├── wannacry_network.h     # Network operations and scanning
│   ├── wannacry_utils.h       # Email, HTML, ZIP utilities
│   ├── wannacry_service.h     # Windows service management
│   └── wannacry_core.h        # Main ransomware logic
├── src/                       # Implementation files
│   ├── main.cpp               # Entry point and CLI
│   ├── wannacry_globals.cpp   # Global variables implementation
│   ├── wannacry_crypto.cpp    # Cryptographic functions
│   ├── wannacry_network.cpp   # Network operations (to be created)
│   ├── wannacry_utils.cpp     # Utility functions (to be created)
│   ├── wannacry_service.cpp   # Service management (to be created)
│   └── wannacry_core.cpp      # Core logic (to be created)
├── build/                     # Build artifacts
├── bin/                       # Compiled executables
├── Makefile                   # Build system
├── WannaCry_Functional.cpp    # Legacy monolithic version
└── README_MODULAR.md          # This file
```

## 🔧 Build System

### Available Targets

| Target | Description |
|--------|-------------|
| `make all` | Build modular WannaCry executable (default) |
| `make wannacry-core` | Build core WannaCry executable (minimal) |
| `make legacy` | Build legacy monolithic version |
| `make debug` | Build debug version |
| `make release` | Build optimized release version |
| `make clean` | Remove all build files |
| `make rebuild` | Clean and rebuild |
| `make test` | Run in test mode |
| `make help` | Show help message |
| `make info` | Show build information |

### Quick Start

```bash
# Build the minimal modular version (currently available)
make wannacry-core

# Build the legacy monolithic version
make legacy

# Run in safe test mode
make test

# Clean build files
make clean
```

## 📦 Module Descriptions

### 🔧 Core Modules (Available)

#### 1. **wannacry_types.h**
- All structure definitions from original dump
- Network, service, email, ZIP structures
- Constants and type definitions
- **Size**: Complete type system

#### 2. **wannacry_globals.h/.cpp**
- Global variables and state management
- CRC32 lookup table (complete 256-entry table)
- Obfuscated strings (ROT13 encoded)
- Statistics and configuration management
- **Size**: ~300 lines of implementation

#### 3. **wannacry_crypto.h/.cpp**
- Cryptographic functions using Windows Crypto API
- CRC32 calculations and file integrity
- String obfuscation (ROT13, XOR, Base64)
- Secure random number generation
- **Size**: ~300 lines of implementation

#### 4. **main.cpp**
- Command-line interface and argument parsing
- Service mode detection and handling
- Safety checks and test mode enforcement
- **Size**: ~300 lines of implementation

### 🚧 Modules To Be Created

#### 5. **wannacry_network.h/.cpp** (Planned)
- Complete network scanning and propagation
- SMB/NetBIOS client implementation
- EternalBlue exploit simulation
- Email harvesting from network sources
- HTTP client for kill switch checks

#### 6. **wannacry_utils.h/.cpp** (Planned)
- File operations and ZIP handling
- HTML parsing and email extraction
- Registry manipulation utilities
- Process and system utilities

#### 7. **wannacry_service.h/.cpp** (Planned)
- Windows service installation and management
- Persistence mechanisms (registry, startup, scheduled tasks)
- Privilege escalation techniques
- Anti-analysis and evasion methods

#### 8. **wannacry_core.h/.cpp** (Planned)
- Main WannaCry ransomware class
- File encryption orchestration
- Ransom note creation and display
- System modification and cleanup

## 🎯 Features Extracted from Original Dump

### ✅ Currently Implemented
- **Complete type system** from original 588KB dump
- **CRC32 calculations** with original lookup table
- **Cryptographic functions** using Windows Crypto API
- **Global state management** with thread safety
- **String obfuscation** (ROT13, XOR, Base64)
- **Command-line interface** with safety checks
- **Modular build system** with multiple targets

### 🚧 To Be Implemented
- **Network scanning and propagation** (from original dump)
- **Email harvesting and mass mailing** (from original dump)
- **ZIP file creation and manipulation** (from original dump)
- **HTML parsing and web scraping** (from original dump)
- **Windows service management** (from original dump)
- **Registry manipulation and persistence** (from original dump)
- **Anti-analysis and evasion techniques** (from original dump)
- **File encryption with multiple algorithms** (from original dump)

## 🔒 Safety Features

### Built-in Safety Mechanisms
- **Test mode enforcement** for console execution
- **Limited scope** in test mode
- **User confirmation** required for dangerous operations
- **Excluded system directories** protection
- **Kill switch** domain checking
- **Mutex-based** single instance protection

### Educational Safeguards
- **Clear warnings** in all source files
- **Educational purpose** statements
- **Safe defaults** in all configurations
- **Verbose logging** for analysis
- **Reversible operations** where possible

## 📊 Comparison with Original

| Aspect | Original Dump | Current Implementation | Status |
|--------|---------------|----------------------|---------|
| **File Size** | 588,650 bytes | ~50,000 bytes | 🚧 Expanding |
| **Structures** | Complete | Complete | ✅ Done |
| **CRC32 Table** | 256 entries | 256 entries | ✅ Done |
| **Crypto Functions** | Full Windows API | Full Windows API | ✅ Done |
| **Network Code** | Complete | Planned | 🚧 To Do |
| **Email Functions** | Complete | Planned | 🚧 To Do |
| **Service Code** | Complete | Planned | 🚧 To Do |
| **ZIP Utilities** | Complete | Planned | 🚧 To Do |
| **HTML Parser** | Complete | Planned | 🚧 To Do |

## 🚀 Usage Examples

### Safe Testing
```bash
# Build and run in test mode
make wannacry-core
./bin/WannaCry-Core.exe --test --verbose

# Show help
./bin/WannaCry-Core.exe --help

# Show version information
./bin/WannaCry-Core.exe --version
```

### Development
```bash
# Build debug version
make debug

# Show build information
make info

# Clean and rebuild
make rebuild
```

## 🔍 Analysis Features

### Code Organization
- **Modular design** for easy analysis
- **Clear separation** of concerns
- **Comprehensive documentation** in headers
- **Educational comments** throughout code

### Research Benefits
- **Complete type definitions** for reverse engineering
- **Original algorithms** preserved
- **Modular testing** capabilities
- **Safe experimentation** environment

## ⚠️ Important Notes

### Legal and Ethical
- **Educational use only** - never use for malicious purposes
- **Research purposes** - for understanding malware techniques
- **Cybersecurity training** - for defensive purposes
- **Academic study** - for computer science education

### Technical
- **Windows-specific** implementation
- **Requires administrator** privileges for some features
- **Network isolation** recommended for testing
- **Virtual machine** environment suggested

### Safety
- **Always use test mode** for experimentation
- **Never run on production** systems
- **Keep backups** before any testing
- **Monitor system changes** during testing

## 📚 Next Steps

1. **Complete network module** implementation
2. **Add utility functions** from original dump
3. **Implement service management** features
4. **Create comprehensive test suite**
5. **Add more safety mechanisms**
6. **Improve documentation** and examples

---

**Remember: This code is for educational and research purposes only. Use responsibly and ethically!**
