# WannaCry Ransomware - Functional Implementation

## ⚠️ CRITICAL WARNING ⚠️

**This code is for EDUCATIONAL and RESEARCH purposes ONLY!**

- **DO NOT** use this code for malicious purposes
- **DO NOT** run this on production systems
- **DO NOT** run this without proper authorization
- The author is **NOT RESPONSIBLE** for any damage caused by this code

This is a simplified, functional implementation of WannaCry ransomware created for:
- Understanding how ransomware works
- Developing countermeasures and detection methods
- Educational purposes in cybersecurity training
- Research into malware behavior

## Overview

This implementation recreates the core functionality of the WannaCry ransomware that caused global disruption in 2017. It includes:

### Core Features
- **Kill Switch Mechanism**: Checks for specific domains before execution
- **File Encryption**: Encrypts files with specific extensions using XOR (simplified)
- **Network Propagation**: Simulates network scanning and lateral movement
- **Ransom Notes**: Creates ransom demand files
- **Shadow Copy Deletion**: Removes Windows shadow copies
- **Persistence**: Installs registry and startup persistence
- **Anti-Analysis**: Detects debuggers and virtual machines

### Technical Implementation
- **Language**: C++17
- **Platform**: Windows
- **Encryption**: XOR (educational - real WannaCry used AES)
- **Network**: Winsock2 for network operations
- **Persistence**: Registry and startup folder methods

## File Structure

```
WannaCry-Functional/
├── WannaCry_Functional.cpp    # Main source code
├── Makefile                   # Build configuration
├── README.md                  # This documentation
└── (generated files)
    ├── WannaCry_Functional.exe    # Compiled executable
    └── test files...              # Test mode files
```

## Compilation

### Prerequisites
- Windows operating system
- MinGW-w64 or Visual Studio with C++17 support
- Make utility (optional)

### Using Makefile
```bash
# Build the executable
make

# Test compilation only
make test

# Clean build artifacts
make clean

# Show help
make help
```

### Manual Compilation
```bash
g++ -std=c++17 -Wall -Wextra -O2 -o WannaCry_Functional.exe WannaCry_Functional.cpp -lws2_32 -lcrypt32 -ladvapi32 -lshell32
```

## Usage

### Command Line Options

```bash
# Show help
WannaCry_Functional.exe --help

# Run in test mode (safe, limited scope)
WannaCry_Functional.exe --test

# Run encryption simulation (DANGEROUS!)
WannaCry_Functional.exe --encrypt

# Run decryption with key
WannaCry_Functional.exe --decrypt <encryption_key>
```

### Test Mode (Recommended)

Test mode is the safest way to observe the ransomware behavior:

```bash
WannaCry_Functional.exe --test
```

This will:
1. Create a test directory: `C:\Users\<USER>\WannaCry_Test`
2. Generate test files with various extensions
3. Encrypt only these test files
4. Create ransom notes
5. Display the encryption process

## Key Components

### 1. Kill Switch Mechanism
```cpp
bool checkKillSwitch() {
    // Checks if kill switch domains resolve
    // If they do, the ransomware terminates
}
```

### 2. File Encryption
```cpp
bool encryptFile(const std::string& filePath) {
    // Reads file, encrypts with XOR, saves with .WNCRY extension
    // Deletes original file
}
```

### 3. Network Propagation
```cpp
void attemptNetworkPropagation() {
    // Simulates scanning for vulnerable SMB services
    // Real WannaCry used EternalBlue exploit
}
```

### 4. Anti-Analysis
```cpp
void antiAnalysis() {
    // Detects debuggers, virtual machines
    // Sleeps to evade sandbox analysis
}
```

## Target File Extensions

The ransomware targets files with these extensions:
- Documents: `.doc`, `.docx`, `.pdf`, `.txt`, `.rtf`
- Images: `.jpg`, `.png`, `.gif`, `.bmp`, `.tiff`
- Media: `.mp3`, `.mp4`, `.avi`, `.mov`, `.wav`
- Archives: `.zip`, `.rar`, `.7z`, `.tar`
- Databases: `.sql`, `.mdb`, `.sqlite`, `.db`
- Code: `.cpp`, `.c`, `.java`, `.py`, `.php`

## Excluded Directories

These directories are skipped to maintain system stability:
- `Windows`
- `Program Files`
- `Program Files (x86)`
- `ProgramData`
- `$Recycle.Bin`
- `System Volume Information`
- `Recovery`

## Security Considerations

### For Researchers
1. **Isolated Environment**: Only run in isolated VMs
2. **Network Isolation**: Disconnect from networks
3. **Backup**: Always have clean snapshots
4. **Monitoring**: Use process and network monitoring tools

### For Defenders
This implementation helps understand:
1. **Detection Points**: Where to monitor for ransomware activity
2. **Prevention**: How to block ransomware techniques
3. **Recovery**: Importance of backups and shadow copies
4. **Network Security**: Importance of patching and segmentation

## Educational Value

### Understanding Ransomware
- File encryption techniques
- Persistence mechanisms
- Anti-analysis methods
- Network propagation
- Kill switch concepts

### Developing Countermeasures
- Behavioral detection signatures
- Network monitoring rules
- File system protection
- Backup strategies
- Incident response procedures

## Legal and Ethical Considerations

### Legal Compliance
- Only use in authorized environments
- Comply with local laws and regulations
- Obtain proper permissions before testing
- Document all testing activities

### Ethical Guidelines
- Never use for malicious purposes
- Respect others' systems and data
- Share knowledge responsibly
- Report vulnerabilities appropriately

## Differences from Real WannaCry

This implementation differs from the original WannaCry in several ways:

### Simplified Features
- **Encryption**: Uses XOR instead of AES for simplicity
- **Key Management**: Simplified key generation and storage
- **Network Propagation**: Simulated instead of actual exploitation
- **Payload**: Educational warnings instead of actual ransom demands

### Safety Measures
- **Test Mode**: Safe testing environment
- **User Confirmation**: Requires explicit confirmation
- **Limited Scope**: Excludes critical system directories
- **Educational Focus**: Emphasizes learning over functionality

## Conclusion

This functional implementation of WannaCry serves as an educational tool for understanding ransomware mechanics and developing effective countermeasures. It demonstrates the importance of:

1. **Regular Backups**: The primary defense against ransomware
2. **System Patching**: Preventing exploitation of vulnerabilities
3. **Network Segmentation**: Limiting lateral movement
4. **User Education**: Recognizing and avoiding threats
5. **Incident Response**: Preparing for and responding to attacks

Remember: The best defense against ransomware is prevention, not cure.

---

**Disclaimer**: This code is provided for educational purposes only. The author assumes no responsibility for any misuse or damage caused by this software. Use at your own risk and only in authorized environments.
