# WannaCry Enhanced Educational Implementation

⚠️ **EDUCATIONAL PURPOSE ONLY - DO NOT USE FOR MALICIOUS ACTIVITIES** ⚠️

## 📚 Overview

This enhanced implementation of WannaCry is based on comprehensive analysis from multiple authoritative security research sources. It provides an accurate educational representation of the original WannaCry ransomware for cybersecurity research and learning purposes.

## 🔍 Research Sources

This implementation is based on analysis from:

- **Secureworks Counter Threat Unit** - Complete technical analysis
- **MalwareTech (<PERSON>)** - Kill switch discovery and analysis
- **US-CERT/CISA** - Official government technical reports
- **G<PERSON><PERSON>erse Engineering** - NSA tool-based analysis
- **Academic Research Papers** - Peer-reviewed studies
- **FBI Flash Reports** - Law enforcement analysis
- **NHS Incident Reports** - Real-world impact assessment

## 🎯 Enhanced Features

### Kill Switch Implementation
- **Original Domain**: `www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com`
- **Exclusion Mutex**: `Global\MsWinZonesCacheCounterMutexA`
- **DNS Resolution Check**: Authentic InternetOpenA/InternetOpenUrlA implementation
- **Multiple Domain Support**: Backup kill switch domains

### Encryption System
- **RSA-2048**: For AES key encryption (authentic key size)
- **AES-128**: For file content encryption (CBC mode)
- **Unique Keys**: Per-file AES key generation
- **Custom Header**: WannaCry file format with magic signature
- **176+ Extensions**: Complete target file type list

### Network Components
- **EternalBlue Exploit**: MS17-010 vulnerability simulation
- **DoublePulsar Detection**: Backdoor checking mechanism
- **SMB Propagation**: Local and random IP scanning
- **Tor C2 Infrastructure**: 5 authentic .onion addresses

### Bitcoin Integration
- **3 Addresses**: Original hardcoded Bitcoin wallets
- **Payment Tracking**: Ransom amount progression ($300 → $600)
- **Timeline**: 4-day increase, 7-day deletion threat

## 🛠️ Building the Enhanced Version

### Prerequisites
- Windows 10/11 (recommended)
- Visual Studio 2019+ or MinGW-w64
- CMake 3.16+
- Git

### Build Instructions

```bash
# Clone the repository
git clone <repository-url>
cd WannaCry-Functional

# Create build directory
mkdir build_enhanced
cd build_enhanced

# Configure with CMake (Enhanced version)
cmake .. -f ../CMakeLists_Enhanced.txt -DCMAKE_BUILD_TYPE=Debug

# Build the project
cmake --build . --config Debug

# Run the enhanced version
./bin/WannaCry_Enhanced --help
```

### Build Options

```bash
# Debug build (recommended for education)
cmake .. -DCMAKE_BUILD_TYPE=Debug

# Release build (use with extreme caution)
cmake .. -DCMAKE_BUILD_TYPE=Release

# With verbose output
cmake --build . --verbose
```

## 🚀 Usage Examples

### Interactive Mode (Recommended)
```bash
# Start interactive analysis menu
./WannaCry_Enhanced

# Or explicitly
./WannaCry_Enhanced --interactive
```

### Specific Analysis Components
```bash
# Test kill switch only
./WannaCry_Enhanced --killswitch

# Test encryption only (safe)
./WannaCry_Enhanced --encrypt

# Show technical information
./WannaCry_Enhanced --info

# Display help
./WannaCry_Enhanced --help
```

### Educational Menu Options

1. **Display Technical Information** - Show all technical details
2. **Show Target File Extensions** - List 176+ targeted file types
3. **Demonstrate Kill Switch** - Test kill switch mechanisms
4. **Demonstrate Encryption** - Safe encryption demonstration
5. **Show Network Propagation Info** - EternalBlue and SMB details
6. **Show Ransom Information** - Bitcoin addresses and timeline
7. **Show Mitigation Strategies** - Defense recommendations
8. **Run Complete Analysis** - Execute all demonstrations

## 🔒 Safety Features

### Built-in Protections
- **Educational Mode Only**: No actual malicious functionality
- **Kill Switch Active**: Always checks for kill switch first
- **Safe Encryption**: Only encrypts test files in demo mode
- **No Network Propagation**: SMB exploitation is simulated only
- **Verbose Logging**: All actions are clearly logged

### Safety Recommendations
- ⚠️ **Always run in isolated environments**
- ⚠️ **Never disable safety checks**
- ⚠️ **Use only for educational purposes**
- ⚠️ **Keep antivirus enabled**
- ⚠️ **Regular system backups**

## 📊 Technical Specifications

### Encryption Details
```
Algorithm: RSA-2048 + AES-128
Mode: CBC (Cipher Block Chaining)
Key Generation: Windows Crypto API
File Header: Custom WannaCry format
Magic Signature: "WANACRY!"
```

### Network Details
```
Exploit: EternalBlue (MS17-010)
Protocol: SMBv1
Target Port: 445
Propagation: Local subnet + random IPs
Backdoor: DoublePulsar detection
```

### Kill Switch Details
```
Domain: www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com
Mutex: Global\MsWinZonesCacheCounterMutexA
Method: DNS resolution check
API: InternetOpenA, InternetOpenUrlA
```

## 📈 Analysis Results

### File Extensions Targeted (176+ types)
- **Documents**: .doc, .docx, .pdf, .txt, .rtf, .odt...
- **Images**: .jpg, .png, .gif, .bmp, .tiff, .raw...
- **Audio**: .mp3, .wav, .flac, .aac, .ogg...
- **Video**: .mp4, .avi, .mkv, .mov, .wmv...
- **Archives**: .zip, .rar, .7z, .tar, .gz...
- **Databases**: .sql, .db, .mdb, .sqlite...
- **Development**: .c, .cpp, .java, .py, .js...

### Bitcoin Addresses
```
**********************************
**********************************
********************************** (hard-coded)
```

### Tor C2 Infrastructure
```
gx7ekbenv2riucmf.onion
57g7spgrzlojinas.onion
xxlvbrloxvriy2c5.onion
76jdd2ir2embyv47.onion
cwwnhwhlz52maqm7.onion
```

## 🛡️ Mitigation Strategies

### Immediate Actions
1. **Apply MS17-010 patches** - Critical security updates
2. **Disable SMBv1** - Remove vulnerable protocol
3. **Network segmentation** - Isolate critical systems
4. **Backup strategy** - Offline backup storage

### Long-term Defense
1. **Endpoint protection** - Advanced threat detection
2. **Email filtering** - Block malicious attachments
3. **User training** - Security awareness programs
4. **Incident response** - Prepared response procedures

## 📖 Educational Value

### Learning Objectives
- Understand ransomware operation mechanisms
- Analyze kill switch implementations
- Study encryption methodologies
- Examine network propagation techniques
- Learn mitigation strategies

### Research Applications
- Cybersecurity education
- Malware analysis training
- Incident response preparation
- Security tool development
- Academic research

## ⚖️ Legal and Ethical Considerations

### Educational Use Only
- This implementation is for educational purposes only
- Never use for malicious activities
- Respect all applicable laws and regulations
- Follow responsible disclosure practices

### Ethical Guidelines
- Use only in controlled environments
- Obtain proper authorization before testing
- Protect sensitive information
- Report vulnerabilities responsibly

## 🤝 Contributing

### Research Contributions
- Additional documentation sources
- Technical analysis improvements
- Educational content enhancements
- Safety feature additions

### Code Contributions
- Bug fixes and improvements
- Documentation updates
- Test case additions
- Performance optimizations

## 📞 Support and Resources

### Documentation
- `DOCUMENTATION_SOURCES.md` - Complete source list
- Inline code comments
- Technical analysis reports

### Community
- Educational cybersecurity forums
- Academic research groups
- Security conference presentations

## 📝 License

This educational implementation is provided for research and learning purposes. See LICENSE file for details.

---

**Remember: This tool is for educational purposes only. Always use responsibly and ethically!** 🎓🔒
