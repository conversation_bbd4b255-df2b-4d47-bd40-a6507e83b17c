/**
 * WannaCry Kill Switch Header
 * Based on Secureworks analysis and MalwareTech discovery
 * 
 * ⚠️ EDUCATIONAL PURPOSE ONLY - DO NOT USE FOR MALICIOUS ACTIVITIES ⚠️
 */

#ifndef WANNACRY_KILLSWITCH_H
#define WANNACRY_KILLSWITCH_H

#include <windows.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Kill Switch Function Declarations
 * Based on original WannaCry analysis from multiple sources
 */

// Main kill switch check function
BOOL WannaCry_CheckKillSwitch();

// Check if kill switch is currently triggered
BOOL WannaCry_IsKillSwitchTriggered();

// Create exclusion mutex to prevent multiple instances
BOOL WannaCry_CreateExclusionMutex();

// Cleanup kill switch resources
void WannaCry_CleanupKillSwitch();

// Enhanced kill switch check with detailed logging
BOOL CheckKillSwitchDetailed();

/**
 * Kill Switch Constants from Documentation
 */

// Original kill switch domain (from MalwareTech analysis)
#ifndef WANNACRY_KILL_SWITCH_DOMAIN
#define WANNACRY_KILL_SWITCH_DOMAIN "www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com"
#endif

// Exclusion mutex names (from Secureworks analysis)
#ifndef WANNACRY_MUTEX_EXCLUSION
#define WANNACRY_MUTEX_EXCLUSION "Global\\MsWinZonesCacheCounterMutexA"
#endif

#ifndef WANNACRY_MUTEX_EXCLUSION_W
#define WANNACRY_MUTEX_EXCLUSION_W "Global\\MsWinZonesCacheCounterMutexW"
#endif

/**
 * Kill Switch Implementation Details
 * 
 * The WannaCry kill switch mechanism works as follows:
 * 
 * 1. Domain Check:
 *    - Attempts to resolve www.iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com
 *    - If domain resolves, malware exits immediately
 *    - Discovered by Marcus Hutchins (MalwareTech)
 * 
 * 2. Exclusion Mutex:
 *    - Checks for Global\MsWinZonesCacheCounterMutexA
 *    - If mutex exists, malware exits
 *    - Prevents multiple instances
 * 
 * 3. Network Connectivity:
 *    - Uses InternetOpenA() and InternetOpenUrlA()
 *    - WinINet API for HTTP connectivity
 *    - No cache, no UI flags
 * 
 * Technical Implementation (from Ghidra analysis):
 * - Function calls: InternetOpenA, InternetOpenUrlA
 * - User-Agent: "Microsoft Internet Explorer"
 * - Flags: INTERNET_FLAG_NO_CACHE_WRITE | INTERNET_FLAG_NO_UI
 * - Error handling: GetLastError() for diagnostics
 */

/**
 * Kill Switch Status Codes
 */
#define KILLSWITCH_NOT_TRIGGERED    0x00000000
#define KILLSWITCH_DOMAIN_RESOLVED  0x00000001
#define KILLSWITCH_MUTEX_EXISTS     0x00000002
#define KILLSWITCH_NETWORK_ERROR    0x00000004
#define KILLSWITCH_UNKNOWN_ERROR    0x00000008

/**
 * Kill Switch Configuration
 */
typedef struct _KILLSWITCH_CONFIG {
    BOOL bCheckDomain;          // Enable domain checking
    BOOL bCheckMutex;           // Enable mutex checking
    BOOL bCreateMutex;          // Create exclusion mutex
    DWORD dwTimeoutMs;          // Network timeout in milliseconds
    DWORD dwRetryCount;         // Number of retry attempts
    char szUserAgent[256];      // HTTP User-Agent string
} KILLSWITCH_CONFIG, *PKILLSWITCH_CONFIG;

// Default kill switch configuration
extern KILLSWITCH_CONFIG g_KillSwitchConfig;

/**
 * Advanced Kill Switch Functions
 */

// Initialize kill switch with custom configuration
BOOL WannaCry_InitKillSwitch(PKILLSWITCH_CONFIG pConfig);

// Check specific domain
BOOL WannaCry_CheckDomain(const char* szDomain);

// Check specific mutex
BOOL WannaCry_CheckMutex(const char* szMutexName);

// Get last kill switch error
DWORD WannaCry_GetLastKillSwitchError();

// Set kill switch configuration
BOOL WannaCry_SetKillSwitchConfig(PKILLSWITCH_CONFIG pConfig);

// Get kill switch configuration
BOOL WannaCry_GetKillSwitchConfig(PKILLSWITCH_CONFIG pConfig);

/**
 * Kill Switch Logging Functions
 */

// Enable/disable kill switch logging
void WannaCry_SetKillSwitchLogging(BOOL bEnable);

// Log kill switch event
void WannaCry_LogKillSwitchEvent(const char* szEvent);

// Get kill switch log
const char* WannaCry_GetKillSwitchLog();

/**
 * Kill Switch Analysis Functions
 * For educational and research purposes
 */

// Analyze kill switch behavior
BOOL WannaCry_AnalyzeKillSwitch();

// Simulate kill switch scenarios
BOOL WannaCry_SimulateKillSwitch(DWORD dwScenario);

// Generate kill switch report
BOOL WannaCry_GenerateKillSwitchReport(const char* szOutputFile);

#ifdef __cplusplus
}
#endif

#endif // WANNACRY_KILLSWITCH_H
