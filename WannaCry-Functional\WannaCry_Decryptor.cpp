/*
 * WannaCry Decryptor - Standalone Decryption Tool
 * 
 * This tool can decrypt files encrypted by the WannaCry_Functional.exe
 * for educational and recovery purposes.
 * 
 * Usage: WannaCry_Decryptor.exe <encryption_key> [target_directory]
 */

#include <windows.h>
#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <filesystem>

namespace fs = std::filesystem;

const std::string RANSOM_EXTENSION = ".WNCRY";
const std::string RANSOM_NOTE_FILENAME = "@Please_Read_Me@.txt";

class WannaCryDecryptor {
private:
    std::string decryptionKey;
    int filesDecrypted;
    int filesSkipped;
    
public:
    WannaCryDecryptor(const std::string& key) : decryptionKey(key), filesDecrypted(0), filesSkipped(0) {}
    
    // Decrypt data using XOR (same as encryption for XOR)
    std::vector<char> decryptData(const std::vector<char>& encryptedData) {
        std::vector<char> decrypted = encryptedData;
        
        for (size_t i = 0; i < decrypted.size(); ++i) {
            decrypted[i] ^= decryptionKey[i % decryptionKey.size()];
        }
        
        return decrypted;
    }
    
    // Decrypt a single file
    bool decryptFile(const std::string& encryptedFilePath) {
        try {
            // Check if file has the ransom extension
            if (encryptedFilePath.find(RANSOM_EXTENSION) == std::string::npos) {
                filesSkipped++;
                return false;
            }
            
            std::cout << "[PROCESSING] " << encryptedFilePath << std::endl;
            
            // Read encrypted file
            std::ifstream file(encryptedFilePath, std::ios::binary);
            if (!file.is_open()) {
                std::cerr << "[ERROR] Cannot open file: " << encryptedFilePath << std::endl;
                return false;
            }
            
            std::vector<char> encryptedData((std::istreambuf_iterator<char>(file)),
                                          std::istreambuf_iterator<char>());
            file.close();
            
            // Decrypt data
            std::vector<char> decryptedData = decryptData(encryptedData);
            
            // Get original filename (remove ransom extension)
            std::string originalPath = encryptedFilePath.substr(0, 
                encryptedFilePath.length() - RANSOM_EXTENSION.length());
            
            // Write decrypted file
            std::ofstream decryptedFile(originalPath, std::ios::binary);
            if (!decryptedFile.is_open()) {
                std::cerr << "[ERROR] Cannot create decrypted file: " << originalPath << std::endl;
                return false;
            }
            
            decryptedFile.write(decryptedData.data(), decryptedData.size());
            decryptedFile.close();
            
            // Delete encrypted file
            if (DeleteFileA(encryptedFilePath.c_str())) {
                std::cout << "[DECRYPTED] " << originalPath << std::endl;
                filesDecrypted++;
                return true;
            } else {
                std::cerr << "[WARNING] Could not delete encrypted file: " << encryptedFilePath << std::endl;
                filesDecrypted++;
                return true;
            }
            
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to decrypt " << encryptedFilePath << ": " << e.what() << std::endl;
            return false;
        }
    }
    
    // Decrypt all files in directory
    void decryptDirectory(const std::string& dirPath) {
        std::cout << "[INFO] Scanning directory: " << dirPath << std::endl;
        
        try {
            for (const auto& entry : fs::recursive_directory_iterator(dirPath)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().string();
                    
                    // Skip ransom notes
                    if (filename.find(RANSOM_NOTE_FILENAME) != std::string::npos) {
                        continue;
                    }
                    
                    if (filename.find(RANSOM_EXTENSION) != std::string::npos) {
                        decryptFile(filename);
                    }
                }
            }
            
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to scan directory " << dirPath << ": " << e.what() << std::endl;
        }
    }
    
    // Remove ransom notes
    void removeRansomNotes(const std::string& dirPath) {
        std::cout << "[INFO] Removing ransom notes from: " << dirPath << std::endl;
        
        try {
            for (const auto& entry : fs::recursive_directory_iterator(dirPath)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    
                    if (filename == RANSOM_NOTE_FILENAME) {
                        if (DeleteFileA(entry.path().string().c_str())) {
                            std::cout << "[REMOVED] " << entry.path().string() << std::endl;
                        }
                    }
                }
            }
            
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to remove ransom notes from " << dirPath << ": " << e.what() << std::endl;
        }
    }
    
    // Get statistics
    void printStatistics() {
        std::cout << "\n=== DECRYPTION STATISTICS ===" << std::endl;
        std::cout << "Files decrypted: " << filesDecrypted << std::endl;
        std::cout << "Files skipped: " << filesSkipped << std::endl;
        std::cout << "=============================" << std::endl;
    }
};

void printUsage(const char* programName) {
    std::cout << "WannaCry Decryptor - File Recovery Tool\n";
    std::cout << "======================================\n\n";
    std::cout << "Usage:\n";
    std::cout << "  " << programName << " <encryption_key> [target_directory]\n\n";
    std::cout << "Parameters:\n";
    std::cout << "  encryption_key     The key used for encryption (required)\n";
    std::cout << "  target_directory   Directory to decrypt (optional, defaults to all drives)\n\n";
    std::cout << "Examples:\n";
    std::cout << "  " << programName << " \"mykey123\" C:\\Users\\<USER>\\WannaCry_Test\n";
    std::cout << "  " << programName << " \"mykey123\"\n\n";
    std::cout << "Note: This tool decrypts files with .WNCRY extension\n";
    std::cout << "      and removes ransom notes (@Please_Read_Me@.txt)\n\n";
}

int main(int argc, char* argv[]) {
    std::cout << "WannaCry Decryptor v1.0\n";
    std::cout << "Educational Recovery Tool\n";
    std::cout << "========================\n\n";
    
    // Check arguments
    if (argc < 2) {
        printUsage(argv[0]);
        return 1;
    }
    
    std::string decryptionKey = argv[1];
    std::string targetDirectory;
    
    if (argc >= 3) {
        targetDirectory = argv[2];
    }
    
    // Validate key
    if (decryptionKey.empty()) {
        std::cerr << "Error: Encryption key cannot be empty" << std::endl;
        return 1;
    }
    
    std::cout << "Decryption key: " << decryptionKey << std::endl;
    
    if (!targetDirectory.empty()) {
        std::cout << "Target directory: " << targetDirectory << std::endl;
    } else {
        std::cout << "Target: All available drives" << std::endl;
    }
    
    std::cout << "\nPress 'Y' to continue or any other key to exit: ";
    char confirm;
    std::cin >> confirm;
    if (confirm != 'Y' && confirm != 'y') {
        std::cout << "Decryption cancelled." << std::endl;
        return 0;
    }
    
    // Create decryptor
    WannaCryDecryptor decryptor(decryptionKey);
    
    try {
        if (!targetDirectory.empty()) {
            // Decrypt specific directory
            if (fs::exists(targetDirectory) && fs::is_directory(targetDirectory)) {
                decryptor.decryptDirectory(targetDirectory);
                decryptor.removeRansomNotes(targetDirectory);
            } else {
                std::cerr << "Error: Directory does not exist: " << targetDirectory << std::endl;
                return 1;
            }
        } else {
            // Decrypt all drives
            DWORD drives = GetLogicalDrives();
            for (int i = 0; i < 26; ++i) {
                if (drives & (1 << i)) {
                    char driveLetter = 'A' + i;
                    std::string drivePath = std::string(1, driveLetter) + ":\\";
                    
                    UINT driveType = GetDriveTypeA(drivePath.c_str());
                    if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                        std::cout << "\n[INFO] Processing drive " << drivePath << std::endl;
                        decryptor.decryptDirectory(drivePath);
                        decryptor.removeRansomNotes(drivePath);
                    }
                }
            }
        }
        
        decryptor.printStatistics();
        std::cout << "\nDecryption process completed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[FATAL ERROR] " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
