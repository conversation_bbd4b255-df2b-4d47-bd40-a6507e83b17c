/**
 * WannaCry Enhanced Main Entry Point
 * Based on comprehensive analysis from multiple security research sources
 * 
 * Sources:
 * - Secureworks Counter Threat Unit Analysis
 * - MalwareTech Kill Switch Discovery  
 * - G<PERSON><PERSON>erse Engineering Analysis
 * - US-CERT/CISA Technical Reports
 * - Academic Research Papers
 * 
 * ⚠️ EDUCATIONAL PURPOSE ONLY - DO NOT USE FOR MALICIOUS ACTIVITIES ⚠️
 */

#include "../include/wannacry_globals.h"
#include "../include/wannacry_killswitch.h"
#include <iostream>
#include <windows.h>
#include <string>
#include <vector>

// Function declarations for enhanced features
BOOL WannaCry_EncryptFile(const char* szInputFile, const char* szOutputFile);
BOOL WannaCry_IsFileEncrypted(const char* szFilePath);
BOOL WannaCry_GetFileInfo(const char* szFilePath, DWORD* pdwOriginalSize, DWORD* pdwEncryptedSize);

void DisplayWannaCryInfo() {
    std::cout << "\n========================================" << std::endl;
    std::cout << "WannaCry Enhanced Implementation" << std::endl;
    std::cout << "Based on Documentation Analysis" << std::endl;
    std::cout << "⚠️ EDUCATIONAL PURPOSE ONLY ⚠️" << std::endl;
    std::cout << "========================================" << std::endl;
    
    std::cout << "\n📚 Research Sources:" << std::endl;
    std::cout << "• Secureworks Counter Threat Unit" << std::endl;
    std::cout << "• MalwareTech (Marcus Hutchins)" << std::endl;
    std::cout << "• US-CERT/CISA Technical Analysis" << std::endl;
    std::cout << "• Ghidra Reverse Engineering" << std::endl;
    std::cout << "• Academic Research Papers" << std::endl;
    
    std::cout << "\n🔍 Technical Details:" << std::endl;
    std::cout << "• Kill Switch: " << WANNACRY_KILL_SWITCH_DOMAIN << std::endl;
    std::cout << "• Exclusion Mutex: " << WANNACRY_MUTEX_EXCLUSION << std::endl;
    std::cout << "• Encryption: RSA-2048 + AES-128" << std::endl;
    std::cout << "• Target Extensions: 176+ file types" << std::endl;
    
    std::cout << "\n💰 Bitcoin Addresses:" << std::endl;
    std::cout << "• " << WANNACRY_BITCOIN_ADDR1 << std::endl;
    std::cout << "• " << WANNACRY_BITCOIN_ADDR2 << std::endl;
    std::cout << "• " << WANNACRY_BITCOIN_ADDR3 << " (hard-coded)" << std::endl;
    
    std::cout << "\n🧅 Tor C2 Servers:" << std::endl;
    std::cout << "• " << WANNACRY_TOR_C2_1 << std::endl;
    std::cout << "• " << WANNACRY_TOR_C2_2 << std::endl;
    std::cout << "• " << WANNACRY_TOR_C2_3 << std::endl;
    std::cout << "• " << WANNACRY_TOR_C2_4 << std::endl;
    std::cout << "• " << WANNACRY_TOR_C2_5 << std::endl;
}

void DisplayTargetExtensions() {
    std::cout << "\n📁 Target File Extensions (176+ types):" << std::endl;
    std::cout << "Documents: .doc, .docx, .pdf, .txt, .rtf, .odt..." << std::endl;
    std::cout << "Images: .jpg, .png, .gif, .bmp, .tiff, .raw..." << std::endl;
    std::cout << "Audio: .mp3, .wav, .flac, .aac, .ogg..." << std::endl;
    std::cout << "Video: .mp4, .avi, .mkv, .mov, .wmv..." << std::endl;
    std::cout << "Archives: .zip, .rar, .7z, .tar, .gz..." << std::endl;
    std::cout << "Databases: .sql, .db, .mdb, .sqlite..." << std::endl;
    std::cout << "Development: .c, .cpp, .java, .py, .js..." << std::endl;
    std::cout << "And many more..." << std::endl;
}

void DemonstrateKillSwitch() {
    std::cout << "\n🛑 Kill Switch Demonstration:" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // Perform detailed kill switch check
    CheckKillSwitchDetailed();
}

void DemonstrateEncryption() {
    std::cout << "\n🔐 Encryption Demonstration:" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // Create a test file
    const char* testFile = "test_file.txt";
    const char* encryptedFile = "test_file.txt.WNCRY";
    
    std::cout << "[DEMO] Creating test file..." << std::endl;
    
    // Create test content
    std::ofstream file(testFile);
    if (file.is_open()) {
        file << "This is a test file for WannaCry encryption demonstration.\n";
        file << "This file will be encrypted using the WannaCry format.\n";
        file << "Educational purposes only!\n";
        file.close();
        
        std::cout << "[DEMO] Test file created: " << testFile << std::endl;
        
        // Encrypt the file
        std::cout << "[DEMO] Encrypting file..." << std::endl;
        if (WannaCry_EncryptFile(testFile, encryptedFile)) {
            std::cout << "[DEMO] File encrypted successfully!" << std::endl;
            
            // Check if file is encrypted
            if (WannaCry_IsFileEncrypted(encryptedFile)) {
                std::cout << "[DEMO] File verified as WannaCry encrypted" << std::endl;
                
                // Get file info
                DWORD originalSize, encryptedSize;
                if (WannaCry_GetFileInfo(encryptedFile, &originalSize, &encryptedSize)) {
                    std::cout << "[DEMO] Original size: " << originalSize << " bytes" << std::endl;
                    std::cout << "[DEMO] Encrypted size: " << encryptedSize << " bytes" << std::endl;
                }
            }
            
            // Cleanup
            DeleteFileA(encryptedFile);
        } else {
            std::cout << "[DEMO] Encryption failed!" << std::endl;
        }
        
        // Cleanup
        DeleteFileA(testFile);
    } else {
        std::cout << "[DEMO] Failed to create test file" << std::endl;
    }
}

void DisplayNetworkInfo() {
    std::cout << "\n🌐 Network Propagation Info:" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "• EternalBlue Exploit (MS17-010)" << std::endl;
    std::cout << "• DoublePulsar Backdoor Detection" << std::endl;
    std::cout << "• SMBv1 Protocol Exploitation" << std::endl;
    std::cout << "• Local Subnet Scanning" << std::endl;
    std::cout << "• Random IP Address Scanning" << std::endl;
    std::cout << "• Target Port: 445 (SMB)" << std::endl;
}

void DisplayRansomInfo() {
    std::cout << "\n💸 Ransom Information:" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "• Initial Ransom: $300 USD" << std::endl;
    std::cout << "• Increased Ransom: $600 USD (after 4 days)" << std::endl;
    std::cout << "• Payment Method: Bitcoin" << std::endl;
    std::cout << "• Deadline: 7 days (files deleted)" << std::endl;
    std::cout << "• Ransom Note: @WanaDecryptor@.exe" << std::endl;
}

void DisplayMitigationInfo() {
    std::cout << "\n🛡️ Mitigation Strategies:" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "• Apply MS17-010 security updates" << std::endl;
    std::cout << "• Disable SMBv1 protocol" << std::endl;
    std::cout << "• Network segmentation" << std::endl;
    std::cout << "• Regular backups (offline)" << std::endl;
    std::cout << "• Email filtering" << std::endl;
    std::cout << "• Endpoint protection" << std::endl;
    std::cout << "• User education" << std::endl;
}

void ShowInteractiveMenu() {
    int choice;
    
    do {
        std::cout << "\n🎯 WannaCry Analysis Menu:" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "1. Display Technical Information" << std::endl;
        std::cout << "2. Show Target File Extensions" << std::endl;
        std::cout << "3. Demonstrate Kill Switch" << std::endl;
        std::cout << "4. Demonstrate Encryption" << std::endl;
        std::cout << "5. Show Network Propagation Info" << std::endl;
        std::cout << "6. Show Ransom Information" << std::endl;
        std::cout << "7. Show Mitigation Strategies" << std::endl;
        std::cout << "8. Run Complete Analysis" << std::endl;
        std::cout << "0. Exit" << std::endl;
        std::cout << "\nEnter your choice: ";
        
        std::cin >> choice;
        
        switch (choice) {
            case 1:
                DisplayWannaCryInfo();
                break;
            case 2:
                DisplayTargetExtensions();
                break;
            case 3:
                DemonstrateKillSwitch();
                break;
            case 4:
                DemonstrateEncryption();
                break;
            case 5:
                DisplayNetworkInfo();
                break;
            case 6:
                DisplayRansomInfo();
                break;
            case 7:
                DisplayMitigationInfo();
                break;
            case 8:
                DisplayWannaCryInfo();
                DisplayTargetExtensions();
                DemonstrateKillSwitch();
                DemonstrateEncryption();
                DisplayNetworkInfo();
                DisplayRansomInfo();
                DisplayMitigationInfo();
                break;
            case 0:
                std::cout << "\nExiting WannaCry analysis..." << std::endl;
                break;
            default:
                std::cout << "\nInvalid choice! Please try again." << std::endl;
                break;
        }
        
        if (choice != 0) {
            std::cout << "\nPress Enter to continue...";
            std::cin.ignore();
            std::cin.get();
        }
        
    } while (choice != 0);
}

int main(int argc, char* argv[]) {
    // Initialize globals
    if (!InitializeGlobals()) {
        std::cerr << "Failed to initialize globals!" << std::endl;
        return 1;
    }
    
    // Display banner
    DisplayWannaCryInfo();
    
    // Check command line arguments
    if (argc > 1) {
        std::string arg = argv[1];
        
        if (arg == "--help" || arg == "-h") {
            std::cout << "\nUsage: " << argv[0] << " [options]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  --help, -h     Show this help" << std::endl;
            std::cout << "  --killswitch   Test kill switch only" << std::endl;
            std::cout << "  --encrypt      Test encryption only" << std::endl;
            std::cout << "  --info         Show technical info only" << std::endl;
            std::cout << "  --interactive  Interactive menu (default)" << std::endl;
            return 0;
        }
        else if (arg == "--killswitch") {
            DemonstrateKillSwitch();
            return 0;
        }
        else if (arg == "--encrypt") {
            DemonstrateEncryption();
            return 0;
        }
        else if (arg == "--info") {
            DisplayWannaCryInfo();
            DisplayTargetExtensions();
            DisplayNetworkInfo();
            DisplayRansomInfo();
            DisplayMitigationInfo();
            return 0;
        }
    }
    
    // Default: Interactive menu
    ShowInteractiveMenu();
    
    // Cleanup
    CleanupGlobals();
    
    std::cout << "\n✅ Analysis complete. Thank you for using WannaCry Educational Tool!" << std::endl;
    return 0;
}
