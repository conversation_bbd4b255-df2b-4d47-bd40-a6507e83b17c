# Analýza Conti Ransomware

## Úvod

Conti je sofistikovaný ransomware, který byl vyvinut rusky mluvící kybernetickou skupinou. Tento ransomware se stal známým díky své efektivitě a byl použit při mnoha významných kybernetických útocích. Tato analýza se zaměřuje na zdrojový kód Conti ransomwaru, který byl zveřejněn na GitHubu.

## Struktura repozitáře

Repozitář obsahuje následující hlavní složky:

1. **locker** - Hlavní komponenta ransomwaru, která provádí šifrování souborů
2. **decryptor** - Nástroj pro dešifrování souborů po zaplacení výkupného
3. **builder** - Nástroj pro sestavení ransomwaru
4. **chacha20** - Implementace šifrovacího algoritmu ChaCha20

## Technická analýza

### Šifrovací mechanismus

Conti používá kombinaci asymetrické a symetrické kryptografie:

1. **Symetrické šifrování** - Používá algoritmus ChaCha20 pro šifrování obsahu souborů
2. **Asymetrické šifrování** - Používá RSA pro šifrování klíčů ChaCha20

Proces šifrování funguje následovně:
- Pro každý soubor je vygenerován unikátní 32-bajtový klíč a 8-bajtový inicializační vektor (IV) pro ChaCha20
- Tyto hodnoty jsou zašifrovány pomocí veřejného RSA klíče
- Zašifrovaný klíč je připojen na konec souboru spolu s informacemi o režimu šifrování

### Režimy šifrování

Conti používá tři různé režimy šifrování v závislosti na typu a velikosti souboru:

1. **FULL_ENCRYPT (0x24)** - Kompletní zašifrování celého souboru
   - Používá se pro databázové soubory a malé soubory (< 1 MB)

2. **PARTLY_ENCRYPT (0x25)** - Částečné šifrování souboru
   - Pro virtuální stroje: šifruje 20% dat (3 části po 7% s mezerami)
   - Pro ostatní velké soubory: šifruje 50% dat (5 částí po 10%)

3. **HEADER_ENCRYPT (0x26)** - Šifrování pouze hlavičky souboru
   - Používá se pro soubory střední velikosti (1-5 MB)
   - Šifruje pouze prvních 1 MB dat

Tato strategie umožňuje rychlé šifrování i velmi velkých souborů, přičemž stále znemožňuje jejich použití.

### Síťové skenování a laterální pohyb

Conti obsahuje pokročilé funkce pro skenování sítě a laterální pohyb:

1. **Skenování sítě** - Identifikuje dostupné počítače v síti pomocí:
   - Skenování portů (zejména SMB port 445)
   - Enumerace podsítí
   - Asynchronní připojení pro efektivní skenování

2. **Enumerace sdílených složek** - Vyhledává síťové sdílené složky pomocí Windows API
   - Používá `NetShareEnum` pro získání seznamu sdílených složek
   - Ignoruje administrativní sdílené složky (ADMIN$)

3. **Šíření** - Šifruje soubory na všech dostupných síťových sdílených složkách

### Ochrana proti detekci a analýze

Conti implementuje několik technik pro vyhnutí se detekci:

1. **Obfuskace řetězců** - Používá makra jako `OBFW` a `OBFA` pro obfuskaci řetězců
2. **Anti-hooking** - Obsahuje mechanismy pro detekci a obcházení hooků v systémových API
3. **Odstranění stínových kopií** - Odstraňuje stínové kopie Windows pomocí WMI
4. **Ukončování procesů** - Ukončuje procesy, které by mohly blokovat šifrování souborů

### Dešifrovací nástroj

Dešifrovací komponenta umožňuje obnovení zašifrovaných souborů pomocí:

1. Dešifrování RSA klíče pomocí soukromého klíče (který má pouze útočník)
2. Extrakce ChaCha20 klíče a IV
3. Dešifrování obsahu souboru pomocí získaného klíče
4. Odstranění přípony a metadat přidaných ransomwarem

## Zajímavé funkce

### Selektivní šifrování

Conti je navržen tak, aby maximalizoval škody při minimalizaci času potřebného k šifrování:

- Databázové soubory jsou vždy plně šifrovány (identifikovány podle přípony)
- Soubory virtuálních strojů jsou částečně šifrovány (20%)
- Velké soubory jsou šifrovány částečně nebo jen v hlavičce

### Ukončování procesů blokujících soubory

Když Conti narazí na soubor, který je používán jiným procesem:

1. Používá Windows Restart Manager API k identifikaci procesu
2. Kontroluje, zda proces není na whitelistu
3. Násilně ukončí proces, aby mohl přistupovat k souboru

### Paralelní zpracování

Conti využívá více vláken pro maximální efektivitu:

1. Vytváří thread pool pro lokální šifrování
2. Vytváří thread pool pro síťové šifrování
3. Používá asynchronní I/O pro síťové skenování

## Závěr

Conti ransomware představuje sofistikovanou hrozbu s pokročilými funkcemi pro šifrování, síťové šíření a vyhýbání se detekci. Jeho modulární architektura, efektivní šifrovací strategie a schopnost laterálního pohybu z něj činí mimořádně nebezpečný malware.

Klíčové vlastnosti, které ho odlišují od jiných ransomwarů:

1. Selektivní šifrování založené na typu a velikosti souboru
2. Efektivní síťové skenování a laterální pohyb
3. Robustní kryptografické implementace
4. Agresivní ukončování procesů pro přístup k zamčeným souborům

Tato analýza poskytuje pohled na technické aspekty Conti ransomwaru, což může pomoci při vývoji lepších obranných strategií a detekčních mechanismů.

## Doporučení pro ochranu

1. Pravidelné zálohování dat na offline médium
2. Segmentace sítě pro omezení laterálního pohybu
3. Implementace detekce anomálií pro identifikaci masového šifrování
4. Aktualizace systémů a aplikací
5. Školení uživatelů o phishingových útocích, které jsou často vstupním bodem ransomwaru
